<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="yysls_ranking@39.105.54.213">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.49">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <ServerVersion>8.0.43</ServerVersion>
    </root>
    <schema id="2" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="3" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="4" parent="1" name="yysls_ranking">
      <Current>1</Current>
      <IntrospectionTimestamp>2025-08-05.00:58:00</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2025-08-04.07:04:12</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <table id="5" parent="4" name="alembic_version">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="6" parent="4" name="broadcast_messages">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="7" parent="4" name="contents">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="8" parent="4" name="feedback">
      <Comment>用户反馈表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="9" parent="4" name="ranking_details">
      <Comment>榜单明细表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="10" parent="4" name="rankings">
      <Comment>榜单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="11" parent="4" name="sponsors">
      <Comment>赞助商表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="12" parent="4" name="system_configs">
      <Comment>系统配置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="13" parent="4" name="users">
      <Comment>用户表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <column id="14" parent="5" name="version_num">
      <DasType>varchar(320)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <index id="15" parent="5" name="PRIMARY">
      <ColNames>version_num</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="16" parent="5" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="17" parent="6" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="18" parent="6" name="message">
      <Comment>播报消息</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="19" parent="6" name="message_type">
      <Comment>消息类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;info&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="20" parent="6" name="is_active">
      <Comment>是否启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="21" parent="6" name="start_time">
      <Comment>开始时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="22" parent="6" name="end_time">
      <Comment>结束时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="23" parent="6" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="24" parent="6" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <column id="25" parent="6" name="display_duration">
      <Comment>显示时长(秒)</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="26" parent="6" name="display_order">
      <Comment>显示顺序</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="27" parent="6" name="created_by">
      <Comment>创建人ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <foreign-key id="28" parent="6" name="broadcast_messages_ibfk_1">
      <ColNames>created_by</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>users</RefTableName>
    </foreign-key>
    <index id="29" parent="6" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="30" parent="6" name="ix_broadcast_messages_id">
      <ColNames>id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="31" parent="6" name="created_by">
      <ColNames>created_by</ColNames>
      <Type>btree</Type>
    </index>
    <key id="32" parent="6" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="33" parent="7" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="34" parent="7" name="content_type">
      <Comment>内容类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="35" parent="7" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="36" parent="7" name="content">
      <Comment>内容正文</Comment>
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="37" parent="7" name="is_published">
      <Comment>发布状态</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="38" parent="7" name="publish_at">
      <Comment>发布时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="39" parent="7" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="40" parent="7" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="41" parent="7" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="42" parent="7" name="idx_content_type">
      <ColNames>content_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="43" parent="7" name="idx_is_published">
      <ColNames>is_published</ColNames>
      <Type>btree</Type>
    </index>
    <index id="44" parent="7" name="idx_publish_at">
      <ColNames>publish_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="45" parent="7" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="46" parent="7" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="47" parent="8" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>反馈ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="48" parent="8" name="content">
      <Comment>反馈内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="49" parent="8" name="user_id">
      <Comment>用户ID，可为空支持匿名反馈</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="50" parent="8" name="status">
      <Comment>反馈状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="51" parent="8" name="admin_reply">
      <Comment>管理员回复</Comment>
      <DasType>text|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="52" parent="8" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="53" parent="8" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <foreign-key id="54" parent="8" name="feedback_ibfk_1">
      <ColNames>user_id</ColNames>
      <OnDelete>set-null</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>users</RefTableName>
    </foreign-key>
    <index id="55" parent="8" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="56" parent="8" name="ix_feedback_user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="57" parent="8" name="ix_feedback_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="58" parent="8" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="59" parent="9" name="id">
      <AutoIncrement>11</AutoIncrement>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="60" parent="9" name="ranking_id">
      <Comment>榜单ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="61" parent="9" name="completion_seconds">
      <Comment>完成时间(总秒数)</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="62" parent="9" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="63" parent="9" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>5</Position>
    </column>
    <column id="64" parent="9" name="team_name">
      <Comment>队伍名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="65" parent="9" name="rank_start">
      <Comment>排名开始</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="66" parent="9" name="rank_end">
      <Comment>排名结束</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="67" parent="9" name="completion_time">
      <Comment>完成时间(分秒)</Comment>
      <DasType>time|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="68" parent="9" name="participant_count">
      <Comment>当前时间区间参与人数</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="69" parent="9" name="team_info">
      <Comment>队伍信息(JSON格式)</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <foreign-key id="70" parent="9" name="ranking_details_ibfk_1">
      <ColNames>ranking_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>rankings</RefTableName>
    </foreign-key>
    <index id="71" parent="9" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="72" parent="9" name="idx_ranking_id">
      <ColNames>ranking_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="73" parent="9" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="74" parent="10" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>榜单ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="75" parent="10" name="name">
      <Comment>榜单标题</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="76" parent="10" name="description">
      <Comment>榜单描述</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="77" parent="10" name="ranking_type">
      <Comment>榜单类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="78" parent="10" name="status">
      <Comment>榜单状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;draft&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="79" parent="10" name="team_size_limit">
      <Comment>组队人数限制</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="80" parent="10" name="total_participants">
      <Comment>当前参与人数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="81" parent="10" name="start_time">
      <Comment>开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="82" parent="10" name="end_time">
      <Comment>结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="83" parent="10" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="84" parent="10" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
    </column>
    <column id="85" parent="10" name="period">
      <DasType>tinyint|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="86" parent="10" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="87" parent="10" name="idx_ranking_type">
      <ColNames>ranking_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="88" parent="10" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="89" parent="10" name="idx_start_time">
      <ColNames>start_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="90" parent="10" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="91" parent="10" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="92" parent="11" name="id">
      <AutoIncrement>2</AutoIncrement>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="93" parent="11" name="name">
      <Comment>赞助商名称</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="94" parent="11" name="logo_url">
      <Comment>Logo URL（用作头像）</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="95" parent="11" name="sort_order">
      <Comment>排序顺序</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="96" parent="11" name="is_active">
      <Comment>是否启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="97" parent="11" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="98" parent="11" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="99" parent="11" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="100" parent="11" name="idx_sponsors_new_sort_order">
      <ColNames>sort_order</ColNames>
      <Type>btree</Type>
    </index>
    <index id="101" parent="11" name="idx_sponsors_new_is_active">
      <ColNames>is_active</ColNames>
      <Type>btree</Type>
    </index>
    <index id="102" parent="11" name="idx_sponsors_new_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="103" parent="11" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="104" parent="12" name="id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>配置ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="105" parent="12" name="config_key">
      <Comment>配置键</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="106" parent="12" name="config_value">
      <Comment>配置值</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="107" parent="12" name="config_type">
      <Comment>配置类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;string&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="108" parent="12" name="description">
      <Comment>配置描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="109" parent="12" name="is_public">
      <Comment>是否公开（前端可访问）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="110" parent="12" name="name">
      <DasType>varchar(30)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="111" parent="12" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="112" parent="12" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>9</Position>
    </column>
    <index id="113" parent="12" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="114" parent="12" name="config_key">
      <ColNames>config_key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="115" parent="12" name="idx_config_key">
      <ColNames>config_key</ColNames>
      <Type>btree</Type>
    </index>
    <index id="116" parent="12" name="idx_config_type">
      <ColNames>config_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="117" parent="12" name="idx_is_public">
      <ColNames>is_public</ColNames>
      <Type>btree</Type>
    </index>
    <key id="118" parent="12" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="119" parent="12" name="config_key">
      <UnderlyingIndexName>config_key</UnderlyingIndexName>
    </key>
    <check id="120" parent="13" name="check_age_range">
      <Predicate>(`age` is null) or ((`age` &gt;= 0) and (`age` &lt;= 150))</Predicate>
    </check>
    <column id="121" parent="13" name="id">
      <AutoIncrement>4</AutoIncrement>
      <Comment>用户ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="122" parent="13" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="123" parent="13" name="password_hash">
      <Comment>密码哈希</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="124" parent="13" name="nickname">
      <Comment>昵称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="125" parent="13" name="avatar_url">
      <Comment>头像URL</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="126" parent="13" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="127" parent="13" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="128" parent="13" name="wechat_openid">
      <Comment>微信OpenID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="129" parent="13" name="wechat_unionid">
      <Comment>微信UnionID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="130" parent="13" name="role">
      <Comment>用户角色</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;user&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="131" parent="13" name="is_active">
      <Comment>是否激活</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="132" parent="13" name="is_verified">
      <Comment>是否验证</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="133" parent="13" name="bio">
      <Comment>个人简介</Comment>
      <DasType>text|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="134" parent="13" name="level">
      <Comment>用户等级</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;江湖新人&apos;</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="135" parent="13" name="points">
      <Comment>积分</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="136" parent="13" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="137" parent="13" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>17</Position>
    </column>
    <column id="138" parent="13" name="last_login_at">
      <Comment>最后登录时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="139" parent="13" name="location">
      <Comment>所在地</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="140" parent="13" name="user_number">
      <Comment>用户编号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="141" parent="13" name="gender">
      <Comment>性别</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="142" parent="13" name="age">
      <Comment>年龄</Comment>
      <DasType>int|0s</DasType>
      <Position>22</Position>
    </column>
    <index id="143" parent="13" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="144" parent="13" name="username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="145" parent="13" name="email">
      <ColNames>email</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="146" parent="13" name="phone">
      <ColNames>phone</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="147" parent="13" name="wechat_openid">
      <ColNames>wechat_openid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="148" parent="13" name="wechat_unionid">
      <ColNames>wechat_unionid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="149" parent="13" name="idx_users_user_number">
      <ColNames>user_number</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="150" parent="13" name="idx_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
    </index>
    <index id="151" parent="13" name="idx_email">
      <ColNames>email</ColNames>
      <Type>btree</Type>
    </index>
    <index id="152" parent="13" name="idx_wechat_openid">
      <ColNames>wechat_openid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="153" parent="13" name="idx_role">
      <ColNames>role</ColNames>
      <Type>btree</Type>
    </index>
    <index id="154" parent="13" name="idx_is_active">
      <ColNames>is_active</ColNames>
      <Type>btree</Type>
    </index>
    <index id="155" parent="13" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="156" parent="13" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="157" parent="13" name="username">
      <UnderlyingIndexName>username</UnderlyingIndexName>
    </key>
    <key id="158" parent="13" name="email">
      <UnderlyingIndexName>email</UnderlyingIndexName>
    </key>
    <key id="159" parent="13" name="phone">
      <UnderlyingIndexName>phone</UnderlyingIndexName>
    </key>
    <key id="160" parent="13" name="wechat_openid">
      <UnderlyingIndexName>wechat_openid</UnderlyingIndexName>
    </key>
    <key id="161" parent="13" name="wechat_unionid">
      <UnderlyingIndexName>wechat_unionid</UnderlyingIndexName>
    </key>
    <key id="162" parent="13" name="idx_users_user_number">
      <UnderlyingIndexName>idx_users_user_number</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>