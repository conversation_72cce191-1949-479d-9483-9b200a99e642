# 飞书合同数据清洗表设计方案

## 1. 项目概述

### 1.1 背景
基于现有的飞书合同数据表（`ins_contract`），设计数据清洗表来处理和标准化合同数据。当合同包含明细数据时，需要将一条合同记录拆分为多条明细记录进行处理。

### 1.2 核心需求
- 基于现有 `ins_contract` 表进行数据清洗
- 支持明细数据的拆分处理（一对多）
- 提供数据清洗状态管理
- 支持清洗规则配置和验证
- 提供清洗结果的审核机制

## 2. 数据库表结构设计

### 2.1 主表：ins_contract_cleaned（合同清洗数据表）

```sql
CREATE TABLE `ins_contract_cleaned` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建人',
  `updated_by` bigint unsigned DEFAULT NULL COMMENT '更新人',
  
  -- 源数据关联
  `source_contract_id` bigint unsigned NOT NULL COMMENT '源合同ID',
  `source_instance_code` varchar(100) NOT NULL COMMENT '源实例代码',
  `detail_index` int NOT NULL DEFAULT 0 COMMENT '明细索引（0表示主记录，>0表示明细记录）',
  `detail_total` int NOT NULL DEFAULT 1 COMMENT '明细总数',
  
  -- 清洗状态
  `cleaning_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '清洗状态：pending-待清洗，processing-清洗中，completed-已完成，failed-失败，verified-已验证',
  `cleaning_time` datetime DEFAULT NULL COMMENT '清洗时间',
  `cleaning_rule_version` varchar(20) DEFAULT '1.0' COMMENT '清洗规则版本',
  `verification_status` varchar(20) DEFAULT 'unverified' COMMENT '验证状态：unverified-未验证，passed-验证通过，rejected-验证驳回',
  `verifier_id` bigint unsigned DEFAULT NULL COMMENT '验证人ID',
  `verification_time` datetime DEFAULT NULL COMMENT '验证时间',
  `verification_note` text DEFAULT NULL COMMENT '验证备注',
  
  -- 清洗后的标准化字段
  `application_number` varchar(100) DEFAULT NULL COMMENT '申请编号',
  `title` varchar(500) DEFAULT NULL COMMENT '标题',
  `application_status` varchar(50) DEFAULT NULL COMMENT '申请状态',
  `initiate_time` datetime DEFAULT NULL COMMENT '发起时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  
  -- 人员信息（清洗后）
  `initiator_employee_id` varchar(50) DEFAULT NULL COMMENT '发起人工号',
  `initiator_user_id` varchar(100) DEFAULT NULL COMMENT '发起人用户ID',
  `initiator_name` varchar(100) DEFAULT NULL COMMENT '发起人姓名',
  `initiator_department_id` varchar(100) DEFAULT NULL COMMENT '发起人部门ID',
  `initiator_department_name` varchar(200) DEFAULT NULL COMMENT '发起人部门名称',
  
  -- 业务信息（清洗后）
  `business_type` varchar(100) DEFAULT NULL COMMENT '业务类型',
  `payment_reason` varchar(500) DEFAULT NULL COMMENT '付款事由',
  `payment_entity` varchar(200) DEFAULT NULL COMMENT '付款主体',
  `reimbursement_reason` varchar(500) DEFAULT NULL COMMENT '报销事由',
  `reimbursement_entity` varchar(200) DEFAULT NULL COMMENT '报销主体',
  `expense_type` varchar(100) DEFAULT NULL COMMENT '费用类型（明细字段）',
  `expense_location` varchar(200) DEFAULT NULL COMMENT '费用地点（明细字段）',
  `expense_date_range` varchar(100) DEFAULT NULL COMMENT '费用日期区间（明细字段）',
  
  -- 金额信息（清洗后）
  `amount` decimal(16,2) DEFAULT 0.00 COMMENT '金额（主记录为总金额，明细记录为明细金额）',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '币种',
  `tax_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '税率',
  `tax_amount` decimal(16,2) DEFAULT 0.00 COMMENT '税额',
  `amount_excluding_tax` decimal(16,2) DEFAULT 0.00 COMMENT '不含税金额',
  `invoice_type` varchar(50) DEFAULT NULL COMMENT '发票类型',
  
  -- 银行信息（清洗后）
  `account_holder` varchar(200) DEFAULT NULL COMMENT '收款方户名',
  `account_number` varchar(100) DEFAULT NULL COMMENT '账户号码',
  `account_type` varchar(50) DEFAULT NULL COMMENT '账户类型',
  `bank_name` varchar(200) DEFAULT NULL COMMENT '银行名称',
  `bank_branch` varchar(200) DEFAULT NULL COMMENT '银行支行',
  `bank_region` varchar(100) DEFAULT NULL COMMENT '银行地区',
  
  -- 时间信息（清洗后）
  `expected_payment_date` datetime DEFAULT NULL COMMENT '期望付款日期',
  `expense_start_date` datetime DEFAULT NULL COMMENT '费用开始日期（明细字段）',
  `expense_end_date` datetime DEFAULT NULL COMMENT '费用结束日期（明细字段）',
  
  -- 其他信息
  `remarks` text DEFAULT NULL COMMENT '备注',
  `attachment_count` int DEFAULT 0 COMMENT '附件数量',
  `original_form_data` json DEFAULT NULL COMMENT '原始表单数据',
  
  -- 清洗质量评分
  `data_quality_score` decimal(3,2) DEFAULT 0.00 COMMENT '数据质量评分（0-1）',
  `completeness_score` decimal(3,2) DEFAULT 0.00 COMMENT '完整性评分',
  `accuracy_score` decimal(3,2) DEFAULT 0.00 COMMENT '准确性评分',
  `consistency_score` decimal(3,2) DEFAULT 0.00 COMMENT '一致性评分',
  
  -- 错误信息
  `cleaning_errors` json DEFAULT NULL COMMENT '清洗错误信息',
  `validation_errors` json DEFAULT NULL COMMENT '验证错误信息',
  `warning_messages` json DEFAULT NULL COMMENT '警告信息',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_source_detail` (`source_contract_id`, `detail_index`),
  KEY `idx_source_instance` (`source_instance_code`),
  KEY `idx_cleaning_status` (`cleaning_status`),
  KEY `idx_verification_status` (`verification_status`),
  KEY `idx_cleaning_time` (`cleaning_time`),
  KEY `idx_initiator_user_id` (`initiator_user_id`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_amount` (`amount`),
  CONSTRAINT `fk_source_contract` FOREIGN KEY (`source_contract_id`) REFERENCES `ins_contract` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同清洗数据表';
```

### 2.2 清洗规则表：ins_contract_cleaning_rule（清洗规则配置表）

```sql
CREATE TABLE `ins_contract_cleaning_rule` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_version` varchar(20) NOT NULL DEFAULT '1.0' COMMENT '规则版本',
  `approval_code` varchar(100) NOT NULL COMMENT '适用的审批代码',
  `approval_name` varchar(200) NOT NULL COMMENT '审批名称',
  `rule_type` varchar(50) NOT NULL COMMENT '规则类型：field_mapping-字段映射，data_validation-数据验证，detail_split-明细拆分',
  `rule_config` json NOT NULL COMMENT '规则配置（JSON格式）',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `priority` int NOT NULL DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
  `description` text DEFAULT NULL COMMENT '规则描述',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_version` (`rule_name`, `rule_version`),
  KEY `idx_approval_code` (`approval_code`),
  KEY `idx_rule_type` (`rule_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同清洗规则配置表';
```

### 2.3 清洗日志表：ins_contract_cleaning_log（清洗处理日志表）

```sql
CREATE TABLE `ins_contract_cleaning_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  
  `batch_no` varchar(50) NOT NULL COMMENT '批次号',
  `source_contract_id` bigint unsigned NOT NULL COMMENT '源合同ID',
  `cleaned_record_id` bigint unsigned DEFAULT NULL COMMENT '清洗记录ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型：clean-清洗，verify-验证，split-拆分',
  `operation_status` varchar(20) NOT NULL COMMENT '操作状态：success-成功，failed-失败，warning-警告',
  `processing_time_ms` int DEFAULT 0 COMMENT '处理耗时（毫秒）',
  `rule_applied` varchar(100) DEFAULT NULL COMMENT '应用的规则名称',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `warning_message` text DEFAULT NULL COMMENT '警告信息',
  `input_data` json DEFAULT NULL COMMENT '输入数据',
  `output_data` json DEFAULT NULL COMMENT '输出数据',
  `operator_id` bigint unsigned DEFAULT NULL COMMENT '操作人ID',
  
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_source_contract` (`source_contract_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_status` (`operation_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同清洗处理日志表';
```

### 2.4 清洗批次表：ins_contract_cleaning_batch（批量清洗记录表）

```sql
CREATE TABLE `ins_contract_cleaning_batch` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  
  `batch_no` varchar(50) NOT NULL COMMENT '批次号',
  `batch_name` varchar(200) DEFAULT NULL COMMENT '批次名称',
  `approval_code` varchar(100) DEFAULT NULL COMMENT '审批代码（为空表示全部）',
  `total_count` int NOT NULL DEFAULT 0 COMMENT '总记录数',
  `processed_count` int NOT NULL DEFAULT 0 COMMENT '已处理记录数',
  `success_count` int NOT NULL DEFAULT 0 COMMENT '成功记录数',
  `failed_count` int NOT NULL DEFAULT 0 COMMENT '失败记录数',
  `warning_count` int NOT NULL DEFAULT 0 COMMENT '警告记录数',
  `detail_records_created` int NOT NULL DEFAULT 0 COMMENT '创建的明细记录数',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '批次状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `operator_id` bigint unsigned NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `rule_version` varchar(20) DEFAULT '1.0' COMMENT '使用的规则版本',
  `config_params` json DEFAULT NULL COMMENT '配置参数',
  `summary_report` json DEFAULT NULL COMMENT '汇总报告',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_approval_code` (`approval_code`),
  KEY `idx_status` (`status`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量清洗记录表';
```

## 3. Go模型定义

### 3.1 主模型：InsContractCleaned

```go
// server/model/insbuy/ins_contract_cleaned.go
package insbuy

import (
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// InsContractCleaned 合同清洗数据表
type InsContractCleaned struct {
	global.GVA_MODEL

	// 源数据关联
	SourceContractId   uint   `gorm:"not null;index;comment:源合同ID" json:"source_contract_id"`
	SourceInstanceCode string `gorm:"type:varchar(100);not null;index;comment:源实例代码" json:"source_instance_code"`
	DetailIndex        int    `gorm:"not null;default:0;comment:明细索引" json:"detail_index"`
	DetailTotal        int    `gorm:"not null;default:1;comment:明细总数" json:"detail_total"`

	// 清洗状态
	CleaningStatus      string     `gorm:"type:varchar(20);not null;default:'pending';index;comment:清洗状态" json:"cleaning_status"`
	CleaningTime        *time.Time `gorm:"type:datetime;index;comment:清洗时间" json:"cleaning_time"`
	CleaningRuleVersion string     `gorm:"type:varchar(20);default:'1.0';comment:清洗规则版本" json:"cleaning_rule_version"`
	VerificationStatus  string     `gorm:"type:varchar(20);default:'unverified';index;comment:验证状态" json:"verification_status"`
	VerifierId          *uint      `gorm:"comment:验证人ID" json:"verifier_id"`
	VerificationTime    *time.Time `gorm:"type:datetime;comment:验证时间" json:"verification_time"`
	VerificationNote    string     `gorm:"type:text;comment:验证备注" json:"verification_note"`

	// 清洗后的标准化字段
	ApplicationNumber string     `gorm:"type:varchar(100);comment:申请编号" json:"application_number"`
	Title             string     `gorm:"type:varchar(500);comment:标题" json:"title"`
	ApplicationStatus string     `gorm:"type:varchar(50);comment:申请状态" json:"application_status"`
	InitiateTime      *time.Time `gorm:"type:datetime;comment:发起时间" json:"initiate_time"`
	CompleteTime      *time.Time `gorm:"type:datetime;comment:完成时间" json:"complete_time"`

	// 人员信息（清洗后）
	InitiatorEmployeeId     string `gorm:"type:varchar(50);comment:发起人工号" json:"initiator_employee_id"`
	InitiatorUserId         string `gorm:"type:varchar(100);index;comment:发起人用户ID" json:"initiator_user_id"`
	InitiatorName           string `gorm:"type:varchar(100);comment:发起人姓名" json:"initiator_name"`
	InitiatorDepartmentId   string `gorm:"type:varchar(100);comment:发起人部门ID" json:"initiator_department_id"`
	InitiatorDepartmentName string `gorm:"type:varchar(200);comment:发起人部门名称" json:"initiator_department_name"`

	// 业务信息（清洗后）
	BusinessType         string `gorm:"type:varchar(100);index;comment:业务类型" json:"business_type"`
	PaymentReason        string `gorm:"type:varchar(500);comment:付款事由" json:"payment_reason"`
	PaymentEntity        string `gorm:"type:varchar(200);comment:付款主体" json:"payment_entity"`
	ReimbursementReason  string `gorm:"type:varchar(500);comment:报销事由" json:"reimbursement_reason"`
	ReimbursementEntity  string `gorm:"type:varchar(200);comment:报销主体" json:"reimbursement_entity"`
	ExpenseType          string `gorm:"type:varchar(100);comment:费用类型" json:"expense_type"`
	ExpenseLocation      string `gorm:"type:varchar(200);comment:费用地点" json:"expense_location"`
	ExpenseDateRange     string `gorm:"type:varchar(100);comment:费用日期区间" json:"expense_date_range"`

	// 金额信息（清洗后）
	Amount             float64 `gorm:"type:decimal(16,2);default:0.00;index;comment:金额" json:"amount"`
	Currency           string  `gorm:"type:varchar(10);default:'CNY';comment:币种" json:"currency"`
	TaxRate            float64 `gorm:"type:decimal(5,4);default:0.0000;comment:税率" json:"tax_rate"`
	TaxAmount          float64 `gorm:"type:decimal(16,2);default:0.00;comment:税额" json:"tax_amount"`
	AmountExcludingTax float64 `gorm:"type:decimal(16,2);default:0.00;comment:不含税金额" json:"amount_excluding_tax"`
	InvoiceType        string  `gorm:"type:varchar(50);comment:发票类型" json:"invoice_type"`

	// 银行信息（清洗后）
	AccountHolder string `gorm:"type:varchar(200);comment:收款方户名" json:"account_holder"`
	AccountNumber string `gorm:"type:varchar(100);comment:账户号码" json:"account_number"`
	AccountType   string `gorm:"type:varchar(50);comment:账户类型" json:"account_type"`
	BankName      string `gorm:"type:varchar(200);comment:银行名称" json:"bank_name"`
	BankBranch    string `gorm:"type:varchar(200);comment:银行支行" json:"bank_branch"`
	BankRegion    string `gorm:"type:varchar(100);comment:银行地区" json:"bank_region"`

	// 时间信息（清洗后）
	ExpectedPaymentDate *time.Time `gorm:"type:datetime;comment:期望付款日期" json:"expected_payment_date"`
	ExpenseStartDate    *time.Time `gorm:"type:datetime;comment:费用开始日期" json:"expense_start_date"`
	ExpenseEndDate      *time.Time `gorm:"type:datetime;comment:费用结束日期" json:"expense_end_date"`

	// 其他信息
	Remarks          string          `gorm:"type:text;comment:备注" json:"remarks"`
	AttachmentCount  int             `gorm:"default:0;comment:附件数量" json:"attachment_count"`
	OriginalFormData datatypes.JSON  `gorm:"type:json;comment:原始表单数据" json:"original_form_data"`

	// 清洗质量评分
	DataQualityScore  float64 `gorm:"type:decimal(3,2);default:0.00;comment:数据质量评分" json:"data_quality_score"`
	CompletenessScore float64 `gorm:"type:decimal(3,2);default:0.00;comment:完整性评分" json:"completeness_score"`
	AccuracyScore     float64 `gorm:"type:decimal(3,2);default:0.00;comment:准确性评分" json:"accuracy_score"`
	ConsistencyScore  float64 `gorm:"type:decimal(3,2);default:0.00;comment:一致性评分" json:"consistency_score"`

	// 错误信息
	CleaningErrors    datatypes.JSON `gorm:"type:json;comment:清洗错误信息" json:"cleaning_errors"`
	ValidationErrors  datatypes.JSON `gorm:"type:json;comment:验证错误信息" json:"validation_errors"`
	WarningMessages   datatypes.JSON `gorm:"type:json;comment:警告信息" json:"warning_messages"`

	// 关联关系
	SourceContract *InsContract `gorm:"foreignKey:SourceContractId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"source_contract"`
}

func (InsContractCleaned) TableName() string {
	return "ins_contract_cleaned"
}

func (InsContractCleaned) TableComment() string {
	return "合同清洗数据表"
}

// InsContractCleaningRule 清洗规则配置表
type InsContractCleaningRule struct {
	global.GVA_MODEL

	RuleName     string          `gorm:"type:varchar(100);not null;comment:规则名称" json:"rule_name"`
	RuleVersion  string          `gorm:"type:varchar(20);not null;default:'1.0';comment:规则版本" json:"rule_version"`
	ApprovalCode string          `gorm:"type:varchar(100);not null;index;comment:适用的审批代码" json:"approval_code"`
	ApprovalName string          `gorm:"type:varchar(200);not null;comment:审批名称" json:"approval_name"`
	RuleType     string          `gorm:"type:varchar(50);not null;index;comment:规则类型" json:"rule_type"`
	RuleConfig   datatypes.JSON  `gorm:"type:json;not null;comment:规则配置" json:"rule_config"`
	IsActive     bool            `gorm:"type:tinyint(1);not null;default:1;index;comment:是否启用" json:"is_active"`
	Priority     int             `gorm:"not null;default:0;comment:优先级" json:"priority"`
	Description  string          `gorm:"type:text;comment:规则描述" json:"description"`
}

func (InsContractCleaningRule) TableName() string {
	return "ins_contract_cleaning_rule"
}

// InsContractCleaningLog 清洗处理日志表
type InsContractCleaningLog struct {
	global.GVA_MODEL

	BatchNo           string          `gorm:"type:varchar(50);not null;index;comment:批次号" json:"batch_no"`
	SourceContractId  uint            `gorm:"not null;index;comment:源合同ID" json:"source_contract_id"`
	CleanedRecordId   *uint           `gorm:"comment:清洗记录ID" json:"cleaned_record_id"`
	OperationType     string          `gorm:"type:varchar(50);not null;index;comment:操作类型" json:"operation_type"`
	OperationStatus   string          `gorm:"type:varchar(20);not null;index;comment:操作状态" json:"operation_status"`
	ProcessingTimeMs  int             `gorm:"default:0;comment:处理耗时" json:"processing_time_ms"`
	RuleApplied       string          `gorm:"type:varchar(100);comment:应用的规则名称" json:"rule_applied"`
	ErrorMessage      string          `gorm:"type:text;comment:错误信息" json:"error_message"`
	WarningMessage    string          `gorm:"type:text;comment:警告信息" json:"warning_message"`
	InputData         datatypes.JSON  `gorm:"type:json;comment:输入数据" json:"input_data"`
	OutputData        datatypes.JSON  `gorm:"type:json;comment:输出数据" json:"output_data"`
	OperatorId        *uint           `gorm:"comment:操作人ID" json:"operator_id"`
}

func (InsContractCleaningLog) TableName() string {
	return "ins_contract_cleaning_log"
}

// InsContractCleaningBatch 批量清洗记录表
type InsContractCleaningBatch struct {
	global.GVA_MODEL

	BatchNo              string          `gorm:"type:varchar(50);not null;unique;comment:批次号" json:"batch_no"`
	BatchName            string          `gorm:"type:varchar(200);comment:批次名称" json:"batch_name"`
	ApprovalCode         string          `gorm:"type:varchar(100);index;comment:审批代码" json:"approval_code"`
	TotalCount           int             `gorm:"not null;default:0;comment:总记录数" json:"total_count"`
	ProcessedCount       int             `gorm:"not null;default:0;comment:已处理记录数" json:"processed_count"`
	SuccessCount         int             `gorm:"not null;default:0;comment:成功记录数" json:"success_count"`
	FailedCount          int             `gorm:"not null;default:0;comment:失败记录数" json:"failed_count"`
	WarningCount         int             `gorm:"not null;default:0;comment:警告记录数" json:"warning_count"`
	DetailRecordsCreated int             `gorm:"not null;default:0;comment:创建的明细记录数" json:"detail_records_created"`
	Status               string          `gorm:"type:varchar(20);not null;default:'pending';index;comment:批次状态" json:"status"`
	StartTime            *time.Time      `gorm:"type:datetime;index;comment:开始时间" json:"start_time"`
	EndTime              *time.Time      `gorm:"type:datetime;comment:结束时间" json:"end_time"`
	OperatorId           uint            `gorm:"not null;index;comment:操作人ID" json:"operator_id"`
	OperatorName         string          `gorm:"type:varchar(100);not null;comment:操作人姓名" json:"operator_name"`
	RuleVersion          string          `gorm:"type:varchar(20);default:'1.0';comment:使用的规则版本" json:"rule_version"`
	ConfigParams         datatypes.JSON  `gorm:"type:json;comment:配置参数" json:"config_params"`
	SummaryReport        datatypes.JSON  `gorm:"type:json;comment:汇总报告" json:"summary_report"`
}

func (InsContractCleaningBatch) TableName() string {
	return "ins_contract_cleaning_batch"
}
```

### 3.2 状态常量定义

```go
// server/model/insbuy/ins_contract_cleaned_constants.go
package insbuy

const (
	// 清洗状态
	CleaningStatusPending    = "pending"    // 待清洗
	CleaningStatusProcessing = "processing" // 清洗中
	CleaningStatusCompleted  = "completed"  // 已完成
	CleaningStatusFailed     = "failed"     // 失败
	CleaningStatusVerified   = "verified"   // 已验证

	// 验证状态
	VerificationStatusUnverified = "unverified" // 未验证
	VerificationStatusPassed     = "passed"     // 验证通过
	VerificationStatusRejected   = "rejected"   // 验证驳回

	// 规则类型
	RuleTypeFieldMapping   = "field_mapping"   // 字段映射
	RuleTypeDataValidation = "data_validation" // 数据验证
	RuleTypeDetailSplit    = "detail_split"    // 明细拆分

	// 操作类型
	OperationTypeClean  = "clean"  // 清洗
	OperationTypeVerify = "verify" // 验证
	OperationTypeSplit  = "split"  // 拆分

	// 操作状态
	OperationStatusSuccess = "success" // 成功
	OperationStatusFailed  = "failed"  // 失败
	OperationStatusWarning = "warning" // 警告

	// 批次状态
	BatchStatusPending    = "pending"    // 待处理
	BatchStatusProcessing = "processing" // 处理中
	BatchStatusCompleted  = "completed"  // 已完成
	BatchStatusFailed     = "failed"     // 失败
)
```

## 4. 核心业务逻辑

### 4.1 数据清洗服务

```go
// server/service/insbuy/ins_contract_cleaning.go
package insbuy

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ContractCleaningService struct{}

// CleaningRule 清洗规则结构
type CleaningRule struct {
	FieldMappings   map[string]FieldMapping `json:"field_mappings"`
	ValidationRules []ValidationRule        `json:"validation_rules"`
	DetailSplitRule *DetailSplitRule        `json:"detail_split_rule"`
}

// FieldMapping 字段映射规则
type FieldMapping struct {
	SourcePath    string      `json:"source_path"`    // 源字段路径
	TargetField   string      `json:"target_field"`   // 目标字段
	DataType      string      `json:"data_type"`      // 数据类型
	DefaultValue  interface{} `json:"default_value"`  // 默认值
	Transform     string      `json:"transform"`      // 转换函数
	Required      bool        `json:"required"`       // 是否必填
	Validation    string      `json:"validation"`     // 验证规则
}

// ValidationRule 验证规则
type ValidationRule struct {
	Field     string `json:"field"`     // 字段名
	Rule      string `json:"rule"`      // 规则类型
	Parameter string `json:"parameter"` // 规则参数
	Message   string `json:"message"`   // 错误消息
}

// DetailSplitRule 明细拆分规则
type DetailSplitRule struct {
	DetailFieldPath string                 `json:"detail_field_path"` // 明细字段路径
	DetailMappings  map[string]FieldMapping `json:"detail_mappings"`   // 明细字段映射
	SplitCondition  string                 `json:"split_condition"`   // 拆分条件
}

// CleaningResult 清洗结果
type CleaningResult struct {
	Success         bool                      `json:"success"`
	MainRecord      *insbuy.InsContractCleaned `json:"main_record"`
	DetailRecords   []*insbuy.InsContractCleaned `json:"detail_records"`
	Errors          []string                  `json:"errors"`
	Warnings        []string                  `json:"warnings"`
	QualityScore    float64                   `json:"quality_score"`
	ProcessingTime  time.Duration             `json:"processing_time"`
}

// CleanContract 清洗单个合同数据
func (s *ContractCleaningService) CleanContract(contractId uint) (*CleaningResult, error) {
	startTime := time.Now()

	// 1. 查询源合同数据
	contract, err := s.getContractById(contractId)
	if err != nil {
		return nil, fmt.Errorf("查询源合同失败: %w", err)
	}

	// 2. 获取清洗规则
	rules, err := s.getCleaningRules(contract.ApprovalCode)
	if err != nil {
		return nil, fmt.Errorf("获取清洗规则失败: %w", err)
	}

	// 3. 执行数据清洗
	result := &CleaningResult{
		Success:        true,
		Errors:         make([]string, 0),
		Warnings:       make([]string, 0),
		ProcessingTime: time.Since(startTime),
	}

	// 4. 解析表单数据
	formData, err := s.parseFormData(contract.Form)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("解析表单数据失败: %v", err))
		result.Success = false
		return result, nil
	}

	// 5. 检查是否需要拆分明细
	if rules.DetailSplitRule != nil {
		return s.cleanWithDetailSplit(contract, formData, rules, result)
	} else {
		return s.cleanSingleRecord(contract, formData, rules, result)
	}
}

// cleanSingleRecord 清洗单条记录（无明细拆分）
func (s *ContractCleaningService) cleanSingleRecord(contract *insbuy.InsContract, formData map[string]interface{}, rules *CleaningRule, result *CleaningResult) (*CleaningResult, error) {
	// 创建清洗记录
	cleaned := &insbuy.InsContractCleaned{
		SourceContractId:    contract.ID,
		SourceInstanceCode:  contract.InstanceCode,
		DetailIndex:         0,
		DetailTotal:         1,
		CleaningStatus:      insbuy.CleaningStatusProcessing,
		CleaningRuleVersion: "1.0",
		OriginalFormData:    contract.Form,
	}

	// 执行字段映射
	s.applyFieldMappings(cleaned, contract, formData, rules.FieldMappings)

	// 执行数据验证
	validationErrors := s.validateData(cleaned, rules.ValidationRules)
	if len(validationErrors) > 0 {
		result.Errors = append(result.Errors, validationErrors...)
		cleaned.CleaningStatus = insbuy.CleaningStatusFailed
		cleaned.ValidationErrors = s.toJSON(validationErrors)
	} else {
		cleaned.CleaningStatus = insbuy.CleaningStatusCompleted
	}

	// 计算数据质量评分
	cleaned.DataQualityScore = s.calculateQualityScore(cleaned)
	cleaned.CleaningTime = &time.Time{}
	*cleaned.CleaningTime = time.Now()

	result.MainRecord = cleaned
	result.QualityScore = cleaned.DataQualityScore

	return result, nil
}

// cleanWithDetailSplit 清洗并拆分明细记录
func (s *ContractCleaningService) cleanWithDetailSplit(contract *insbuy.InsContract, formData map[string]interface{}, rules *CleaningRule, result *CleaningResult) (*CleaningResult, error) {
	// 1. 提取明细数据
	detailData, err := s.extractDetailData(formData, rules.DetailSplitRule.DetailFieldPath)
	if err != nil {
		result.Errors = append(result.Errors, fmt.Sprintf("提取明细数据失败: %v", err))
		result.Success = false
		return result, nil
	}

	if len(detailData) == 0 {
		// 没有明细数据，按单条记录处理
		return s.cleanSingleRecord(contract, formData, rules, result)
	}

	// 2. 创建主记录
	mainRecord := &insbuy.InsContractCleaned{
		SourceContractId:    contract.ID,
		SourceInstanceCode:  contract.InstanceCode,
		DetailIndex:         0,
		DetailTotal:         len(detailData) + 1,
		CleaningStatus:      insbuy.CleaningStatusProcessing,
		CleaningRuleVersion: "1.0",
		OriginalFormData:    contract.Form,
	}

	// 应用主记录字段映射
	s.applyFieldMappings(mainRecord, contract, formData, rules.FieldMappings)

	// 3. 创建明细记录
	detailRecords := make([]*insbuy.InsContractCleaned, 0, len(detailData))
	totalAmount := 0.0

	for i, detail := range detailData {
		detailRecord := &insbuy.InsContractCleaned{
			SourceContractId:    contract.ID,
			SourceInstanceCode:  contract.InstanceCode,
			DetailIndex:         i + 1,
			DetailTotal:         len(detailData) + 1,
			CleaningStatus:      insbuy.CleaningStatusProcessing,
			CleaningRuleVersion: "1.0",
			OriginalFormData:    s.toJSON(detail),
		}

		// 复制主记录的基础信息
		s.copyBaseFields(detailRecord, mainRecord)

		// 应用明细字段映射
		s.applyDetailFieldMappings(detailRecord, detail, rules.DetailSplitRule.DetailMappings)

		// 验证明细数据
		validationErrors := s.validateData(detailRecord, rules.ValidationRules)
		if len(validationErrors) > 0 {
			result.Warnings = append(result.Warnings, fmt.Sprintf("明细记录%d验证失败: %v", i+1, validationErrors))
			detailRecord.ValidationErrors = s.toJSON(validationErrors)
		}

		detailRecord.CleaningStatus = insbuy.CleaningStatusCompleted
		detailRecord.DataQualityScore = s.calculateQualityScore(detailRecord)
		detailRecord.CleaningTime = &time.Time{}
		*detailRecord.CleaningTime = time.Now()

		detailRecords = append(detailRecords, detailRecord)
		totalAmount += detailRecord.Amount
	}

	// 4. 更新主记录的汇总信息
	mainRecord.Amount = totalAmount
	mainRecord.CleaningStatus = insbuy.CleaningStatusCompleted
	mainRecord.DataQualityScore = s.calculateQualityScore(mainRecord)
	mainRecord.CleaningTime = &time.Time{}
	*mainRecord.CleaningTime = time.Now()

	result.MainRecord = mainRecord
	result.DetailRecords = detailRecords
	result.QualityScore = mainRecord.DataQualityScore

	return result, nil
}

// BatchCleanContracts 批量清洗合同数据
func (s *ContractCleaningService) BatchCleanContracts(approvalCode string, operatorId uint, operatorName string) (string, error) {
	// 1. 生成批次号
	batchNo := s.generateBatchNo()

	// 2. 查询待清洗的合同数据
	contracts, err := s.getContractsForCleaning(approvalCode)
	if err != nil {
		return "", fmt.Errorf("查询待清洗合同失败: %w", err)
	}

	// 3. 创建批次记录
	batch := &insbuy.InsContractCleaningBatch{
		BatchNo:      batchNo,
		BatchName:    fmt.Sprintf("批量清洗-%s", time.Now().Format("20060102-150405")),
		ApprovalCode: approvalCode,
		TotalCount:   len(contracts),
		Status:       insbuy.BatchStatusProcessing,
		StartTime:    &time.Time{},
		OperatorId:   operatorId,
		OperatorName: operatorName,
		RuleVersion:  "1.0",
	}
	*batch.StartTime = time.Now()

	if err := global.GVA_DB.Create(batch).Error; err != nil {
		return "", fmt.Errorf("创建批次记录失败: %w", err)
	}

	// 4. 异步处理清洗任务
	go s.processBatchCleaning(batch, contracts)

	return batchNo, nil
}
```

## 5. 数据流程图

### 5.1 数据清洗整体流程

```mermaid
graph TD
    A[飞书合同数据 ins_contract] --> B{是否包含明细}
    B -->|否| C[单条记录清洗]
    B -->|是| D[明细拆分清洗]

    C --> E[字段映射]
    E --> F[数据验证]
    F --> G[质量评分]
    G --> H[保存到 ins_contract_cleaned]

    D --> I[提取明细数据]
    I --> J[创建主记录]
    I --> K[创建明细记录]
    J --> L[主记录字段映射]
    K --> M[明细字段映射]
    L --> N[数据验证]
    M --> N
    N --> O[质量评分]
    O --> P[批量保存清洗结果]

    H --> Q{清洗状态}
    P --> Q
    Q -->|completed| R[待验证]
    Q -->|failed| S[重新清洗]

    R --> T[业务人员验证]
    T --> U{验证结果}
    U -->|通过| V[状态更新为verified]
    U -->|驳回| W[状态更新为rejected]

    V --> X[数据可用于报表]
    W --> S
```

### 5.2 明细拆分处理流程

```mermaid
graph TD
    A[合同表单数据] --> B[解析JSON表单]
    B --> C{检查明细字段}
    C -->|存在| D[提取明细数组]
    C -->|不存在| E[单条记录处理]

    D --> F[遍历明细数组]
    F --> G[创建明细记录]
    G --> H[复制基础信息]
    H --> I[映射明细字段]
    I --> J[验证明细数据]
    J --> K{还有明细?}
    K -->|是| F
    K -->|否| L[汇总计算]

    L --> M[更新主记录汇总]
    M --> N[保存所有记录]

    E --> O[直接字段映射]
    O --> P[保存单条记录]
```

## 6. 清洗规则配置示例

### 6.1 付款申请清洗规则

```json
{
  "field_mappings": {
    "application_number": {
      "source_path": "form.widget_application_number.value",
      "target_field": "application_number",
      "data_type": "string",
      "required": true
    },
    "payment_reason": {
      "source_path": "form.widget_payment_reason.value",
      "target_field": "payment_reason",
      "data_type": "string",
      "required": true
    },
    "amount": {
      "source_path": "form.widget_amount.value",
      "target_field": "amount",
      "data_type": "float",
      "required": true,
      "validation": "min:0"
    }
  },
  "validation_rules": [
    {
      "field": "amount",
      "rule": "required",
      "message": "金额不能为空"
    },
    {
      "field": "amount",
      "rule": "min",
      "parameter": "0",
      "message": "金额必须大于0"
    }
  ],
  "detail_split_rule": null
}
```

### 6.2 费用报销清洗规则（含明细拆分）

```json
{
  "field_mappings": {
    "application_number": {
      "source_path": "form.widget_application_number.value",
      "target_field": "application_number",
      "data_type": "string",
      "required": true
    },
    "reimbursement_reason": {
      "source_path": "form.widget_reimbursement_reason.value",
      "target_field": "reimbursement_reason",
      "data_type": "string",
      "required": true
    }
  },
  "validation_rules": [
    {
      "field": "expense_type",
      "rule": "required",
      "message": "费用类型不能为空"
    }
  ],
  "detail_split_rule": {
    "detail_field_path": "form.widget_expense_list.value",
    "detail_mappings": {
      "expense_type": {
        "source_path": "widget_expense_type.value",
        "target_field": "expense_type",
        "data_type": "string",
        "required": true
      },
      "amount": {
        "source_path": "widget_amount.value",
        "target_field": "amount",
        "data_type": "float",
        "required": true
      },
      "expense_location": {
        "source_path": "widget_location.value",
        "target_field": "expense_location",
        "data_type": "string"
      },
      "expense_start_date": {
        "source_path": "widget_start_date.value",
        "target_field": "expense_start_date",
        "data_type": "datetime"
      },
      "expense_end_date": {
        "source_path": "widget_end_date.value",
        "target_field": "expense_end_date",
        "data_type": "datetime"
      }
    }
  }
}
```

## 7. API接口设计

### 7.1 清洗相关接口

```go
// 单个合同清洗
POST /contract-cleaning/clean/{contractId}

// 批量清洗
POST /contract-cleaning/batch-clean
{
  "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
  "operator_name": "张三"
}

// 获取清洗结果列表
POST /contract-cleaning/list
{
  "page": 1,
  "page_size": 20,
  "cleaning_status": "completed",
  "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5"
}

// 验证清洗结果
POST /contract-cleaning/verify
{
  "ids": [1, 2, 3],
  "verification_status": "passed",
  "verification_note": "数据验证通过"
}

// 获取清洗统计
GET /contract-cleaning/stats

// 获取批次处理状态
GET /contract-cleaning/batch/{batchNo}
```

## 8. 实施步骤

### 8.1 第一阶段：基础表结构（2天）
- 创建数据库表结构
- 编写Go模型定义
- 实现基础CRUD操作

### 8.2 第二阶段：清洗规则引擎（3天）
- 实现字段映射逻辑
- 开发数据验证机制
- 构建质量评分算法

### 8.3 第三阶段：明细拆分功能（3天）
- 实现明细数据提取
- 开发拆分处理逻辑
- 测试各种明细场景

### 8.4 第四阶段：批量处理（2天）
- 实现批量清洗功能
- 添加进度监控
- 优化性能和并发处理

### 8.5 第五阶段：验证和API（2天）
- 开发验证审核功能
- 完善API接口
- 编写接口文档

## 9. 核心特性

✅ **基于现有合同表** - 直接处理 `ins_contract` 表数据
✅ **智能明细拆分** - 自动识别并拆分明细数据为多条记录
✅ **灵活清洗规则** - 支持不同审批类型的清洗配置
✅ **数据质量评分** - 自动计算数据完整性和准确性
✅ **批量处理能力** - 支持大量数据的高效清洗
✅ **验证审核机制** - 业务人员可以审核清洗结果
✅ **完整操作日志** - 记录所有清洗和验证操作

这个方案专门针对您现有的飞书合同数据表设计，重点解决明细数据的拆分处理问题，确保一条包含多个明细的合同能够被正确拆分为多条清洗记录。
```
