# 合同审批数据中间表（Staging Table）技术方案

## 1. 项目概述

### 1.1 背景
设计一个中间数据表（staging table）用于存储从飞书系统拉取并经过数据清洗和转换后的合同审批数据，提供数据二次确认机制，支持业务人员审核和修改转换结果。

### 1.2 核心需求
- 存储标准化的合同审批数据
- 支持多种审批类型（付款申请、费用报销等）
- 提供数据状态管理和修改历史
- 支持批量和单条确认操作
- 提供数据回滚机制

## 2. 数据库表结构设计

### 2.1 主表：ins_contract_staging（合同审批中间表）

```sql
CREATE TABLE `ins_contract_staging` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建人',
  `updated_by` bigint unsigned DEFAULT NULL COMMENT '更新人',
  `deleted_by` bigint unsigned DEFAULT NULL COMMENT '删除人',
  
  -- 源数据信息
  `source_instance_code` varchar(100) NOT NULL COMMENT '源实例代码',
  `approval_code` varchar(100) NOT NULL COMMENT '审批定义Code',
  `approval_name` varchar(200) NOT NULL COMMENT '审批定义名称',
  `transform_time` datetime NOT NULL COMMENT '转换时间',
  `data_version` varchar(20) DEFAULT '1.0' COMMENT '数据版本',
  
  -- 状态管理
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '数据状态：pending-待确认，confirmed-已确认，modified-已修改，rejected-已驳回，migrated-已迁移',
  `confirm_user_id` bigint unsigned DEFAULT NULL COMMENT '确认人ID',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认时间',
  `reject_reason` text DEFAULT NULL COMMENT '驳回原因',
  
  -- 基础信息
  `application_number` varchar(100) DEFAULT NULL COMMENT '申请编号',
  `title` varchar(500) DEFAULT NULL COMMENT '标题',
  `application_status` varchar(50) DEFAULT NULL COMMENT '申请状态',
  `initiate_time` datetime DEFAULT NULL COMMENT '发起时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  
  -- 人员信息
  `initiator_employee_id` varchar(50) DEFAULT NULL COMMENT '发起人工号',
  `initiator_user_id` varchar(100) DEFAULT NULL COMMENT '发起人用户ID',
  `initiator_name` varchar(100) DEFAULT NULL COMMENT '发起人姓名',
  `initiator_department_id` varchar(100) DEFAULT NULL COMMENT '发起人部门ID',
  `department_manager` varchar(100) DEFAULT NULL COMMENT '部门经理',
  
  -- 业务信息
  `payment_reason` varchar(500) DEFAULT NULL COMMENT '付款事由',
  `payment_entity` varchar(200) DEFAULT NULL COMMENT '付款主体',
  `reimbursement_reason` varchar(500) DEFAULT NULL COMMENT '报销事由',
  `reimbursement_entity` varchar(200) DEFAULT NULL COMMENT '报销主体',
  `business_type` varchar(100) DEFAULT NULL COMMENT '业务类型',
  `payment_currency` varchar(10) DEFAULT 'CNY' COMMENT '付款币种',
  
  -- 金额信息
  `contract_sign_amount` decimal(16,2) DEFAULT 0.00 COMMENT '合同签约金额',
  `contract_paid_amount` decimal(16,2) DEFAULT 0.00 COMMENT '合同已付金额',
  `current_request_amount` decimal(16,2) DEFAULT 0.00 COMMENT '本次请款金额',
  `total_amount` decimal(16,2) DEFAULT 0.00 COMMENT '费用总金额',
  `tax_rate` decimal(5,4) DEFAULT 0.0000 COMMENT '税率',
  `vat_invoice_type` varchar(50) DEFAULT NULL COMMENT '增值税发票类型',
  
  -- 银行信息
  `account_holder` varchar(200) DEFAULT NULL COMMENT '收款方户名',
  `account_number` varchar(100) DEFAULT NULL COMMENT '账户号码',
  `account_type` varchar(50) DEFAULT NULL COMMENT '账户类型',
  `bank_name` varchar(200) DEFAULT NULL COMMENT '银行名称',
  `bank_branch` varchar(200) DEFAULT NULL COMMENT '银行支行',
  `bank_region` varchar(100) DEFAULT NULL COMMENT '银行地区',
  
  -- 其他信息
  `expected_payment_date` datetime DEFAULT NULL COMMENT '期望付款日期',
  `remarks` text DEFAULT NULL COMMENT '备注',
  `serial_number` varchar(100) DEFAULT NULL COMMENT '审批序列号',
  
  -- 多币种金额
  `usd_amount` decimal(16,2) DEFAULT 0.00 COMMENT '美元金额',
  `eur_amount` decimal(16,2) DEFAULT 0.00 COMMENT '欧元金额',
  `jpy_amount` decimal(16,2) DEFAULT 0.00 COMMENT '日元金额',
  `hkd_amount` decimal(16,2) DEFAULT 0.00 COMMENT '港币金额',
  `gbp_amount` decimal(16,2) DEFAULT 0.00 COMMENT '英镑金额',
  
  -- 转换后的人民币金额
  `usd_to_rmb_amount` decimal(16,2) DEFAULT 0.00 COMMENT '美元转人民币金额',
  `eur_to_rmb_amount` decimal(16,2) DEFAULT 0.00 COMMENT '欧元转人民币金额',
  `jpy_to_rmb_amount` decimal(16,2) DEFAULT 0.00 COMMENT '日元转人民币金额',
  `hkd_to_rmb_amount` decimal(16,2) DEFAULT 0.00 COMMENT '港币转人民币金额',
  `gbp_to_rmb_amount` decimal(16,2) DEFAULT 0.00 COMMENT '英镑转人民币金额',
  
  -- 审批流程信息
  `approval_duration` int DEFAULT 0 COMMENT '审批耗时（分钟）',
  `approval_node_count` int DEFAULT 0 COMMENT '审批节点数量',
  `current_approval_node` varchar(200) DEFAULT NULL COMMENT '当前审批节点',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_source_instance` (`source_instance_code`),
  KEY `idx_approval_code` (`approval_code`),
  KEY `idx_status` (`status`),
  KEY `idx_transform_time` (`transform_time`),
  KEY `idx_confirm_time` (`confirm_time`),
  KEY `idx_initiator_user_id` (`initiator_user_id`),
  KEY `idx_application_number` (`application_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同审批数据中间表';
```

### 2.2 附件表：ins_contract_staging_attachment（中间表附件）

```sql
CREATE TABLE `ins_contract_staging_attachment` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  
  `staging_id` bigint unsigned NOT NULL COMMENT '中间表ID',
  `file_name` varchar(500) NOT NULL COMMENT '文件名',
  `file_url` varchar(1000) NOT NULL COMMENT '文件URL',
  `file_size` bigint DEFAULT 0 COMMENT '文件大小（字节）',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  
  PRIMARY KEY (`id`),
  KEY `idx_staging_id` (`staging_id`),
  CONSTRAINT `fk_staging_attachment` FOREIGN KEY (`staging_id`) REFERENCES `ins_contract_staging` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同审批中间表附件';
```

### 2.3 费用明细表：ins_contract_staging_expense（费用明细）

```sql
CREATE TABLE `ins_contract_staging_expense` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  
  `staging_id` bigint unsigned NOT NULL COMMENT '中间表ID',
  `expense_type` varchar(100) DEFAULT NULL COMMENT '费用类型',
  `location` varchar(200) DEFAULT NULL COMMENT '地点',
  `date_range` varchar(100) DEFAULT NULL COMMENT '日期区间',
  `start_date` datetime DEFAULT NULL COMMENT '开始日期',
  `end_date` datetime DEFAULT NULL COMMENT '结束日期',
  `vat_invoice_type` varchar(50) DEFAULT NULL COMMENT '增值税发票类型',
  `amount` decimal(16,2) DEFAULT 0.00 COMMENT '金额',
  `amount_currency` varchar(10) DEFAULT 'CNY' COMMENT '金额币种',
  `amount_capital` varchar(200) DEFAULT NULL COMMENT '金额大写',
  `row_index` int DEFAULT 0 COMMENT '行索引',
  
  PRIMARY KEY (`id`),
  KEY `idx_staging_id` (`staging_id`),
  KEY `idx_expense_type` (`expense_type`),
  CONSTRAINT `fk_staging_expense` FOREIGN KEY (`staging_id`) REFERENCES `ins_contract_staging` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同审批中间表费用明细';
```

### 2.4 修改历史表：ins_contract_staging_history（修改历史）

```sql
CREATE TABLE `ins_contract_staging_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  
  `staging_id` bigint unsigned NOT NULL COMMENT '中间表ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型：create-创建，update-更新，confirm-确认，reject-驳回，migrate-迁移',
  `operator_id` bigint unsigned NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `field_name` varchar(100) DEFAULT NULL COMMENT '修改字段名',
  `old_value` text DEFAULT NULL COMMENT '修改前值',
  `new_value` text DEFAULT NULL COMMENT '修改后值',
  `operation_reason` text DEFAULT NULL COMMENT '操作原因',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  
  PRIMARY KEY (`id`),
  KEY `idx_staging_id` (`staging_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_staging_history` FOREIGN KEY (`staging_id`) REFERENCES `ins_contract_staging` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同审批中间表修改历史';
```

### 2.5 批量操作表：ins_contract_staging_batch（批量操作记录）

```sql
CREATE TABLE `ins_contract_staging_batch` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  
  `batch_no` varchar(50) NOT NULL COMMENT '批次号',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型：confirm-批量确认，reject-批量驳回，migrate-批量迁移',
  `operator_id` bigint unsigned NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `total_count` int NOT NULL DEFAULT 0 COMMENT '总记录数',
  `success_count` int NOT NULL DEFAULT 0 COMMENT '成功记录数',
  `failed_count` int NOT NULL DEFAULT 0 COMMENT '失败记录数',
  `status` varchar(20) NOT NULL DEFAULT 'processing' COMMENT '批次状态：processing-处理中，completed-已完成，failed-失败',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同审批中间表批量操作记录';
```

## 3. Go模型定义

### 3.1 主模型：InsContractStaging

```go
// server/model/insbuy/ins_contract_staging.go
package insbuy

import (
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// InsContractStaging 合同审批中间表
type InsContractStaging struct {
	global.GVA_MODEL

	// 源数据信息
	SourceInstanceCode string    `gorm:"type:varchar(100);not null;unique;comment:源实例代码" json:"source_instance_code"`
	ApprovalCode       string    `gorm:"type:varchar(100);not null;index;comment:审批定义Code" json:"approval_code"`
	ApprovalName       string    `gorm:"type:varchar(200);not null;comment:审批定义名称" json:"approval_name"`
	TransformTime      time.Time `gorm:"type:datetime;not null;index;comment:转换时间" json:"transform_time"`
	DataVersion        string    `gorm:"type:varchar(20);default:'1.0';comment:数据版本" json:"data_version"`

	// 状态管理
	Status        string     `gorm:"type:varchar(20);not null;default:'pending';index;comment:数据状态" json:"status"`
	ConfirmUserId *uint      `gorm:"comment:确认人ID" json:"confirm_user_id"`
	ConfirmTime   *time.Time `gorm:"type:datetime;index;comment:确认时间" json:"confirm_time"`
	RejectReason  string     `gorm:"type:text;comment:驳回原因" json:"reject_reason"`

	// 基础信息
	ApplicationNumber string     `gorm:"type:varchar(100);index;comment:申请编号" json:"application_number"`
	Title             string     `gorm:"type:varchar(500);comment:标题" json:"title"`
	ApplicationStatus string     `gorm:"type:varchar(50);comment:申请状态" json:"application_status"`
	InitiateTime      *time.Time `gorm:"type:datetime;comment:发起时间" json:"initiate_time"`
	CompleteTime      *time.Time `gorm:"type:datetime;comment:完成时间" json:"complete_time"`

	// 人员信息
	InitiatorEmployeeId   string `gorm:"type:varchar(50);comment:发起人工号" json:"initiator_employee_id"`
	InitiatorUserId       string `gorm:"type:varchar(100);index;comment:发起人用户ID" json:"initiator_user_id"`
	InitiatorName         string `gorm:"type:varchar(100);comment:发起人姓名" json:"initiator_name"`
	InitiatorDepartmentId string `gorm:"type:varchar(100);comment:发起人部门ID" json:"initiator_department_id"`
	DepartmentManager     string `gorm:"type:varchar(100);comment:部门经理" json:"department_manager"`

	// 业务信息
	PaymentReason        string `gorm:"type:varchar(500);comment:付款事由" json:"payment_reason"`
	PaymentEntity        string `gorm:"type:varchar(200);comment:付款主体" json:"payment_entity"`
	ReimbursementReason  string `gorm:"type:varchar(500);comment:报销事由" json:"reimbursement_reason"`
	ReimbursementEntity  string `gorm:"type:varchar(200);comment:报销主体" json:"reimbursement_entity"`
	BusinessType         string `gorm:"type:varchar(100);comment:业务类型" json:"business_type"`
	PaymentCurrency      string `gorm:"type:varchar(10);default:'CNY';comment:付款币种" json:"payment_currency"`

	// 金额信息
	ContractSignAmount   float64 `gorm:"type:decimal(16,2);default:0.00;comment:合同签约金额" json:"contract_sign_amount"`
	ContractPaidAmount   float64 `gorm:"type:decimal(16,2);default:0.00;comment:合同已付金额" json:"contract_paid_amount"`
	CurrentRequestAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:本次请款金额" json:"current_request_amount"`
	TotalAmount          float64 `gorm:"type:decimal(16,2);default:0.00;comment:费用总金额" json:"total_amount"`
	TaxRate              float64 `gorm:"type:decimal(5,4);default:0.0000;comment:税率" json:"tax_rate"`
	VatInvoiceType       string  `gorm:"type:varchar(50);comment:增值税发票类型" json:"vat_invoice_type"`

	// 银行信息
	AccountHolder string `gorm:"type:varchar(200);comment:收款方户名" json:"account_holder"`
	AccountNumber string `gorm:"type:varchar(100);comment:账户号码" json:"account_number"`
	AccountType   string `gorm:"type:varchar(50);comment:账户类型" json:"account_type"`
	BankName      string `gorm:"type:varchar(200);comment:银行名称" json:"bank_name"`
	BankBranch    string `gorm:"type:varchar(200);comment:银行支行" json:"bank_branch"`
	BankRegion    string `gorm:"type:varchar(100);comment:银行地区" json:"bank_region"`

	// 其他信息
	ExpectedPaymentDate *time.Time `gorm:"type:datetime;comment:期望付款日期" json:"expected_payment_date"`
	Remarks             string     `gorm:"type:text;comment:备注" json:"remarks"`
	SerialNumber        string     `gorm:"type:varchar(100);comment:审批序列号" json:"serial_number"`

	// 多币种金额
	UsdAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:美元金额" json:"usd_amount"`
	EurAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:欧元金额" json:"eur_amount"`
	JpyAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:日元金额" json:"jpy_amount"`
	HkdAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:港币金额" json:"hkd_amount"`
	GbpAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:英镑金额" json:"gbp_amount"`

	// 转换后的人民币金额
	UsdToRmbAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:美元转人民币金额" json:"usd_to_rmb_amount"`
	EurToRmbAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:欧元转人民币金额" json:"eur_to_rmb_amount"`
	JpyToRmbAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:日元转人民币金额" json:"jpy_to_rmb_amount"`
	HkdToRmbAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:港币转人民币金额" json:"hkd_to_rmb_amount"`
	GbpToRmbAmount float64 `gorm:"type:decimal(16,2);default:0.00;comment:英镑转人民币金额" json:"gbp_to_rmb_amount"`

	// 审批流程信息
	ApprovalDuration     int    `gorm:"type:int;default:0;comment:审批耗时（分钟）" json:"approval_duration"`
	ApprovalNodeCount    int    `gorm:"type:int;default:0;comment:审批节点数量" json:"approval_node_count"`
	CurrentApprovalNode  string `gorm:"type:varchar(200);comment:当前审批节点" json:"current_approval_node"`

	// 关联关系
	Attachments []InsContractStagingAttachment `gorm:"foreignKey:StagingId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"attachments"`
	ExpenseDetails []InsContractStagingExpense `gorm:"foreignKey:StagingId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"expense_details"`
	Histories   []InsContractStagingHistory    `gorm:"foreignKey:StagingId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"histories"`
}

func (InsContractStaging) TableName() string {
	return "ins_contract_staging"
}

func (InsContractStaging) TableComment() string {
	return "合同审批数据中间表"
}

// InsContractStagingAttachment 中间表附件
type InsContractStagingAttachment struct {
	global.GVA_MODEL

	StagingId uint   `gorm:"not null;index;comment:中间表ID" json:"staging_id"`
	FileName  string `gorm:"type:varchar(500);not null;comment:文件名" json:"file_name"`
	FileUrl   string `gorm:"type:varchar(1000);not null;comment:文件URL" json:"file_url"`
	FileSize  int64  `gorm:"default:0;comment:文件大小（字节）" json:"file_size"`
	FileType  string `gorm:"type:varchar(50);comment:文件类型" json:"file_type"`
	SortOrder int    `gorm:"default:0;comment:排序" json:"sort_order"`
}

func (InsContractStagingAttachment) TableName() string {
	return "ins_contract_staging_attachment"
}

// InsContractStagingExpense 费用明细
type InsContractStagingExpense struct {
	global.GVA_MODEL

	StagingId        uint       `gorm:"not null;index;comment:中间表ID" json:"staging_id"`
	ExpenseType      string     `gorm:"type:varchar(100);index;comment:费用类型" json:"expense_type"`
	Location         string     `gorm:"type:varchar(200);comment:地点" json:"location"`
	DateRange        string     `gorm:"type:varchar(100);comment:日期区间" json:"date_range"`
	StartDate        *time.Time `gorm:"type:datetime;comment:开始日期" json:"start_date"`
	EndDate          *time.Time `gorm:"type:datetime;comment:结束日期" json:"end_date"`
	VatInvoiceType   string     `gorm:"type:varchar(50);comment:增值税发票类型" json:"vat_invoice_type"`
	Amount           float64    `gorm:"type:decimal(16,2);default:0.00;comment:金额" json:"amount"`
	AmountCurrency   string     `gorm:"type:varchar(10);default:'CNY';comment:金额币种" json:"amount_currency"`
	AmountCapital    string     `gorm:"type:varchar(200);comment:金额大写" json:"amount_capital"`
	RowIndex         int        `gorm:"default:0;comment:行索引" json:"row_index"`
}

func (InsContractStagingExpense) TableName() string {
	return "ins_contract_staging_expense"
}

// InsContractStagingHistory 修改历史
type InsContractStagingHistory struct {
	global.GVA_MODEL

	StagingId       uint   `gorm:"not null;index;comment:中间表ID" json:"staging_id"`
	OperationType   string `gorm:"type:varchar(20);not null;index;comment:操作类型" json:"operation_type"`
	OperatorId      uint   `gorm:"not null;index;comment:操作人ID" json:"operator_id"`
	OperatorName    string `gorm:"type:varchar(100);not null;comment:操作人姓名" json:"operator_name"`
	FieldName       string `gorm:"type:varchar(100);comment:修改字段名" json:"field_name"`
	OldValue        string `gorm:"type:text;comment:修改前值" json:"old_value"`
	NewValue        string `gorm:"type:text;comment:修改后值" json:"new_value"`
	OperationReason string `gorm:"type:text;comment:操作原因" json:"operation_reason"`
	IpAddress       string `gorm:"type:varchar(50);comment:IP地址" json:"ip_address"`
	UserAgent       string `gorm:"type:varchar(500);comment:用户代理" json:"user_agent"`
}

func (InsContractStagingHistory) TableName() string {
	return "ins_contract_staging_history"
}

// InsContractStagingBatch 批量操作记录
type InsContractStagingBatch struct {
	global.GVA_MODEL

	BatchNo       string     `gorm:"type:varchar(50);not null;unique;comment:批次号" json:"batch_no"`
	OperationType string     `gorm:"type:varchar(20);not null;index;comment:操作类型" json:"operation_type"`
	OperatorId    uint       `gorm:"not null;index;comment:操作人ID" json:"operator_id"`
	OperatorName  string     `gorm:"type:varchar(100);not null;comment:操作人姓名" json:"operator_name"`
	TotalCount    int        `gorm:"not null;default:0;comment:总记录数" json:"total_count"`
	SuccessCount  int        `gorm:"not null;default:0;comment:成功记录数" json:"success_count"`
	FailedCount   int        `gorm:"not null;default:0;comment:失败记录数" json:"failed_count"`
	Status        string     `gorm:"type:varchar(20);not null;default:'processing';index;comment:批次状态" json:"status"`
	StartTime     time.Time  `gorm:"type:datetime;not null;index;comment:开始时间" json:"start_time"`
	EndTime       *time.Time `gorm:"type:datetime;comment:结束时间" json:"end_time"`
	ErrorMessage  string     `gorm:"type:text;comment:错误信息" json:"error_message"`
}

func (InsContractStagingBatch) TableName() string {
	return "ins_contract_staging_batch"
}
```

### 3.2 状态常量定义

```go
// server/model/insbuy/ins_contract_staging_constants.go
package insbuy

const (
	// 数据状态
	StagingStatusPending   = "pending"   // 待确认
	StagingStatusConfirmed = "confirmed" // 已确认
	StagingStatusModified  = "modified"  // 已修改
	StagingStatusRejected  = "rejected"  // 已驳回
	StagingStatusMigrated  = "migrated"  // 已迁移

	// 操作类型
	OperationTypeCreate  = "create"  // 创建
	OperationTypeUpdate  = "update"  // 更新
	OperationTypeConfirm = "confirm" // 确认
	OperationTypeReject  = "reject"  // 驳回
	OperationTypeMigrate = "migrate" // 迁移

	// 批次状态
	BatchStatusProcessing = "processing" // 处理中
	BatchStatusCompleted  = "completed"  // 已完成
	BatchStatusFailed     = "failed"     // 失败
)
```

## 4. 请求响应结构体

### 4.1 请求结构体

```go
// server/model/insbuy/request/ins_contract_staging.go
package request

import (
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// ContractStagingCreateReq 创建中间表数据请求
type ContractStagingCreateReq struct {
	SourceInstanceCode string `json:"source_instance_code" binding:"required" comment:"源实例代码"`
	ApprovalCode       string `json:"approval_code" binding:"required" comment:"审批定义Code"`
}

// ContractStagingUpdateReq 更新中间表数据请求
type ContractStagingUpdateReq struct {
	ID                    uint       `json:"id" binding:"required" comment:"记录ID"`
	PaymentReason         string     `json:"payment_reason" comment:"付款事由"`
	PaymentEntity         string     `json:"payment_entity" comment:"付款主体"`
	CurrentRequestAmount  float64    `json:"current_request_amount" comment:"本次请款金额"`
	TaxRate               float64    `json:"tax_rate" comment:"税率"`
	VatInvoiceType        string     `json:"vat_invoice_type" comment:"增值税发票类型"`
	AccountHolder         string     `json:"account_holder" comment:"收款方户名"`
	AccountNumber         string     `json:"account_number" comment:"账户号码"`
	BankName              string     `json:"bank_name" comment:"银行名称"`
	ExpectedPaymentDate   *time.Time `json:"expected_payment_date" comment:"期望付款日期"`
	Remarks               string     `json:"remarks" comment:"备注"`
	UpdateReason          string     `json:"update_reason" binding:"required" comment:"修改原因"`
}

// ContractStagingConfirmReq 确认数据请求
type ContractStagingConfirmReq struct {
	ID           uint   `json:"id" binding:"required" comment:"记录ID"`
	ConfirmNote  string `json:"confirm_note" comment:"确认备注"`
}

// ContractStagingRejectReq 驳回数据请求
type ContractStagingRejectReq struct {
	ID           uint   `json:"id" binding:"required" comment:"记录ID"`
	RejectReason string `json:"reject_reason" binding:"required" comment:"驳回原因"`
}

// ContractStagingBatchConfirmReq 批量确认请求
type ContractStagingBatchConfirmReq struct {
	IDs         []uint `json:"ids" binding:"required,min=1" comment:"记录ID列表"`
	ConfirmNote string `json:"confirm_note" comment:"确认备注"`
}

// ContractStagingBatchRejectReq 批量驳回请求
type ContractStagingBatchRejectReq struct {
	IDs          []uint `json:"ids" binding:"required,min=1" comment:"记录ID列表"`
	RejectReason string `json:"reject_reason" binding:"required" comment:"驳回原因"`
}

// ContractStagingMigrateReq 数据迁移请求
type ContractStagingMigrateReq struct {
	IDs         []uint `json:"ids" binding:"required,min=1" comment:"记录ID列表"`
	TargetTable string `json:"target_table" binding:"required" comment:"目标表名"`
	MigrateNote string `json:"migrate_note" comment:"迁移备注"`
}

// ContractStagingSearchReq 搜索请求
type ContractStagingSearchReq struct {
	request.PageInfo

	ApprovalCode          string     `json:"approval_code" comment:"审批定义Code"`
	Status                string     `json:"status" comment:"数据状态"`
	InitiatorUserId       string     `json:"initiator_user_id" comment:"发起人用户ID"`
	PaymentEntity         string     `json:"payment_entity" comment:"付款主体"`
	TransformTimeStart    *time.Time `json:"transform_time_start" comment:"转换时间开始"`
	TransformTimeEnd      *time.Time `json:"transform_time_end" comment:"转换时间结束"`
	AmountMin             float64    `json:"amount_min" comment:"最小金额"`
	AmountMax             float64    `json:"amount_max" comment:"最大金额"`
	Keyword               string     `json:"keyword" comment:"关键词搜索"`

	// 排序
	OrderBy   string `json:"order_by" comment:"排序字段"`
	OrderDesc bool   `json:"order_desc" comment:"是否降序"`
}

// ContractStagingHistorySearchReq 历史记录搜索请求
type ContractStagingHistorySearchReq struct {
	request.PageInfo

	StagingId     uint   `json:"staging_id" comment:"中间表ID"`
	OperationType string `json:"operation_type" comment:"操作类型"`
	OperatorId    uint   `json:"operator_id" comment:"操作人ID"`
}
```

### 4.2 响应结构体

```go
// server/model/insbuy/response/ins_contract_staging.go
package response

import (
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
)

// ContractStagingListResp 列表响应
type ContractStagingListResp struct {
	response.PageResult
	List []ContractStagingItem `json:"list"`
}

// ContractStagingItem 列表项
type ContractStagingItem struct {
	ID                   uint       `json:"id"`
	SourceInstanceCode   string     `json:"source_instance_code"`
	ApprovalCode         string     `json:"approval_code"`
	ApprovalName         string     `json:"approval_name"`
	Status               string     `json:"status"`
	StatusText           string     `json:"status_text"`
	ApplicationNumber    string     `json:"application_number"`
	Title                string     `json:"title"`
	InitiatorName        string     `json:"initiator_name"`
	PaymentEntity        string     `json:"payment_entity"`
	CurrentRequestAmount float64    `json:"current_request_amount"`
	PaymentCurrency      string     `json:"payment_currency"`
	TransformTime        time.Time  `json:"transform_time"`
	ConfirmTime          *time.Time `json:"confirm_time"`
	CreatedAt            time.Time  `json:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at"`

	// 统计信息
	AttachmentCount    int `json:"attachment_count"`
	ExpenseDetailCount int `json:"expense_detail_count"`
	HistoryCount       int `json:"history_count"`
}

// ContractStagingDetailResp 详情响应
type ContractStagingDetailResp struct {
	insbuy.InsContractStaging

	// 关联数据
	Attachments    []ContractStagingAttachmentItem `json:"attachments"`
	ExpenseDetails []ContractStagingExpenseItem    `json:"expense_details"`
	Histories      []ContractStagingHistoryItem    `json:"histories"`

	// 扩展信息
	StatusText      string `json:"status_text"`
	ConfirmUserName string `json:"confirm_user_name"`
	CanEdit         bool   `json:"can_edit"`
	CanConfirm      bool   `json:"can_confirm"`
	CanReject       bool   `json:"can_reject"`
	CanMigrate      bool   `json:"can_migrate"`
}

// ContractStagingAttachmentItem 附件项
type ContractStagingAttachmentItem struct {
	ID        uint   `json:"id"`
	FileName  string `json:"file_name"`
	FileUrl   string `json:"file_url"`
	FileSize  int64  `json:"file_size"`
	FileType  string `json:"file_type"`
	SortOrder int    `json:"sort_order"`
}

// ContractStagingExpenseItem 费用明细项
type ContractStagingExpenseItem struct {
	ID               uint       `json:"id"`
	ExpenseType      string     `json:"expense_type"`
	Location         string     `json:"location"`
	DateRange        string     `json:"date_range"`
	StartDate        *time.Time `json:"start_date"`
	EndDate          *time.Time `json:"end_date"`
	VatInvoiceType   string     `json:"vat_invoice_type"`
	Amount           float64    `json:"amount"`
	AmountCurrency   string     `json:"amount_currency"`
	AmountCapital    string     `json:"amount_capital"`
	RowIndex         int        `json:"row_index"`
}

// ContractStagingHistoryItem 历史记录项
type ContractStagingHistoryItem struct {
	ID              uint      `json:"id"`
	OperationType   string    `json:"operation_type"`
	OperationText   string    `json:"operation_text"`
	OperatorName    string    `json:"operator_name"`
	FieldName       string    `json:"field_name"`
	OldValue        string    `json:"old_value"`
	NewValue        string    `json:"new_value"`
	OperationReason string    `json:"operation_reason"`
	CreatedAt       time.Time `json:"created_at"`
}

// ContractStagingBatchResp 批量操作响应
type ContractStagingBatchResp struct {
	BatchNo      string `json:"batch_no"`
	TotalCount   int    `json:"total_count"`
	SuccessCount int    `json:"success_count"`
	FailedCount  int    `json:"failed_count"`
	Status       string `json:"status"`
	Message      string `json:"message"`
}

// ContractStagingStatsResp 统计响应
type ContractStagingStatsResp struct {
	TotalCount     int     `json:"total_count"`
	PendingCount   int     `json:"pending_count"`
	ConfirmedCount int     `json:"confirmed_count"`
	RejectedCount  int     `json:"rejected_count"`
	MigratedCount  int     `json:"migrated_count"`
	TotalAmount    float64 `json:"total_amount"`

	// 按审批类型统计
	ApprovalTypeStats []ApprovalTypeStat `json:"approval_type_stats"`

	// 按状态统计
	StatusStats []StatusStat `json:"status_stats"`

	// 趋势数据
	TrendData []TrendDataItem `json:"trend_data"`
}

// ApprovalTypeStat 审批类型统计
type ApprovalTypeStat struct {
	ApprovalCode string  `json:"approval_code"`
	ApprovalName string  `json:"approval_name"`
	Count        int     `json:"count"`
	Amount       float64 `json:"amount"`
}

// StatusStat 状态统计
type StatusStat struct {
	Status     string  `json:"status"`
	StatusText string  `json:"status_text"`
	Count      int     `json:"count"`
	Amount     float64 `json:"amount"`
}

// TrendDataItem 趋势数据项
type TrendDataItem struct {
	Date   string  `json:"date"`
	Count  int     `json:"count"`
	Amount float64 `json:"amount"`
}
```

## 5. API接口设计

### 5.1 API控制器

```go
// server/api/v1/insbuy/ins_contract_staging.go
package insbuy

import (
	"strconv"
	"github.com/gin-gonic/gin"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
)

type ContractStagingApi struct{}

var contractStagingService = service.ServiceGroupApp.InsbuyServiceGroup.ContractStagingService

// CreateFromTransform 从转换结果创建中间表数据
// @Tags      ContractStaging
// @Summary   从转换结果创建中间表数据
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.ContractStagingCreateReq  true  "创建请求"
// @Success   200   {object}  response.Response{msg=string}  "创建成功"
// @Router    /contract-staging/create [post]
func (api *ContractStagingApi) CreateFromTransform(c *gin.Context) {
	var req insbuyReq.ContractStagingCreateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = contractStagingService.CreateFromTransform(&req)
	if err != nil {
		global.GVA_LOG.Error("创建中间表数据失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// GetList 获取中间表数据列表
// @Tags      ContractStaging
// @Summary   获取中间表数据列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.ContractStagingSearchReq  true  "搜索条件"
// @Success   200   {object}  response.Response{data=insbuyResp.ContractStagingListResp}  "获取成功"
// @Router    /contract-staging/list [post]
func (api *ContractStagingApi) GetList(c *gin.Context) {
	var req insbuyReq.ContractStagingSearchReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := contractStagingService.GetList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetDetail 获取详情
// @Tags      ContractStaging
// @Summary   获取详情
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     id   path      int  true  "记录ID"
// @Success   200  {object}  response.Response{data=insbuyResp.ContractStagingDetailResp}  "获取成功"
// @Router    /contract-staging/detail/{id} [get]
func (api *ContractStagingApi) GetDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	detail, err := contractStagingService.GetDetail(uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取详情失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	response.OkWithData(detail, c)
}

// Update 更新数据
// @Tags      ContractStaging
// @Summary   更新数据
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.ContractStagingUpdateReq  true  "更新请求"
// @Success   200   {object}  response.Response{msg=string}  "更新成功"
// @Router    /contract-staging/update [put]
func (api *ContractStagingApi) Update(c *gin.Context) {
	var req insbuyReq.ContractStagingUpdateReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = contractStagingService.Update(&req, c)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// Confirm 确认数据
// @Tags      ContractStaging
// @Summary   确认数据
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.ContractStagingConfirmReq  true  "确认请求"
// @Success   200   {object}  response.Response{msg=string}  "确认成功"
// @Router    /contract-staging/confirm [post]
func (api *ContractStagingApi) Confirm(c *gin.Context) {
	var req insbuyReq.ContractStagingConfirmReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = contractStagingService.Confirm(&req, c)
	if err != nil {
		global.GVA_LOG.Error("确认失败!", zap.Error(err))
		response.FailWithMessage("确认失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("确认成功", c)
}

// BatchConfirm 批量确认
// @Tags      ContractStaging
// @Summary   批量确认
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.ContractStagingBatchConfirmReq  true  "批量确认请求"
// @Success   200   {object}  response.Response{data=insbuyResp.ContractStagingBatchResp}  "操作成功"
// @Router    /contract-staging/batch-confirm [post]
func (api *ContractStagingApi) BatchConfirm(c *gin.Context) {
	var req insbuyReq.ContractStagingBatchConfirmReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	result, err := contractStagingService.BatchConfirm(&req, c)
	if err != nil {
		global.GVA_LOG.Error("批量确认失败!", zap.Error(err))
		response.FailWithMessage("批量确认失败:"+err.Error(), c)
		return
	}
	response.OkWithData(result, c)
}
```

### 5.2 路由配置

```go
// server/router/insbuy/ins_contract_staging.go
package insbuy

import (
	"github.com/gin-gonic/gin"
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
)

type ContractStagingRouter struct{}

func (r *ContractStagingRouter) InitContractStagingRouter(Router *gin.RouterGroup) {
	contractStagingRouter := Router.Group("contract-staging").Use(middleware.OperationRecord())
	contractStagingRouterWithoutRecord := Router.Group("contract-staging")
	contractStagingApi := v1.ApiGroupApp.InsbuyApiGroup.ContractStagingApi

	{
		// 需要记录操作日志的接口
		contractStagingRouter.POST("create", contractStagingApi.CreateFromTransform)     // 创建
		contractStagingRouter.PUT("update", contractStagingApi.Update)                   // 更新
		contractStagingRouter.POST("confirm", contractStagingApi.Confirm)                // 确认
		contractStagingRouter.POST("reject", contractStagingApi.Reject)                  // 驳回
		contractStagingRouter.POST("batch-confirm", contractStagingApi.BatchConfirm)     // 批量确认
		contractStagingRouter.POST("batch-reject", contractStagingApi.BatchReject)       // 批量驳回
		contractStagingRouter.POST("migrate", contractStagingApi.Migrate)                // 数据迁移
	}

	{
		// 不需要记录操作日志的接口
		contractStagingRouterWithoutRecord.POST("list", contractStagingApi.GetList)         // 列表
		contractStagingRouterWithoutRecord.GET("detail/:id", contractStagingApi.GetDetail)  // 详情
		contractStagingRouterWithoutRecord.GET("stats", contractStagingApi.GetStats)        // 统计
		contractStagingRouterWithoutRecord.POST("history", contractStagingApi.GetHistory)   // 历史记录
	}
}
```

## 6. 数据流程图

### 6.1 整体数据流程

```mermaid
graph TD
    A[飞书系统] --> B[数据拉取服务]
    B --> C[ContractTransformer]
    C --> D[StandardContractData]
    D --> E[中间表 ins_contract_staging]

    E --> F{数据状态}
    F -->|pending| G[业务人员审核]
    F -->|confirmed| H[数据迁移]
    F -->|rejected| I[重新处理]
    F -->|modified| J[修改后确认]

    G --> K{审核结果}
    K -->|通过| L[状态更新为confirmed]
    K -->|驳回| M[状态更新为rejected]
    K -->|需修改| N[状态更新为modified]

    L --> H
    J --> H
    H --> O[最终统计报表表]

    P[修改历史表] --> Q[操作审计]
    E --> P
    G --> P
    H --> P
```

### 6.2 批量操作流程

```mermaid
graph TD
    A[批量操作请求] --> B[创建批次记录]
    B --> C[生成批次号]
    C --> D[开始处理]

    D --> E{遍历记录}
    E --> F[处理单条记录]
    F --> G{处理结果}
    G -->|成功| H[成功计数+1]
    G -->|失败| I[失败计数+1]

    H --> J{是否还有记录}
    I --> J
    J -->|是| E
    J -->|否| K[更新批次状态]

    K --> L[返回批次结果]
```

## 7. 服务层核心方法

### 7.1 服务接口定义

```go
// server/service/insbuy/ins_contract_staging.go
package insbuy

import (
	"context"
	"fmt"
	"time"
	"github.com/gin-gonic/gin"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ContractStagingService struct{}

// CreateFromTransform 从转换结果创建中间表数据
func (s *ContractStagingService) CreateFromTransform(req *insbuyReq.ContractStagingCreateReq) error {
	// 1. 调用ContractTransformer进行数据转换
	transformer := NewContractTransformer()

	// 2. 从数据库查询原始合同数据
	contract, err := s.getContractByInstanceCode(req.SourceInstanceCode)
	if err != nil {
		return fmt.Errorf("查询原始合同数据失败: %w", err)
	}

	// 3. 转换为ContractDetails格式
	contractDetails := s.convertToContractDetails(contract)

	// 4. 执行数据转换
	result, err := transformer.Transform(context.Background(), contractDetails)
	if err != nil {
		return fmt.Errorf("数据转换失败: %w", err)
	}

	// 5. 将转换结果保存到中间表
	staging := s.buildStagingFromResult(result.Data, req.SourceInstanceCode)

	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 保存主记录
		if err := tx.Create(&staging).Error; err != nil {
			return err
		}

		// 保存附件记录
		if len(result.Data.Attachments) > 0 {
			attachments := s.buildAttachmentsFromResult(staging.ID, result.Data.Attachments)
			if err := tx.Create(&attachments).Error; err != nil {
				return err
			}
		}

		// 保存费用明细记录
		if len(result.Data.ExpenseDetails) > 0 {
			expenses := s.buildExpensesFromResult(staging.ID, result.Data.ExpenseDetails)
			if err := tx.Create(&expenses).Error; err != nil {
				return err
			}
		}

		// 记录创建历史
		history := &insbuy.InsContractStagingHistory{
			StagingId:       staging.ID,
			OperationType:   insbuy.OperationTypeCreate,
			OperatorId:      1, // TODO: 从上下文获取用户ID
			OperatorName:    "系统", // TODO: 从上下文获取用户名
			OperationReason: "数据转换创建",
		}
		return tx.Create(history).Error
	})
}

// GetList 获取列表
func (s *ContractStagingService) GetList(req *insbuyReq.ContractStagingSearchReq) ([]insbuyResp.ContractStagingItem, int64, error) {
	var list []insbuyResp.ContractStagingItem
	var total int64

	db := global.GVA_DB.Model(&insbuy.InsContractStaging{})

	// 构建查询条件
	if req.ApprovalCode != "" {
		db = db.Where("approval_code = ?", req.ApprovalCode)
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.InitiatorUserId != "" {
		db = db.Where("initiator_user_id = ?", req.InitiatorUserId)
	}
	if req.PaymentEntity != "" {
		db = db.Where("payment_entity LIKE ?", "%"+req.PaymentEntity+"%")
	}
	if req.TransformTimeStart != nil {
		db = db.Where("transform_time >= ?", req.TransformTimeStart)
	}
	if req.TransformTimeEnd != nil {
		db = db.Where("transform_time <= ?", req.TransformTimeEnd)
	}
	if req.AmountMin > 0 {
		db = db.Where("current_request_amount >= ?", req.AmountMin)
	}
	if req.AmountMax > 0 {
		db = db.Where("current_request_amount <= ?", req.AmountMax)
	}
	if req.Keyword != "" {
		db = db.Where("title LIKE ? OR application_number LIKE ? OR payment_reason LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 排序
	orderBy := "created_at"
	if req.OrderBy != "" {
		orderBy = req.OrderBy
	}
	if req.OrderDesc {
		orderBy += " DESC"
	}
	db = db.Order(orderBy)

	// 分页
	offset := (req.Page - 1) * req.PageSize
	db = db.Offset(offset).Limit(req.PageSize)

	// 查询数据
	var stagings []insbuy.InsContractStaging
	if err := db.Find(&stagings).Error; err != nil {
		return nil, 0, err
	}

	// 转换为响应格式
	for _, staging := range stagings {
		item := insbuyResp.ContractStagingItem{
			ID:                   staging.ID,
			SourceInstanceCode:   staging.SourceInstanceCode,
			ApprovalCode:         staging.ApprovalCode,
			ApprovalName:         staging.ApprovalName,
			Status:               staging.Status,
			StatusText:           s.getStatusText(staging.Status),
			ApplicationNumber:    staging.ApplicationNumber,
			Title:                staging.Title,
			InitiatorName:        staging.InitiatorName,
			PaymentEntity:        staging.PaymentEntity,
			CurrentRequestAmount: staging.CurrentRequestAmount,
			PaymentCurrency:      staging.PaymentCurrency,
			TransformTime:        staging.TransformTime,
			ConfirmTime:          staging.ConfirmTime,
			CreatedAt:            staging.CreatedAt,
			UpdatedAt:            staging.UpdatedAt,
		}

		// 获取统计信息
		item.AttachmentCount = s.getAttachmentCount(staging.ID)
		item.ExpenseDetailCount = s.getExpenseDetailCount(staging.ID)
		item.HistoryCount = s.getHistoryCount(staging.ID)

		list = append(list, item)
	}

	return list, total, nil
}
```

## 8. 数据库迁移脚本

### 8.1 创建迁移文件

```sql
-- migrations/20250105_create_contract_staging_tables.sql

-- 创建中间表
CREATE TABLE `ins_contract_staging` (
  -- 表结构见上文 2.1 节
);

-- 创建附件表
CREATE TABLE `ins_contract_staging_attachment` (
  -- 表结构见上文 2.2 节
);

-- 创建费用明细表
CREATE TABLE `ins_contract_staging_expense` (
  -- 表结构见上文 2.3 节
);

-- 创建修改历史表
CREATE TABLE `ins_contract_staging_history` (
  -- 表结构见上文 2.4 节
);

-- 创建批量操作表
CREATE TABLE `ins_contract_staging_batch` (
  -- 表结构见上文 2.5 节
);
```

### 8.2 GORM自动迁移

```go
// server/initialize/gorm_mysql.go 中添加
func initInsbuyTables(db *gorm.DB) error {
	return db.AutoMigrate(
		// 现有表...

		// 新增中间表
		&insbuy.InsContractStaging{},
		&insbuy.InsContractStagingAttachment{},
		&insbuy.InsContractStagingExpense{},
		&insbuy.InsContractStagingHistory{},
		&insbuy.InsContractStagingBatch{},
	)
}
```

## 9. 实施步骤和时间规划

### 9.1 第一阶段：基础架构搭建（3-4天）

**目标**: 完成数据库表结构和基础模型定义

**任务清单**:
- [x] 设计数据库表结构
- [ ] 创建Go模型定义
- [ ] 编写数据库迁移脚本
- [ ] 创建基础的CRUD操作
- [ ] 单元测试编写

**交付物**:
- 数据库表创建脚本
- Go模型文件
- 基础服务层代码
- 单元测试用例

### 9.2 第二阶段：核心功能开发（5-6天）

**目标**: 实现数据转换、状态管理和基础API

**任务清单**:
- [ ] 集成ContractTransformer服务
- [ ] 实现数据转换和存储逻辑
- [ ] 开发状态管理功能
- [ ] 实现单条确认/驳回功能
- [ ] 开发修改历史记录功能
- [ ] API接口开发和测试

**交付物**:
- 数据转换服务
- 状态管理服务
- REST API接口
- API文档

### 9.3 第三阶段：批量操作和高级功能（4-5天）

**目标**: 实现批量操作、数据迁移和统计功能

**任务清单**:
- [ ] 开发批量确认/驳回功能
- [ ] 实现数据迁移机制
- [ ] 开发统计和报表功能
- [ ] 实现数据回滚功能
- [ ] 性能优化和并发处理

**交付物**:
- 批量操作服务
- 数据迁移服务
- 统计报表功能
- 性能测试报告

### 9.4 第四阶段：前端界面开发（6-7天）

**目标**: 开发用户界面和交互功能

**任务清单**:
- [ ] 数据列表页面开发
- [ ] 详情页面和编辑功能
- [ ] 批量操作界面
- [ ] 统计仪表板
- [ ] 历史记录查看
- [ ] 用户权限控制

**交付物**:
- Vue.js前端页面
- 用户交互界面
- 权限控制系统

### 9.5 第五阶段：测试和部署（3-4天）

**目标**: 完成系统测试和生产部署

**任务清单**:
- [ ] 集成测试
- [ ] 性能测试
- [ ] 安全测试
- [ ] 用户验收测试
- [ ] 生产环境部署
- [ ] 监控和日志配置

**交付物**:
- 测试报告
- 部署文档
- 运维手册
- 用户使用手册

## 10. 风险评估和应对措施

### 10.1 技术风险

**风险**: 数据转换复杂度高，可能出现数据丢失或错误
**应对**:
- 实施严格的数据验证机制
- 建立完整的错误日志和回滚机制
- 进行充分的单元测试和集成测试

**风险**: 批量操作性能问题
**应对**:
- 实施分批处理机制
- 使用异步处理和队列
- 添加进度监控和取消机制

### 10.2 业务风险

**风险**: 业务规则变更频繁
**应对**:
- 设计灵活的配置机制
- 实施版本控制和向后兼容
- 建立快速响应机制

**风险**: 用户接受度问题
**应对**:
- 提供详细的用户培训
- 设计直观的用户界面
- 建立用户反馈机制

## 11. 监控和维护

### 11.1 系统监控

- 数据转换成功率监控
- API响应时间监控
- 数据库性能监控
- 错误率和异常监控

### 11.2 数据质量监控

- 数据完整性检查
- 数据一致性验证
- 业务规则合规性检查
- 数据时效性监控

### 11.3 运维支持

- 自动化部署流程
- 数据备份和恢复
- 系统健康检查
- 性能调优建议

## 12. 总结

本技术方案提供了一个完整的合同审批数据中间表解决方案，包括：

1. **完整的数据模型设计** - 支持多种审批类型和复杂业务场景
2. **灵活的状态管理** - 支持数据的全生命周期管理
3. **强大的批量操作** - 提高业务处理效率
4. **完善的审计机制** - 确保数据变更的可追溯性
5. **可扩展的架构设计** - 支持未来业务需求的扩展

通过分阶段实施，可以确保项目的稳定推进和质量保证。预计总开发周期为21-26个工作日，可以根据实际情况调整时间安排。
```
