# 飞书合同数据转换和审核系统设计方案

## 1. 项目概述

### 1.1 背景
基于现有的飞书合同数据表（`ins_contract`），设计一个完整的数据转换和审核流程，将原始合同数据转换为标准化的23个字段格式，经过人工审核后，以与现有Excel导入功能相同的格式存储到 `SourceStorageData` 中。

### 1.2 核心需求
- **数据源**：从 `ins_contract` 表读取飞书合同数据
- **转换目标**：转换为23个标准字段的 `RegionalExpenseDetailItem` 格式
- **明细拆分**：当原始数据包含费用明细时，拆分为多条记录
- **审核流程**：提供二次确认和人工审核机制
- **存储集成**：复用现有的 `SourceStorageData` 存储机制

## 2. 数据库表结构设计

### 2.1 主表：ins_contract_transform_staging（合同转换中间表）

```sql
CREATE TABLE `ins_contract_transform_staging` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(3) DEFAULT NULL COMMENT '删除时间',
  `created_by` bigint unsigned DEFAULT NULL COMMENT '创建人',
  `updated_by` bigint unsigned DEFAULT NULL COMMENT '更新人',
  
  -- 源数据关联
  `source_contract_id` bigint unsigned NOT NULL COMMENT '源合同ID',
  `source_instance_code` varchar(100) NOT NULL COMMENT '源实例代码',
  `source_approval_code` varchar(100) NOT NULL COMMENT '源审批代码',
  `detail_index` int NOT NULL DEFAULT 0 COMMENT '明细索引（0表示主记录，>0表示明细记录）',
  `detail_total` int NOT NULL DEFAULT 1 COMMENT '明细总数',
  
  -- 转换状态
  `transform_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '转换状态：pending-待转换，transformed-已转换，failed-转换失败',
  `transform_time` datetime DEFAULT NULL COMMENT '转换时间',
  `transform_error` text DEFAULT NULL COMMENT '转换错误信息',
  
  -- 审核状态
  `audit_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '审核状态：pending-待审核，approved-审核通过，rejected-审核驳回，modified-已修改',
  `auditor_id` bigint unsigned DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_note` text DEFAULT NULL COMMENT '审核备注',
  
  -- 存储状态
  `storage_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '存储状态：pending-待存储，stored-已存储，failed-存储失败',
  `storage_time` datetime DEFAULT NULL COMMENT '存储时间',
  `storage_error` text DEFAULT NULL COMMENT '存储错误信息',
  
  -- 转换后的标准化字段（23个核心字段，与RegionalExpenseDetailItem保持一致）
  `serial_number` int DEFAULT NULL COMMENT '序号',
  `completion_time` varchar(50) DEFAULT NULL COMMENT '完成时间',
  `report_month` varchar(20) DEFAULT NULL COMMENT '管报月份',
  `title` varchar(500) DEFAULT NULL COMMENT '标题',
  `payment_entity` varchar(200) DEFAULT NULL COMMENT '付款主体',
  `payment_reason` varchar(500) DEFAULT NULL COMMENT '付款事由',
  `business_type` varchar(100) DEFAULT NULL COMMENT '业务类型',
  `contract_amount` varchar(50) DEFAULT NULL COMMENT '合同签约金额',
  `contract_paid_amount` varchar(50) DEFAULT NULL COMMENT '合同已付金额',
  `current_request_amount` varchar(50) DEFAULT NULL COMMENT '本次请款金额',
  `pending_amount` varchar(50) DEFAULT NULL COMMENT '待付款金额',
  `report_confirm_amount` varchar(50) DEFAULT NULL COMMENT '管报确认金额',
  `tax_rate` varchar(20) DEFAULT NULL COMMENT '税率',
  `amount_exclude_tax` varchar(50) DEFAULT NULL COMMENT '不含税金额',
  `expense_category` varchar(100) DEFAULT NULL COMMENT '费用类别',
  `include_in_report` varchar(10) DEFAULT NULL COMMENT '是否纳入管报',
  `account_name` varchar(200) DEFAULT NULL COMMENT '户名',
  `report_entity` varchar(200) DEFAULT NULL COMMENT '管报主体',
  `report_entity_detail` varchar(200) DEFAULT NULL COMMENT '管报主体明细',
  `report_region` varchar(100) DEFAULT NULL COMMENT '管报区域',
  `initiator_name` varchar(100) DEFAULT NULL COMMENT '发起人姓名',
  `initiator_department` varchar(200) DEFAULT NULL COMMENT '发起人部门',
  `department` varchar(200) DEFAULT NULL COMMENT '部门',
  
  -- 明细相关字段
  `expense_type` varchar(100) DEFAULT NULL COMMENT '费用类型（明细字段）',
  `expense_location` varchar(200) DEFAULT NULL COMMENT '费用地点（明细字段）',
  `expense_date_range` varchar(100) DEFAULT NULL COMMENT '费用日期区间（明细字段）',
  `expense_start_date` datetime DEFAULT NULL COMMENT '费用开始日期（明细字段）',
  `expense_end_date` datetime DEFAULT NULL COMMENT '费用结束日期（明细字段）',
  `invoice_type` varchar(50) DEFAULT NULL COMMENT '发票类型（明细字段）',
  
  -- 原始数据备份
  `original_form_data` json DEFAULT NULL COMMENT '原始表单数据',
  
  -- 索引
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_source_detail` (`source_contract_id`, `detail_index`),
  KEY `idx_source_instance` (`source_instance_code`),
  KEY `idx_source_approval` (`source_approval_code`),
  KEY `idx_transform_status` (`transform_status`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_storage_status` (`storage_status`),
  KEY `idx_completion_time` (`completion_time`),
  KEY `idx_report_month` (`report_month`),
  CONSTRAINT `fk_source_contract_transform` FOREIGN KEY (`source_contract_id`) REFERENCES `ins_contract` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同转换中间表';
```

### 2.2 批次处理表：ins_contract_transform_batch（批量转换记录表）

```sql
CREATE TABLE `ins_contract_transform_batch` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT NULL COMMENT '更新时间',
  
  `batch_no` varchar(50) NOT NULL COMMENT '批次号',
  `batch_name` varchar(200) DEFAULT NULL COMMENT '批次名称',
  `approval_code` varchar(100) DEFAULT NULL COMMENT '审批代码（为空表示全部）',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型：transform-转换，audit-审核，storage-存储',
  `total_count` int NOT NULL DEFAULT 0 COMMENT '总记录数',
  `processed_count` int NOT NULL DEFAULT 0 COMMENT '已处理记录数',
  `success_count` int NOT NULL DEFAULT 0 COMMENT '成功记录数',
  `failed_count` int NOT NULL DEFAULT 0 COMMENT '失败记录数',
  `detail_records_created` int NOT NULL DEFAULT 0 COMMENT '创建的明细记录数',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '批次状态：pending-待处理，processing-处理中，completed-已完成，failed-失败',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `operator_id` bigint unsigned NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `config_params` json DEFAULT NULL COMMENT '配置参数',
  `summary_report` json DEFAULT NULL COMMENT '汇总报告',
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_approval_code` (`approval_code`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_status` (`status`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批量转换记录表';
```

### 2.3 操作日志表：ins_contract_transform_log（转换操作日志表）

```sql
CREATE TABLE `ins_contract_transform_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` datetime(3) DEFAULT NULL COMMENT '创建时间',
  
  `batch_no` varchar(50) DEFAULT NULL COMMENT '批次号',
  `staging_id` bigint unsigned NOT NULL COMMENT '中间表ID',
  `source_contract_id` bigint unsigned NOT NULL COMMENT '源合同ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型：transform-转换，audit-审核，modify-修改，storage-存储',
  `operation_status` varchar(20) NOT NULL COMMENT '操作状态：success-成功，failed-失败，warning-警告',
  `processing_time_ms` int DEFAULT 0 COMMENT '处理耗时（毫秒）',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `warning_message` text DEFAULT NULL COMMENT '警告信息',
  `operator_id` bigint unsigned DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `before_data` json DEFAULT NULL COMMENT '操作前数据',
  `after_data` json DEFAULT NULL COMMENT '操作后数据',
  
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_staging_id` (`staging_id`),
  KEY `idx_source_contract` (`source_contract_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_status` (`operation_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='转换操作日志表';
```

## 3. Go模型定义

### 3.1 主模型：InsContractTransformStaging

```go
// server/model/insbuy/ins_contract_transform_staging.go
package insbuy

import (
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// InsContractTransformStaging 合同转换中间表
type InsContractTransformStaging struct {
	global.GVA_MODEL
	
	// 源数据关联
	SourceContractId   uint   `gorm:"not null;index;comment:源合同ID" json:"source_contract_id"`
	SourceInstanceCode string `gorm:"type:varchar(100);not null;index;comment:源实例代码" json:"source_instance_code"`
	SourceApprovalCode string `gorm:"type:varchar(100);not null;index;comment:源审批代码" json:"source_approval_code"`
	DetailIndex        int    `gorm:"not null;default:0;comment:明细索引" json:"detail_index"`
	DetailTotal        int    `gorm:"not null;default:1;comment:明细总数" json:"detail_total"`
	
	// 转换状态
	TransformStatus string     `gorm:"type:varchar(20);not null;default:'pending';index;comment:转换状态" json:"transform_status"`
	TransformTime   *time.Time `gorm:"type:datetime;comment:转换时间" json:"transform_time"`
	TransformError  string     `gorm:"type:text;comment:转换错误信息" json:"transform_error"`
	
	// 审核状态
	AuditStatus string     `gorm:"type:varchar(20);not null;default:'pending';index;comment:审核状态" json:"audit_status"`
	AuditorId   *uint      `gorm:"comment:审核人ID" json:"auditor_id"`
	AuditTime   *time.Time `gorm:"type:datetime;comment:审核时间" json:"audit_time"`
	AuditNote   string     `gorm:"type:text;comment:审核备注" json:"audit_note"`
	
	// 存储状态
	StorageStatus string     `gorm:"type:varchar(20);not null;default:'pending';index;comment:存储状态" json:"storage_status"`
	StorageTime   *time.Time `gorm:"type:datetime;comment:存储时间" json:"storage_time"`
	StorageError  string     `gorm:"type:text;comment:存储错误信息" json:"storage_error"`
	
	// 转换后的标准化字段（与RegionalExpenseDetailItem保持一致）
	SerialNumber         int    `gorm:"comment:序号" json:"serial_number"`
	CompletionTime       string `gorm:"type:varchar(50);comment:完成时间" json:"completion_time"`
	ReportMonth          string `gorm:"type:varchar(20);index;comment:管报月份" json:"report_month"`
	Title                string `gorm:"type:varchar(500);comment:标题" json:"title"`
	PaymentEntity        string `gorm:"type:varchar(200);comment:付款主体" json:"payment_entity"`
	PaymentReason        string `gorm:"type:varchar(500);comment:付款事由" json:"payment_reason"`
	BusinessType         string `gorm:"type:varchar(100);comment:业务类型" json:"business_type"`
	ContractAmount       string `gorm:"type:varchar(50);comment:合同签约金额" json:"contract_amount"`
	ContractPaidAmount   string `gorm:"type:varchar(50);comment:合同已付金额" json:"contract_paid_amount"`
	CurrentRequestAmount string `gorm:"type:varchar(50);comment:本次请款金额" json:"current_request_amount"`
	PendingAmount        string `gorm:"type:varchar(50);comment:待付款金额" json:"pending_amount"`
	ReportConfirmAmount  string `gorm:"type:varchar(50);comment:管报确认金额" json:"report_confirm_amount"`
	TaxRate              string `gorm:"type:varchar(20);comment:税率" json:"tax_rate"`
	AmountExcludeTax     string `gorm:"type:varchar(50);comment:不含税金额" json:"amount_exclude_tax"`
	ExpenseCategory      string `gorm:"type:varchar(100);comment:费用类别" json:"expense_category"`
	IncludeInReport      string `gorm:"type:varchar(10);comment:是否纳入管报" json:"include_in_report"`
	AccountName          string `gorm:"type:varchar(200);comment:户名" json:"account_name"`
	ReportEntity         string `gorm:"type:varchar(200);comment:管报主体" json:"report_entity"`
	ReportEntityDetail   string `gorm:"type:varchar(200);comment:管报主体明细" json:"report_entity_detail"`
	ReportRegion         string `gorm:"type:varchar(100);comment:管报区域" json:"report_region"`
	InitiatorName        string `gorm:"type:varchar(100);comment:发起人姓名" json:"initiator_name"`
	InitiatorDepartment  string `gorm:"type:varchar(200);comment:发起人部门" json:"initiator_department"`
	Department           string `gorm:"type:varchar(200);comment:部门" json:"department"`
	
	// 明细相关字段
	ExpenseType      string     `gorm:"type:varchar(100);comment:费用类型" json:"expense_type"`
	ExpenseLocation  string     `gorm:"type:varchar(200);comment:费用地点" json:"expense_location"`
	ExpenseDateRange string     `gorm:"type:varchar(100);comment:费用日期区间" json:"expense_date_range"`
	ExpenseStartDate *time.Time `gorm:"type:datetime;comment:费用开始日期" json:"expense_start_date"`
	ExpenseEndDate   *time.Time `gorm:"type:datetime;comment:费用结束日期" json:"expense_end_date"`
	InvoiceType      string     `gorm:"type:varchar(50);comment:发票类型" json:"invoice_type"`
	
	// 原始数据备份
	OriginalFormData datatypes.JSON `gorm:"type:json;comment:原始表单数据" json:"original_form_data"`
	
	// 关联关系
	SourceContract *InsContract `gorm:"foreignKey:SourceContractId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"source_contract"`
}

func (InsContractTransformStaging) TableName() string {
	return "ins_contract_transform_staging"
}

func (InsContractTransformStaging) TableComment() string {
	return "合同转换中间表"
}

// ToRegionalExpenseDetailItem 转换为RegionalExpenseDetailItem格式
func (s *InsContractTransformStaging) ToRegionalExpenseDetailItem() RegionalExpenseDetailItem {
	return RegionalExpenseDetailItem{
		SerialNumber:         s.SerialNumber,
		CompletionTime:       s.CompletionTime,
		ReportMonth:          s.ReportMonth,
		Title:                s.Title,
		PaymentEntity:        s.PaymentEntity,
		PaymentReason:        s.PaymentReason,
		BusinessType:         s.BusinessType,
		ContractAmount:       s.ContractAmount,
		ContractPaidAmount:   s.ContractPaidAmount,
		CurrentRequestAmount: s.CurrentRequestAmount,
		PendingAmount:        s.PendingAmount,
		ReportConfirmAmount:  s.ReportConfirmAmount,
		TaxRate:              s.TaxRate,
		AmountExcludeTax:     s.AmountExcludeTax,
		ExpenseCategory:      s.ExpenseCategory,
		IncludeInReport:      s.IncludeInReport,
		AccountName:          s.AccountName,
		ReportEntity:         s.ReportEntity,
		ReportEntityDetail:   s.ReportEntityDetail,
		ReportRegion:         s.ReportRegion,
		InitiatorName:        s.InitiatorName,
		InitiatorDepartment:  s.InitiatorDepartment,
		Department:           s.Department,
		StoreId:              0, // 统一设为总部ID
	}
}
```

### 3.2 批次和日志模型

```go
// InsContractTransformBatch 批量转换记录表
type InsContractTransformBatch struct {
	global.GVA_MODEL

	BatchNo              string          `gorm:"type:varchar(50);not null;unique;comment:批次号" json:"batch_no"`
	BatchName            string          `gorm:"type:varchar(200);comment:批次名称" json:"batch_name"`
	ApprovalCode         string          `gorm:"type:varchar(100);index;comment:审批代码" json:"approval_code"`
	OperationType        string          `gorm:"type:varchar(20);not null;index;comment:操作类型" json:"operation_type"`
	TotalCount           int             `gorm:"not null;default:0;comment:总记录数" json:"total_count"`
	ProcessedCount       int             `gorm:"not null;default:0;comment:已处理记录数" json:"processed_count"`
	SuccessCount         int             `gorm:"not null;default:0;comment:成功记录数" json:"success_count"`
	FailedCount          int             `gorm:"not null;default:0;comment:失败记录数" json:"failed_count"`
	DetailRecordsCreated int             `gorm:"not null;default:0;comment:创建的明细记录数" json:"detail_records_created"`
	Status               string          `gorm:"type:varchar(20);not null;default:'pending';index;comment:批次状态" json:"status"`
	StartTime            *time.Time      `gorm:"type:datetime;index;comment:开始时间" json:"start_time"`
	EndTime              *time.Time      `gorm:"type:datetime;comment:结束时间" json:"end_time"`
	OperatorId           uint            `gorm:"not null;index;comment:操作人ID" json:"operator_id"`
	OperatorName         string          `gorm:"type:varchar(100);not null;comment:操作人姓名" json:"operator_name"`
	ConfigParams         datatypes.JSON  `gorm:"type:json;comment:配置参数" json:"config_params"`
	SummaryReport        datatypes.JSON  `gorm:"type:json;comment:汇总报告" json:"summary_report"`
}

func (InsContractTransformBatch) TableName() string {
	return "ins_contract_transform_batch"
}

// InsContractTransformLog 转换操作日志表
type InsContractTransformLog struct {
	global.GVA_MODEL

	BatchNo           string          `gorm:"type:varchar(50);index;comment:批次号" json:"batch_no"`
	StagingId         uint            `gorm:"not null;index;comment:中间表ID" json:"staging_id"`
	SourceContractId  uint            `gorm:"not null;index;comment:源合同ID" json:"source_contract_id"`
	OperationType     string          `gorm:"type:varchar(50);not null;index;comment:操作类型" json:"operation_type"`
	OperationStatus   string          `gorm:"type:varchar(20);not null;index;comment:操作状态" json:"operation_status"`
	ProcessingTimeMs  int             `gorm:"default:0;comment:处理耗时" json:"processing_time_ms"`
	ErrorMessage      string          `gorm:"type:text;comment:错误信息" json:"error_message"`
	WarningMessage    string          `gorm:"type:text;comment:警告信息" json:"warning_message"`
	OperatorId        *uint           `gorm:"comment:操作人ID" json:"operator_id"`
	OperatorName      string          `gorm:"type:varchar(100);comment:操作人姓名" json:"operator_name"`
	BeforeData        datatypes.JSON  `gorm:"type:json;comment:操作前数据" json:"before_data"`
	AfterData         datatypes.JSON  `gorm:"type:json;comment:操作后数据" json:"after_data"`
}

func (InsContractTransformLog) TableName() string {
	return "ins_contract_transform_log"
}
```

## 4. 核心转换服务

### 4.1 数据转换服务

```go
// server/service/insbuy/ins_contract_transform.go
package insbuy

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/datasource"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ContractTransformService struct{}

// TransformResult 转换结果
type TransformResult struct {
	Success       bool                                  `json:"success"`
	MainRecord    *insbuy.InsContractTransformStaging   `json:"main_record"`
	DetailRecords []*insbuy.InsContractTransformStaging `json:"detail_records"`
	Errors        []string                              `json:"errors"`
	Warnings      []string                              `json:"warnings"`
}

// TransformContract 转换单个合同数据
func (s *ContractTransformService) TransformContract(contractId uint) (*TransformResult, error) {
	startTime := time.Now()

	// 1. 查询源合同数据
	contract, err := s.getContractById(contractId)
	if err != nil {
		return nil, fmt.Errorf("查询源合同失败: %w", err)
	}

	// 2. 解析表单数据
	formData, err := s.parseFormData(contract.Form)
	if err != nil {
		return &TransformResult{
			Success: false,
			Errors:  []string{fmt.Sprintf("解析表单数据失败: %v", err)},
		}, nil
	}

	// 3. 检查是否有明细数据
	detailData := s.extractExpenseDetails(formData)

	if len(detailData) == 0 {
		// 无明细，处理单条记录
		return s.transformSingleRecord(contract, formData, startTime)
	} else {
		// 有明细，拆分处理
		return s.transformWithDetails(contract, formData, detailData, startTime)
	}
}

// transformSingleRecord 转换单条记录（无明细）
func (s *ContractTransformService) transformSingleRecord(contract *insbuy.InsContract, formData map[string]interface{}, startTime time.Time) (*TransformResult, error) {
	result := &TransformResult{
		Success:  true,
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// 创建转换记录
	staging := &insbuy.InsContractTransformStaging{
		SourceContractId:   contract.ID,
		SourceInstanceCode: contract.InstanceCode,
		SourceApprovalCode: contract.ApprovalCode,
		DetailIndex:        0,
		DetailTotal:        1,
		TransformStatus:    "transformed",
		TransformTime:      &startTime,
		OriginalFormData:   contract.Form,
	}

	// 映射基础字段
	s.mapBaseFields(staging, contract, formData)

	// 映射业务字段
	s.mapBusinessFields(staging, formData)

	// 计算衍生字段
	s.calculateDerivedFields(staging)

	result.MainRecord = staging
	return result, nil
}

// transformWithDetails 转换包含明细的记录
func (s *ContractTransformService) transformWithDetails(contract *insbuy.InsContract, formData map[string]interface{}, detailData []map[string]interface{}, startTime time.Time) (*TransformResult, error) {
	result := &TransformResult{
		Success:       true,
		Errors:        make([]string, 0),
		Warnings:      make([]string, 0),
		DetailRecords: make([]*insbuy.InsContractTransformStaging, 0),
	}

	// 1. 创建主记录
	mainRecord := &insbuy.InsContractTransformStaging{
		SourceContractId:   contract.ID,
		SourceInstanceCode: contract.InstanceCode,
		SourceApprovalCode: contract.ApprovalCode,
		DetailIndex:        0,
		DetailTotal:        len(detailData) + 1,
		TransformStatus:    "transformed",
		TransformTime:      &startTime,
		OriginalFormData:   contract.Form,
	}

	// 映射主记录字段
	s.mapBaseFields(mainRecord, contract, formData)
	s.mapBusinessFields(mainRecord, formData)

	// 2. 创建明细记录
	totalAmount := 0.0
	for i, detail := range detailData {
		detailRecord := &insbuy.InsContractTransformStaging{
			SourceContractId:   contract.ID,
			SourceInstanceCode: contract.InstanceCode,
			SourceApprovalCode: contract.ApprovalCode,
			DetailIndex:        i + 1,
			DetailTotal:        len(detailData) + 1,
			TransformStatus:    "transformed",
			TransformTime:      &startTime,
		}

		// 复制主记录的基础信息
		s.copyBaseInfo(detailRecord, mainRecord)

		// 映射明细字段
		s.mapDetailFields(detailRecord, detail)

		// 计算衍生字段
		s.calculateDerivedFields(detailRecord)

		result.DetailRecords = append(result.DetailRecords, detailRecord)

		// 累计金额
		if amount, err := strconv.ParseFloat(detailRecord.CurrentRequestAmount, 64); err == nil {
			totalAmount += amount
		}
	}

	// 3. 更新主记录汇总信息
	mainRecord.CurrentRequestAmount = fmt.Sprintf("%.2f", totalAmount)
	s.calculateDerivedFields(mainRecord)

	result.MainRecord = mainRecord
	return result, nil
}

// mapBaseFields 映射基础字段
func (s *ContractTransformService) mapBaseFields(staging *insbuy.InsContractTransformStaging, contract *insbuy.InsContract, formData map[string]interface{}) {
	// 基础信息
	staging.Title = contract.Title
	staging.InitiatorName = contract.InitiatorName

	// 完成时间格式化
	if contract.CompleteTime != nil {
		staging.CompletionTime = contract.CompleteTime.Format("2006-01-02 15:04:05")
		// 管报月份（从完成时间计算）
		staging.ReportMonth = contract.CompleteTime.Format("2006-01")
	}

	// 序号（可以从表单中提取或自动生成）
	if serialNum := s.getFormValue(formData, "widget_serial_number"); serialNum != "" {
		if num, err := strconv.Atoi(serialNum); err == nil {
			staging.SerialNumber = num
		}
	}

	// 发起人部门
	if dept := s.getFormValue(formData, "widget_department"); dept != "" {
		staging.InitiatorDepartment = dept
		staging.Department = dept
	}
}

// mapBusinessFields 映射业务字段
func (s *ContractTransformService) mapBusinessFields(staging *insbuy.InsContractTransformStaging, formData map[string]interface{}) {
	// 付款相关
	staging.PaymentEntity = s.getFormValue(formData, "widget_payment_entity")
	staging.PaymentReason = s.getFormValue(formData, "widget_payment_reason")
	staging.BusinessType = s.getFormValue(formData, "widget_business_type")

	// 金额相关（保持字符串格式）
	staging.ContractAmount = s.getFormValue(formData, "widget_contract_amount")
	staging.ContractPaidAmount = s.getFormValue(formData, "widget_paid_amount")
	staging.CurrentRequestAmount = s.getFormValue(formData, "widget_request_amount")

	// 税率
	staging.TaxRate = s.getFormValue(formData, "widget_tax_rate")

	// 费用类别
	staging.ExpenseCategory = s.getFormValue(formData, "widget_expense_category")

	// 银行信息
	staging.AccountName = s.getFormValue(formData, "widget_account_name")

	// 管报相关
	staging.ReportEntity = s.getFormValue(formData, "widget_report_entity")
	staging.ReportEntityDetail = s.getFormValue(formData, "widget_report_entity_detail")
	staging.ReportRegion = s.getFormValue(formData, "widget_report_region")

	// 是否纳入管报
	if includeReport := s.getFormValue(formData, "widget_include_report"); includeReport == "是" || includeReport == "true" {
		staging.IncludeInReport = "是"
	} else {
		staging.IncludeInReport = "否"
	}
}

// StoreToSourceStorage 存储到SourceStorageData（与现有Excel导入保持一致）
func (s *ContractTransformService) StoreToSourceStorage(stagingRecords []*insbuy.InsContractTransformStaging) error {
	// 1. 转换为RegionalExpenseDetailItem格式
	var items []RegionalExpenseDetailItem
	for _, staging := range stagingRecords {
		if staging.AuditStatus != "approved" {
			continue // 只处理审核通过的记录
		}

		item := staging.ToRegionalExpenseDetailItem()
		items = append(items, item)
	}

	if len(items) == 0 {
		return fmt.Errorf("没有审核通过的记录可以存储")
	}

	// 2. 使用现有的存储机制
	storageData := &datasource.SourceStorageData{
		DataSource: "飞书合同转换",
		DataType:   "RegionalExpenseDetail",
		Items:      items,
		CreatedAt:  time.Now(),
	}

	// 3. 调用现有的存储服务
	excelImportService := &ExcelImportService{}
	err := excelImportService.StoreRegionalExpenseDetailData(storageData)
	if err != nil {
		return fmt.Errorf("存储到SourceStorageData失败: %w", err)
	}

	// 4. 更新staging记录的存储状态
	for _, staging := range stagingRecords {
		if staging.AuditStatus == "approved" {
			staging.StorageStatus = "stored"
			staging.StorageTime = &time.Time{}
			*staging.StorageTime = time.Now()

			// 更新数据库
			if err := global.GVA_DB.Save(staging).Error; err != nil {
				global.GVA_LOG.Error("更新存储状态失败", zap.Error(err), zap.Uint("staging_id", staging.ID))
			}
		}
	}

	return nil
}

// BatchTransformContracts 批量转换合同数据
func (s *ContractTransformService) BatchTransformContracts(approvalCode string, operatorId uint, operatorName string) (string, error) {
	// 1. 生成批次号
	batchNo := s.generateBatchNo()

	// 2. 查询待转换的合同数据
	contracts, err := s.getContractsForTransform(approvalCode)
	if err != nil {
		return "", fmt.Errorf("查询待转换合同失败: %w", err)
	}

	// 3. 创建批次记录
	batch := &insbuy.InsContractTransformBatch{
		BatchNo:       batchNo,
		BatchName:     fmt.Sprintf("批量转换-%s", time.Now().Format("20060102-150405")),
		ApprovalCode:  approvalCode,
		OperationType: "transform",
		TotalCount:    len(contracts),
		Status:        "processing",
		StartTime:     &time.Time{},
		OperatorId:    operatorId,
		OperatorName:  operatorName,
	}
	*batch.StartTime = time.Now()

	if err := global.GVA_DB.Create(batch).Error; err != nil {
		return "", fmt.Errorf("创建批次记录失败: %w", err)
	}

	// 4. 异步处理转换任务
	go s.processBatchTransform(batch, contracts)

	return batchNo, nil
}

// BatchAuditRecords 批量审核记录
func (s *ContractTransformService) BatchAuditRecords(stagingIds []uint, auditStatus string, auditorId uint, auditNote string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 批量更新审核状态
		err := tx.Model(&insbuy.InsContractTransformStaging{}).
			Where("id IN ?", stagingIds).
			Updates(map[string]interface{}{
				"audit_status": auditStatus,
				"auditor_id":   auditorId,
				"audit_time":   now,
				"audit_note":   auditNote,
				"updated_at":   now,
			}).Error

		if err != nil {
			return err
		}

		// 记录审核日志
		for _, stagingId := range stagingIds {
			log := &insbuy.InsContractTransformLog{
				StagingId:       stagingId,
				OperationType:   "audit",
				OperationStatus: "success",
				OperatorId:      &auditorId,
				OperatorName:    "审核员", // 可以从用户表查询
			}
			if err := tx.Create(log).Error; err != nil {
				global.GVA_LOG.Error("创建审核日志失败", zap.Error(err))
			}
		}

		return nil
	})
}
```

## 5. API接口设计

### 5.1 主要接口

```go
// server/api/v1/insbuy/ins_contract_transform.go
package insbuy

import (
	"strconv"
	"github.com/gin-gonic/gin"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"go.uber.org/zap"
)

type ContractTransformApi struct{}

var contractTransformService = service.ServiceGroupApp.InsbuyServiceGroup.ContractTransformService

// TransformContract 转换单个合同
// @Tags      ContractTransform
// @Summary   转换单个合同
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     contractId   path      int  true  "合同ID"
// @Success   200   {object}  response.Response{msg=string}  "转换成功"
// @Router    /contract-transform/transform/{contractId} [post]
func (api *ContractTransformApi) TransformContract(c *gin.Context) {
	contractIdStr := c.Param("contractId")
	contractId, err := strconv.ParseUint(contractIdStr, 10, 32)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	result, err := contractTransformService.TransformContract(uint(contractId))
	if err != nil {
		global.GVA_LOG.Error("转换合同失败!", zap.Error(err))
		response.FailWithMessage("转换失败:"+err.Error(), c)
		return
	}

	if !result.Success {
		response.FailWithDetailed(result, "转换失败", c)
		return
	}

	response.OkWithData(result, c)
}

// BatchTransform 批量转换
// @Tags      ContractTransform
// @Summary   批量转换
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.BatchTransformReq  true  "批量转换请求"
// @Success   200   {object}  response.Response{data=string}  "转换成功"
// @Router    /contract-transform/batch-transform [post]
func (api *ContractTransformApi) BatchTransform(c *gin.Context) {
	var req insbuyReq.BatchTransformReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	batchNo, err := contractTransformService.BatchTransformContracts(req.ApprovalCode, req.OperatorId, req.OperatorName)
	if err != nil {
		global.GVA_LOG.Error("批量转换失败!", zap.Error(err))
		response.FailWithMessage("转换失败:"+err.Error(), c)
		return
	}

	response.OkWithData(batchNo, c)
}

// GetStagingList 获取转换结果列表
// @Tags      ContractTransform
// @Summary   获取转换结果列表
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.StagingSearchReq  true  "搜索条件"
// @Success   200   {object}  response.Response{data=response.PageResult}  "获取成功"
// @Router    /contract-transform/staging-list [post]
func (api *ContractTransformApi) GetStagingList(c *gin.Context) {
	var req insbuyReq.StagingSearchReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := contractTransformService.GetStagingList(&req)
	if err != nil {
		global.GVA_LOG.Error("获取列表失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// BatchAudit 批量审核
// @Tags      ContractTransform
// @Summary   批量审核
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.BatchAuditReq  true  "批量审核请求"
// @Success   200   {object}  response.Response{msg=string}  "审核成功"
// @Router    /contract-transform/batch-audit [post]
func (api *ContractTransformApi) BatchAudit(c *gin.Context) {
	var req insbuyReq.BatchAuditReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = contractTransformService.BatchAuditRecords(req.StagingIds, req.AuditStatus, req.AuditorId, req.AuditNote)
	if err != nil {
		global.GVA_LOG.Error("批量审核失败!", zap.Error(err))
		response.FailWithMessage("审核失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("审核成功", c)
}

// StoreToSource 存储到源数据
// @Tags      ContractTransform
// @Summary   存储到源数据
// @Security  ApiKeyAuth
// @accept    application/json
// @Produce   application/json
// @Param     data  body      insbuyReq.StoreToSourceReq  true  "存储请求"
// @Success   200   {object}  response.Response{msg=string}  "存储成功"
// @Router    /contract-transform/store-to-source [post]
func (api *ContractTransformApi) StoreToSource(c *gin.Context) {
	var req insbuyReq.StoreToSourceReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 查询审核通过的记录
	var stagingRecords []*insbuy.InsContractTransformStaging
	err = global.GVA_DB.Where("id IN ? AND audit_status = ?", req.StagingIds, "approved").Find(&stagingRecords).Error
	if err != nil {
		response.FailWithMessage("查询记录失败", c)
		return
	}

	err = contractTransformService.StoreToSourceStorage(stagingRecords)
	if err != nil {
		global.GVA_LOG.Error("存储失败!", zap.Error(err))
		response.FailWithMessage("存储失败:"+err.Error(), c)
		return
	}

	response.OkWithMessage("存储成功", c)
}
```

## 6. 数据流程图

### 6.1 整体转换和审核流程

```mermaid
graph TD
    A[飞书合同数据 ins_contract] --> B[数据转换服务]
    B --> C{是否包含明细}

    C -->|否| D[单条记录转换]
    C -->|是| E[明细拆分转换]

    D --> F[创建主记录]
    E --> G[创建主记录 + 明细记录]

    F --> H[ins_contract_transform_staging]
    G --> H

    H --> I{转换状态}
    I -->|transformed| J[待审核状态]
    I -->|failed| K[转换失败]

    J --> L[人工审核]
    L --> M{审核结果}
    M -->|approved| N[审核通过]
    M -->|rejected| O[审核驳回]
    M -->|modified| P[修改后重审]

    N --> Q[存储到SourceStorageData]
    Q --> R[与现有Excel导入数据合并]
    R --> S[最终数据可用于报表]

    O --> T[重新转换]
    P --> L
    T --> B

    style A fill:#e1f5fe
    style H fill:#f3e5f5
    style Q fill:#e8f5e8
    style S fill:#e8f5e8
```

### 6.2 与现有系统集成流程

```mermaid
graph TD
    A[飞书合同转换数据] --> B[RegionalExpenseDetailItem格式]
    B --> C[SourceStorageData]

    D[Excel导入数据] --> E[RegionalExpenseDetailItem格式]
    E --> C

    F[其他数据源] --> G[RegionalExpenseDetailItem格式]
    G --> C

    C --> H[统一的数据存储]
    H --> I[现有报表系统]
    H --> J[现有分析功能]
    H --> K[现有导出功能]

    style A fill:#e3f2fd
    style D fill:#e3f2fd
    style F fill:#e3f2fd
    style C fill:#f3e5f5
    style H fill:#e8f5e8
```

## 7. 实施步骤和时间规划

### 7.1 第一阶段：基础架构搭建（3天）

**目标**: 完成数据库表结构和基础模型定义

**任务清单**:
- [x] 设计数据库表结构
- [ ] 创建Go模型定义
- [ ] 编写数据库迁移脚本
- [ ] 创建基础的CRUD操作
- [ ] 单元测试编写

**交付物**:
- 数据库表创建脚本
- Go模型文件
- 基础服务层代码
- 单元测试用例

### 7.2 第二阶段：数据转换功能开发（4天）

**目标**: 实现核心的数据转换逻辑

**任务清单**:
- [ ] 实现表单数据解析逻辑
- [ ] 开发字段映射功能
- [ ] 实现明细拆分处理
- [ ] 开发单条和批量转换功能
- [ ] 集成测试

**交付物**:
- 数据转换服务
- 字段映射逻辑
- 明细拆分功能
- 转换测试用例

### 7.3 第三阶段：审核流程开发（3天）

**目标**: 实现人工审核和状态管理

**任务清单**:
- [ ] 开发审核界面和API
- [ ] 实现批量审核功能
- [ ] 开发修改和重审功能
- [ ] 实现审核日志记录
- [ ] 权限控制集成

**交付物**:
- 审核管理功能
- 批量操作接口
- 操作日志系统
- 权限控制机制

### 7.4 第四阶段：存储集成开发（2天）

**目标**: 与现有SourceStorageData系统集成

**任务清单**:
- [ ] 实现与现有存储系统的集成
- [ ] 开发数据格式转换功能
- [ ] 测试数据一致性
- [ ] 性能优化

**交付物**:
- 存储集成服务
- 数据格式转换器
- 集成测试报告
- 性能测试结果

### 7.5 第五阶段：前端界面和测试（3天）

**目标**: 完成用户界面和系统测试

**任务清单**:
- [ ] 开发转换管理界面
- [ ] 开发审核管理界面
- [ ] 系统集成测试
- [ ] 用户验收测试
- [ ] 文档编写

**交付物**:
- 前端管理界面
- 完整的系统测试
- 用户操作手册
- 技术文档

## 8. 核心特性总结

### 8.1 技术特性

✅ **完全集成现有系统** - 复用现有的 `SourceStorageData` 存储机制
✅ **数据格式一致性** - 与现有Excel导入功能使用相同的 `RegionalExpenseDetailItem` 格式
✅ **智能明细拆分** - 自动识别并拆分费用明细为多条记录
✅ **完整审核流程** - 提供转换、审核、修改、存储的完整生命周期管理
✅ **批量处理能力** - 支持大量数据的高效批量转换和审核
✅ **操作可追溯** - 完整的操作日志和状态变更记录

### 8.2 业务特性

✅ **三状态管理** - 转换状态、审核状态、存储状态独立管理
✅ **人工二次确认** - 转换后的数据需要人工审核确认
✅ **灵活的修改机制** - 支持审核驳回后的数据修改和重审
✅ **与现有流程兼容** - 最终数据与Excel导入数据完全一致
✅ **多维度查询** - 支持按状态、时间、审批类型等多种条件查询

### 8.3 系统架构特性

✅ **微服务架构** - 转换、审核、存储服务相互独立
✅ **异步处理** - 批量操作采用异步处理，提高系统响应性
✅ **事务一致性** - 关键操作使用数据库事务保证数据一致性
✅ **扩展性设计** - 支持未来新的审批类型和字段扩展

这个方案提供了一个完整的飞书合同数据转换和审核系统，确保数据质量的同时，与现有系统完美集成，为业务提供可靠的数据处理能力。
```

## 4. 核心转换服务

### 4.1 数据转换服务

```go
// server/service/insbuy/ins_contract_transform.go
package insbuy

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ContractTransformService struct{}

// TransformResult 转换结果
type TransformResult struct {
	Success       bool                                  `json:"success"`
	MainRecord    *insbuy.InsContractTransformStaging   `json:"main_record"`
	DetailRecords []*insbuy.InsContractTransformStaging `json:"detail_records"`
	Errors        []string                              `json:"errors"`
	Warnings      []string                              `json:"warnings"`
}

// TransformContract 转换单个合同数据
func (s *ContractTransformService) TransformContract(contractId uint) (*TransformResult, error) {
	startTime := time.Now()

	// 1. 查询源合同数据
	contract, err := s.getContractById(contractId)
	if err != nil {
		return nil, fmt.Errorf("查询源合同失败: %w", err)
	}

	// 2. 解析表单数据
	formData, err := s.parseFormData(contract.Form)
	if err != nil {
		return &TransformResult{
			Success: false,
			Errors:  []string{fmt.Sprintf("解析表单数据失败: %v", err)},
		}, nil
	}

	// 3. 检查是否有明细数据
	detailData := s.extractExpenseDetails(formData)

	if len(detailData) == 0 {
		// 无明细，处理单条记录
		return s.transformSingleRecord(contract, formData, startTime)
	} else {
		// 有明细，拆分处理
		return s.transformWithDetails(contract, formData, detailData, startTime)
	}
}

// transformSingleRecord 转换单条记录（无明细）
func (s *ContractTransformService) transformSingleRecord(contract *insbuy.InsContract, formData map[string]interface{}, startTime time.Time) (*TransformResult, error) {
	result := &TransformResult{
		Success:  true,
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// 创建转换记录
	staging := &insbuy.InsContractTransformStaging{
		SourceContractId:   contract.ID,
		SourceInstanceCode: contract.InstanceCode,
		SourceApprovalCode: contract.ApprovalCode,
		DetailIndex:        0,
		DetailTotal:        1,
		TransformStatus:    "transformed",
		TransformTime:      &startTime,
		OriginalFormData:   contract.Form,
	}

	// 映射基础字段
	s.mapBaseFields(staging, contract, formData)

	// 映射业务字段
	s.mapBusinessFields(staging, formData)

	// 计算衍生字段
	s.calculateDerivedFields(staging)

	result.MainRecord = staging
	return result, nil
}

// transformWithDetails 转换包含明细的记录
func (s *ContractTransformService) transformWithDetails(contract *insbuy.InsContract, formData map[string]interface{}, detailData []map[string]interface{}, startTime time.Time) (*TransformResult, error) {
	result := &TransformResult{
		Success:       true,
		Errors:        make([]string, 0),
		Warnings:      make([]string, 0),
		DetailRecords: make([]*insbuy.InsContractTransformStaging, 0),
	}

	// 1. 创建主记录
	mainRecord := &insbuy.InsContractTransformStaging{
		SourceContractId:   contract.ID,
		SourceInstanceCode: contract.InstanceCode,
		SourceApprovalCode: contract.ApprovalCode,
		DetailIndex:        0,
		DetailTotal:        len(detailData) + 1,
		TransformStatus:    "transformed",
		TransformTime:      &startTime,
		OriginalFormData:   contract.Form,
	}

	// 映射主记录字段
	s.mapBaseFields(mainRecord, contract, formData)
	s.mapBusinessFields(mainRecord, formData)

	// 2. 创建明细记录
	totalAmount := 0.0
	for i, detail := range detailData {
		detailRecord := &insbuy.InsContractTransformStaging{
			SourceContractId:   contract.ID,
			SourceInstanceCode: contract.InstanceCode,
			SourceApprovalCode: contract.ApprovalCode,
			DetailIndex:        i + 1,
			DetailTotal:        len(detailData) + 1,
			TransformStatus:    "transformed",
			TransformTime:      &startTime,
		}

		// 复制主记录的基础信息
		s.copyBaseInfo(detailRecord, mainRecord)

		// 映射明细字段
		s.mapDetailFields(detailRecord, detail)

		// 计算衍生字段
		s.calculateDerivedFields(detailRecord)

		result.DetailRecords = append(result.DetailRecords, detailRecord)

		// 累计金额
		if amount, err := strconv.ParseFloat(detailRecord.CurrentRequestAmount, 64); err == nil {
			totalAmount += amount
		}
	}

	// 3. 更新主记录汇总信息
	mainRecord.CurrentRequestAmount = fmt.Sprintf("%.2f", totalAmount)
	s.calculateDerivedFields(mainRecord)

	result.MainRecord = mainRecord
	return result, nil
}
```
