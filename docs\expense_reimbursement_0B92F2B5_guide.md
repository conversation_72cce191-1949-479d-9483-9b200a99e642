# 费用报销申请单配置指南 (0B92F2B5-922F-4570-8A83-489E476FF811)

## 概述

本文档介绍费用报销申请单（审批代码：0B92F2B5-922F-4570-8A83-489E476FF811）的配置文件使用方法。该配置文件位于 `server/config/contract_mappings/expense_reimbursement_0B92F2B5.yaml`。

## 配置文件结构

### 1. 基础信息映射

配置文件包含以下基础信息字段：
- `application_number`: 申请编号
- `title`: 申请标题  
- `application_status`: 申请状态
- `initiate_time`: 发起时间
- `complete_time`: 完成时间
- `initiator_user_id`: 发起人User ID
- `initiator_department_id`: 发起人部门ID

### 2. 费用报销特有字段

#### 费用报销编号 (serialNumber类型)
```yaml
serial_number:
  source_path: "form.widget_serial_number.value"
  target_field: "serial_number"
  data_type: "string"
  required: false
  description: "费用报销编号"
```

#### 报销事由 (textarea类型)
```yaml
reimbursement_reason:
  source_path: "form.widget_reason.value"
  target_field: "reimbursement_reason"
  data_type: "string"
  required: true
  description: "报销事由"
```

#### 费用所属部门 (department类型)
```yaml
expense_department:
  source_path: "form.widget_department.value[0].name"
  target_field: "expense_department"
  data_type: "string"
  required: true
  description: "费用所属部门"
```

#### 付款公司 (radioV2类型)
```yaml
payment_company:
  source_path: "form.widget_payment_company.value"
  target_field: "payment_company"
  data_type: "string"
  required: true
  description: "付款公司"
```

### 3. 费用明细列表 (fieldList类型)

费用明细是一个复杂的列表结构，包含以下子字段：

```yaml
expense_details:
  source_path: "form.widget_expense_list.value"
  target_field: "expense_details"
  data_type: "fieldList"
  required: true
  description: "费用明细列表"
  field_list_config:
    expense_type:
      source_path: "widget_expense_type.value"
      target_field: "expense_type"
      data_type: "string"
      required: true
      description: "费用类型"
    
    vat_invoice_type:
      source_path: "widget_vat_invoice_type.value"
      target_field: "vat_invoice_type"
      data_type: "string"
      required: false
      description: "增值税发票类型"
    
    amount:
      source_path: "widget_amount.value"
      target_field: "amount"
      data_type: "float"
      required: true
      description: "费用金额"
```

### 4. 收款账号信息 (account类型)

收款账号是一个复合对象，包含：
- `account_holder`: 收款方户名
- `account_type`: 账户类型
- `account_number`: 账户号码
- `bank_name`: 银行名称
- `bank_branch`: 银行支行
- `bank_region`: 银行地区

### 5. 其他字段

#### 说明备注 (input类型)
```yaml
remarks:
  source_path: "form.widget_remarks.value"
  target_field: "remarks"
  data_type: "string"
  required: false
  description: "说明备注"
```

#### 附件 (attachmentV2类型)
```yaml
attachments:
  source_path: "form.widget_attachments.value"
  target_field: "attachments"
  data_type: "array"
  required: false
  description: "附件"
  transform: "parseAttachmentsWithUrls"
```

## 特殊处理配置

### fieldList 处理

配置文件包含特殊的 `field_list_processing` 配置，用于处理费用明细列表：

```yaml
field_list_processing:
  enabled: true
  split_records: true  # 将列表拆分为独立记录
  preserve_parent_data: true  # 保留父级数据
  record_prefix: "expense_detail"  # 记录前缀
```

### 数据转换规则

配置了多种数据转换规则：
- 银行信息提取（`extractBankName`, `extractBankBranch`, `extractBankRegion`）
- 附件URL生成（`parseAttachmentsWithUrls`）
- 金额大写转换（`convertToCapital`）

### 业务规则验证

包含以下业务规则：
- 费用明细验证（最少1条，最多50条）
- 总金额验证（必须等于明细金额之和）
- 部门权限验证（检查跨部门申请权限）

## 验证规则

配置文件定义了以下验证规则：

1. **必填字段验证**：申请编号、报销事由、费用所属部门、付款公司等
2. **格式验证**：账户号码必须为10-30位数字
3. **范围验证**：费用总金额必须大于0.01
4. **业务逻辑验证**：费用明细不能为空

## 导出配置

支持Excel导出，包含三个工作表：
1. **费用报销主表**：包含所有主要字段
2. **费用明细表**：费用明细列表的详细信息
3. **附件清单**：附件列表信息

导出文件命名格式：`expense_reimbursement_export_{instance_code}_{timestamp}.xlsx`

## 使用方法

1. 将配置文件放置在 `server/config/contract_mappings/` 目录下
2. 确保审批代码 `0B92F2B5-922F-4570-8A83-489E476FF811` 在系统配置中已启用
3. 系统会自动加载该配置文件并应用于对应的审批流程
4. 数据转换时会按照配置的映射规则进行字段转换和验证

## 注意事项

1. **Widget ID 映射**：配置文件中的 widget ID（如 `widget_serial_number`、`widget_reason` 等）需要根据实际的飞书表单字段ID进行调整
2. **字段类型匹配**：确保 `data_type` 与实际数据类型匹配
3. **必填字段**：根据业务需求调整 `required` 字段的设置
4. **验证规则**：根据实际业务规则调整验证参数

## 扩展说明

如需添加新字段或修改现有配置，请参考现有字段的配置格式，并确保：
1. 正确设置 `source_path` 和 `target_field`
2. 选择合适的 `data_type`
3. 添加相应的验证规则
4. 更新必填字段列表（如适用）
