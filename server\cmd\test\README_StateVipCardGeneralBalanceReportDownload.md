# StateVipCardGeneralBalanceReportDownload 测试用例说明

## 概述

本测试用例用于测试 `StateVipCardGeneralBalanceReportDownload` 函数的文件导出功能。该函数用于生成集团会员用通用余额消费报表并导出为 Excel 文件。

## 测试文件位置

- 测试文件：`server/cmd/test/report_test.go`
- 测试函数：
  - `TestStateVipCardGeneralBalanceReportDownload` - 完整测试（包含多个场景）
  - `TestStateVipCardGeneralBalanceReportDownloadSimple` - 简化测试

## 功能说明

### 主要功能
1. 直接调用 `insreport.StatusMemberBalanceConsume` 函数获取会员余额消费数据
2. 使用 `excel.ExportSimple` 将数据导出为 Excel 文件
3. 验证文件生成和数据完整性

### 测试场景
1. **默认参数测试** - 使用当前月份作为结束日期，保留收入日志
2. **指定参数测试** - 指定结束日期为 2024-01-31，保留收入日志
3. **不保留收入日志测试** - 指定结束日期为 2024-02-29，不保留收入日志

## 运行方法

### 方法1：运行完整测试
```bash
cd server
go test ./cmd/test -run TestStateVipCardGeneralBalanceReportDownload -v
```

### 方法2：运行简化测试
```bash
cd server
go test ./cmd/test -run TestStateVipCardGeneralBalanceReportDownloadSimple -v
```

### 方法3：在主测试函数中启用
在 `Test报告` 函数中，取消注释以下行：
```go
TestStateVipCardGeneralBalanceReportDownload(t)
```

然后运行：
```bash
cd server
go test ./cmd/test -run Test报告 -v
```

## 输出文件

测试成功后会在当前目录生成以下文件：
- `test_output_INS会员余额消费.YYYY-MM-DD.xlsx` - 完整测试生成的文件
- `test_simple_INS会员余额消费.YYYY-MM-DD.xlsx` - 简化测试生成的文件

## 参数说明

### StatusMemberBalanceConsumeParams 参数
- `BeginDate` - 筛选开始日期（可选）
- `EndDate` - 筛选结束日期，默认为当月1号
- `SettleDate` - 结算结束日期（可选）
- `KeepIncome` - 是否保留产出日志（1-保留，0-不保留）
- `VipPhones` - 会员手机号码列表（可选）

## 预期结果

### 成功情况
- 测试日志显示数据获取成功
- Excel 文件成功生成
- 文件大小大于 0
- 导出行数与数据记录数匹配

### 失败情况处理
- 如果数据库中没有相关数据，测试会记录信息但不会失败
- 如果数据库连接失败，会记录错误信息
- 文件导出失败会显示具体错误信息

## 数据结构

导出的 Excel 文件包含以下字段：
- `event_log_id` - 日志ID
- `event_at` - 下单时间
- `change_type` - 变动类型
- `order_code` - 订单号
- `balance_cost` - INS余额抵现金额
- `gift_card_code` - 对应CDK
- `gift_card_caption` - CDK备注
- `source_type` - 来源分类
- `discount` - 结算折扣
- `amount` - 结算金额
- `balance_left` - 事后结余
- `nick_name` - 下单用户昵称
- 其他相关字段...

## 注意事项

1. 确保数据库连接正常
2. 确保有足够的磁盘空间存储导出文件
3. 测试完成后可以删除生成的测试文件
4. 如果在生产环境运行，请注意数据量可能很大

## 故障排除

### 常见问题
1. **数据库连接失败** - 检查数据库配置和连接
2. **权限不足** - 确保有文件写入权限
3. **内存不足** - 大量数据可能导致内存问题
4. **文件路径问题** - 确保输出目录存在

### 调试方法
- 查看测试日志中的详细错误信息
- 检查 `prepare()` 函数是否正确初始化了数据库连接
- 验证查询参数是否正确
