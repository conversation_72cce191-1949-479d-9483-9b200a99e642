package test

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
)

func TestAttachmentInfoConversion(t *testing.T) {
	// 创建转换器
	transformer := insbuy.NewContractTransformer()

	fmt.Println("=== 测试新的 AttachmentInfo 结构体转换 ===")

	// 测试数据1：模拟飞书附件数据结构
	testData1 := map[string]interface{}{
		"ext": "【飞猪】曼谷-上海 (往返) 订单8271376686906-机票款凭证 报销凭证.pdf,酒店发票.pdf,Hush 三虫玩家（上海）文化传播有限公司_20250728185110.pdf",
		"value": []interface{}{
			"http://example.com/file1.pdf",
			"http://example.com/file2.pdf",
			"http://example.com/file3.pdf",
		},
	}

	fmt.Printf("输入数据1: %+v\n", testData1)

	// 使用 parseAttachmentsWithUrls 转换
	result1 := transformer.ParseAttachmentsWithUrls(testData1)
	if attachments, ok := result1.([]insbuy.AttachmentInfo); ok {
		fmt.Printf("转换结果1 (共 %d 个附件):\n", len(attachments))
		for i, attachment := range attachments {
			fmt.Printf("  %d: 文件名='%s', URL='%s'\n", i+1, attachment.FileName, attachment.FileURL)
		}
	} else {
		t.Errorf("转换结果1类型错误，期望 []AttachmentInfo，实际 %T", result1)
	}

	// 测试数据2：字符串数组格式（文件名|URL）
	testData2 := []string{
		"文件1.pdf|http://example.com/file1.pdf",
		"文件2.pdf|http://example.com/file2.pdf",
		"只有文件名.pdf",
		"http://example.com/only-url.pdf",
	}

	fmt.Printf("\n输入数据2: %+v\n", testData2)

	// 使用 convertToAttachmentInfoArray 转换
	result2 := transformer.ConvertToAttachmentInfoArray(testData2)
	fmt.Printf("转换结果2 (共 %d 个附件):\n", len(result2))
	for i, attachment := range result2 {
		fmt.Printf("  %d: 文件名='%s', URL='%s'\n", i+1, attachment.FileName, attachment.FileURL)
	}

	// 测试数据3：纯字符串格式
	testData3 := "文件A.pdf,文件B.pdf,文件C.pdf"

	fmt.Printf("\n输入数据3: %s\n", testData3)

	// 使用 parseAttachments 转换
	result3 := transformer.ParseAttachments(testData3)
	if attachments, ok := result3.([]insbuy.AttachmentInfo); ok {
		fmt.Printf("转换结果3 (共 %d 个附件):\n", len(attachments))
		for i, attachment := range attachments {
			fmt.Printf("  %d: 文件名='%s', URL='%s'\n", i+1, attachment.FileName, attachment.FileURL)
		}
	} else {
		t.Errorf("转换结果3类型错误，期望 []AttachmentInfo，实际 %T", result3)
	}

	// 测试JSON序列化
	fmt.Println("\n=== 测试JSON序列化 ===")
	if attachments, ok := result1.([]insbuy.AttachmentInfo); ok && len(attachments) > 0 {
		jsonData, err := json.MarshalIndent(attachments, "", "  ")
		if err != nil {
			t.Errorf("JSON序列化失败: %v", err)
		} else {
			fmt.Printf("JSON格式:\n%s\n", string(jsonData))
		}
	}
}

// 为了测试，我们需要创建一些包装方法来访问私有方法
// 注意：在实际代码中，这些方法应该是公开的或者通过其他方式测试

// 这里我们假设这些方法已经被导出或者我们通过其他方式测试
// 如果需要，可以在 contract_transformer.go 中临时导出这些方法进行测试
