package test

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"testing"

	"gopkg.in/yaml.v3"
)

// FieldConfig 字段配置
type FieldConfig struct {
	SourcePath   string      `json:"source_path" yaml:"source_path"`
	TargetField  string      `json:"target_field" yaml:"target_field"`
	DataType     string      `json:"data_type" yaml:"data_type"`
	DefaultValue interface{} `json:"default_value" yaml:"default_value"`
	Transform    string      `json:"transform" yaml:"transform"`
	Required     bool        `json:"required" yaml:"required"`
	Validation   string      `json:"validation" yaml:"validation"`
	Description  string      `json:"description" yaml:"description"`
}

// ValidationRule 验证规则
type ValidationRule struct {
	Field     string `json:"field" yaml:"field"`
	Rule      string `json:"rule" yaml:"rule"`
	Parameter string `json:"parameter" yaml:"parameter"`
	Message   string `json:"message" yaml:"message"`
}

// FieldMappingRule 字段映射规则
type FieldMappingRule struct {
	ApprovalCode    string                 `json:"approval_code" yaml:"approval_code"`
	ApprovalName    string                 `json:"approval_name" yaml:"approval_name"`
	FieldMappings   map[string]FieldConfig `json:"field_mappings" yaml:"field_mappings"`
	DefaultValues   map[string]interface{} `json:"default_values" yaml:"default_values"`
	RequiredFields  []string               `json:"required_fields" yaml:"required_fields"`
	ValidationRules []ValidationRule       `json:"validation_rules" yaml:"validation_rules"`
}

func TestConfigParsing(t *testing.T) {
	// 测试 YAML 文件解析
	fmt.Println("=== 测试 YAML 文件解析 ===")
	testYAMLFile("./config/contract_mappings/payment_application.yaml")

	fmt.Println("\n=== 测试 JSON 文件解析 ===")
	testJSONFile("./config/contract_mappings/purchase_contract.json")
}

func testYAMLFile(filename string) {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		log.Printf("读取文件失败: %v", err)
		return
	}

	fmt.Printf("文件内容长度: %d 字节\n", len(content))
	fmt.Printf("文件内容前200字符:\n%s\n", string(content[:min(200, len(content))]))

	// 尝试解析为 map[string]*FieldMappingRule
	var rules map[string]*FieldMappingRule
	err = yaml.Unmarshal(content, &rules)
	if err != nil {
		log.Printf("YAML 解析失败: %v", err)
		return
	}

	fmt.Printf("解析成功，规则数量: %d\n", len(rules))
	for code, rule := range rules {
		fmt.Printf("规则代码: %s\n", code)
		fmt.Printf("  - 规则: %+v\n", rule)
		if rule != nil {
			fmt.Printf("  - 审批代码: %s\n", rule.ApprovalCode)
			fmt.Printf("  - 审批名称: %s\n", rule.ApprovalName)
			fmt.Printf("  - 字段映射数量: %d\n", len(rule.FieldMappings))
			fmt.Printf("  - 必填字段数量: %d\n", len(rule.RequiredFields))
		} else {
			fmt.Printf("  - 规则为 nil\n")
		}
	}
}

func testJSONFile(filename string) {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		log.Printf("读取文件失败: %v", err)
		return
	}

	fmt.Printf("文件内容长度: %d 字节\n", len(content))

	// 尝试解析为 map[string]*FieldMappingRule
	var rules map[string]*FieldMappingRule
	err = json.Unmarshal(content, &rules)
	if err != nil {
		log.Printf("JSON 解析失败: %v", err)
		return
	}

	fmt.Printf("解析成功，规则数量: %d\n", len(rules))
	for code, rule := range rules {
		fmt.Printf("规则代码: %s\n", code)
		if rule != nil {
			fmt.Printf("  - 审批代码: %s\n", rule.ApprovalCode)
			fmt.Printf("  - 审批名称: %s\n", rule.ApprovalName)
			fmt.Printf("  - 字段映射数量: %d\n", len(rule.FieldMappings))
			fmt.Printf("  - 必填字段数量: %d\n", len(rule.RequiredFields))
		} else {
			fmt.Printf("  - 规则为 nil\n")
		}
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
