package test

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insfinance"
	"testing"
)

func TestInstancesDetails(t *testing.T) {
	prepare()
	insfinance.InstancesDetails()
}

func TestInstancesList(t *testing.T) {
	prepare()
	insfinance.InstanceCodeList()
}

func TestDailyFullSyncTask(t *testing.T) {
	prepare()
	insbuy.DailyFullSyncTask()
}

func TestIncrementalSyncTask(t *testing.T) {
	prepare()
	insbuy.IncrementalSyncTask()
}
