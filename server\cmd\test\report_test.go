package test

import (
	"context"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insdata"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insexternal"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport"
	"github.com/xtulnx/jkit-go/jtime"
	"go.uber.org/zap"
	"log"
	"testing"
	"time"
)

func Test报告(t *testing.T) {
	prepare()

	//reportUserOfSalesman(t)

	//reportStateSalesman(t)
	//reportStateSalesmanRank(t)
	//reportStateDeskSales(t)
	//reportStateProductCategory(t)
	//reportStateProductSales(t)
	//reportStateProductGive(t)

	//reportProduct(t)

	//reportProductDetails(t)
	//reportProductSale(t)

	//reportUserInfo(t)

	//reportHySummary(t)
	//reportOpenDesk(t)
	//reportWineSales(t)
	//reportWineGiveDetail(t)

	//dailyRemarkNum(t)
	//analysisProduct(t)
	//gateFlowSummary(t)
	//analyzeBusinessHours(t)
	//Summary(t)
	//stateDeposit(t)
	//analyzeRefund(t)
	//analysisProductHours(t)
	//analyzeOverall(t)
	//AnalysisRecharge(t)
	//analyzeReserve(t)
	//analyzeDeskConsume(t)
	//AnalyzeTypeReserve(t)
	//AnalyzeTypeOpen(t)
	//AnalyzeGiveRemark(t)
	//syncGateFlowSummaryToDb(t)
	//getOpenDeskPay(t)
	//OpenDeskSnapshot1(t)
	//StateVipCardBalance(t)
	//syncExtStoreTicketToDb(t)
	Test_weeklyReport(t)
}

func reportHySummary(t *testing.T) {
	srv := service.ServiceGroupApp.InsBuyServiceGroup.HyReport
	resp, list, err := srv.Summary(context.Background(), insbuyReq.HyReportSummary{
		//StoreId:   "GreenRoom",
		//StartDate: "2023-12-24",
		//EndDate:   "2024-01-07",
		//Mode:      2,
		StartDate: "2023-12-31",
		EndDate:   "2024-01-05",
		Mode:      1,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	dumpTable(nil, list)
}

func reportUserInfo(t *testing.T) {
	d1, err := insreport.DataUserInfo(context.Background(), query.Q, insreport.DataUserInfoParam{})
	if err != nil {
		t.Fatal(err)
	}
	for k, v := range d1 {
		t.Logf("%d: %s = %s", k, v.NickName, v.OrgName)
	}
}

func reportProduct(t *testing.T) {
	srv := service.ServiceGroupApp.InsBuyServiceGroup.Report
	req := insbuyReq.InsReportDetailProductReq{}
	resp, err := srv.DetailProduct(req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func reportProductDetails(t *testing.T) {
	ctx := context.Background()
	dp := insreport.NewDailyReportParam(jtime.Str2Date("2024-01-05"), 1, 20, "6")
	resp, data, err := insreport.DailyProductSaleDetails(ctx, query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	//t.Log(resp)
	header := kitHeaders(resp.List.Header)

	// "year","month","day","product_name","unit","nums","product_price","product_shop_price","package_flag","product_category","order_amount","gift_amount","giver_name","shipment_sn","order_sn","close_time","desk_name","desk_type","desk_area","open_desk_sn","salesman_name","salesman_org","order_flag","cashier_name","cashier_org","waiter_name","waiter_org","order_time","shipper_name","shipper_org","ship_time"
	header = []string{
		"product_name", "unit", "nums", "product_price", "product_shop_price",
		"package_flag", "product_category",
		"order_amount",
		//"gift_amount",
		//"giver_name",
		"order_sn",
		"order_flag",
	}

	dumpTable(header, data)
}

func reportProductSale(t *testing.T) {
	ctx := context.Background()
	dp := insreport.NewDailyReportParam(jtime.Str2Date("2024-01-05"), 1, 2000, "")
	resp, data, err := insreport.DailyProductSale(ctx, query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.List.Header)
	dumpTable(header, data)
}

func dailyRemarkNum(t *testing.T) {
	ctx := context.Background()
	resp, data, err := insreport.DailyRemarkNum(ctx, query.Q, insreport.DailyRemarkNumParams{
		StartDate:  jtime.Str2Date("2024-01-07"),
		EndDate:    jtime.Str2Date("2024-01-07"),
		RemarkType: "1",
		Remark:     "",
		StoreIds:   "1",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := []string{
		"day", "remark_num", "remark_type", "remark_content",
	}
	dumpTable(header, data)
}

func reportUserOfSalesman(t *testing.T) {
	ctx := context.Background()
	l1, err := insreport.DataUserOfSalesman(ctx, query.Q, insreport.DataUserOfSalesmanParam{})
	if err != nil {
		t.Fatal(err)
	}
	for _, v := range l1 {
		t.Logf("%d: %s = %v", v.UserId, v.NickName, v.Level)
	}
}

func reportStateSalesman(t *testing.T) {
	ctx := context.Background()
	resp, data, err := insreport.StateSalesman(ctx, query.Q, insreport.StateSalesmanParams{
		StartDate: jtime.Str2Date("2024-01-07"),
		EndDate:   jtime.Str2Date("2024-01-07"),
		StoreIds:  nil,
		//SalesMainId: []uint{37},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := []string{
		"salesman_id", "salesman_name", "total_amount", "give_amount",
		"normal_paid", "credit_paid", "free_paid",
	}
	dumpTable(header, data)
}

func reportStateSalesmanRank(t *testing.T) {
	ctx := context.Background()
	resp, data, err := insreport.StateSalesmanRank(ctx, query.Q, insreport.StateSalesmanRankParams{
		/*StartDate: jtime.Str2Date("2024-01-06"),
		EndDate:   jtime.Str2Date("2024-01-06"),
		StoreIds:  nil,
		UserIds:   []uint{37},*/
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := []string{
		"salesman_id", "salesman_name", "salesman_org", "order_amount", "give_amount",
	}
	dumpTable(header, data)
}

func reportStateDeskSales(t *testing.T) {
	ctx := context.Background()
	resp, data, err := insreport.StateDeskSales(ctx, query.Q, insreport.StateReportParams{
		StartDate: jtime.Str2Date("2024-01-06"),
		EndDate:   jtime.Str2Date("2024-01-06"),
		StoreIds:  nil,
		UserIds:   []uint{37},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := []string{
		"desk_id", "desk_name", "order_amount", "give_amount",
	}
	dumpTable(header, data)
}

func reportStateProductCategory(t *testing.T) {
	ctx := context.Background()
	resp, data, err := insreport.StateProductCategory(ctx, query.Q, insreport.StateReportParams{
		StartDate: jtime.Str2Date("2024-01-01"),
		EndDate:   jtime.Str2Date("2024-01-06"),
		StoreIds:  nil,
		UserIds:   []uint{37},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := []string{
		"category_id", "category_name", "order_amount", "rate",
	}
	dumpTable(header, data)
}
func reportStateProductSales(t *testing.T) {
	ctx := context.Background()
	resp, data, err := insreport.StateProductSales(ctx, query.Q, insreport.StateReportParams{
		StartDate: jtime.Str2Date("2024-01-06"),
		EndDate:   jtime.Str2Date("2024-01-06"),
		StoreIds:  nil,
		UserIds:   []uint{37},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := []string{
		"product_id", "product_name", "category_id", "category_name", "order_amount", "nums",
	}
	dumpTable(header, data)
}
func reportStateProductGive(t *testing.T) {
	ctx := context.Background()
	resp, data, err := insreport.StateProductGive(ctx, query.Q, insreport.StateReportParams{
		StartDate: jtime.Str2Date("2024-01-06"),
		EndDate:   jtime.Str2Date("2024-01-06"),
		StoreIds:  nil,
		UserIds:   []uint{37},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := []string{
		"product_id", "product_name", "category_id", "category_name", "give_amount", "nums",
	}
	dumpTable(header, data)
}

func kitHeaders(header []insreport.SimpleReportRespDataHeader) []string {
	var headers []string
	for _, i := range header {
		headers = append(headers, i.Field)
	}
	return headers
}

func reportOpenDesk(t *testing.T) {
	dp := insreport.NewDailyReportParam(jtime.Str2Date("2024-01-05"), 1, 500, "6")
	resp, data, err := insreport.DailyOpenDesk(context.Background(), query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)

	header := kitHeaders(resp.List.Header)
	dumpTable(header, data)
}

func reportWineSales(t *testing.T) {
	dp := insreport.NewDailyReportParam(jtime.Str2Date("2024-01-05"), 1, 500, "6")
	resp, data, err := insreport.DailyWineSales(context.Background(), query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.List.Header)
	dumpTable(header, data)
}

func reportWineGiveDetail(t *testing.T) {
	dp := insreport.NewDailyReportParam(jtime.Str2Date("2024-01-05"), 1, 500, "6,10")
	resp, data, err := insreport.DailyWineGiveDetail(context.Background(), query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.List.Header)
	dumpTable(header, data)
}

func analysisProduct(t *testing.T) {
	dp := insreport.AnalysisProductParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-09"),
			EndDate:   jtime.Str2Date("2024-07-09"),
		},
	}
	resp, _, err := insreport.AnalysisProduct(context.Background(), query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)
}

// AnalysisProductHours 时段商品分析
func analysisProductHours(t *testing.T) {
	dp := insreport.AnalysisProductHoursParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-02"),
			EndDate:   jtime.Str2Date("2024-07-03"),
		},
	}
	resp, _, err := insreport.AnalysisProductHours(context.Background(), query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)
}

// GateFlowSummary
func gateFlowSummary(t *testing.T) {
	dp := insexternal.GateFlowSummaryParams{
		ExtGateFlowSummaryParams: insexternal.ExtGateFlowSummaryParams{
			StartDate: jtime.Str2Date("2024-06-21"),
			EndDate:   jtime.Str2Date("2024-06-21"),
		},
	}
	logger := global.GVA_LOG.With(zap.Any("dp", dp))
	resp, err := insexternal.ClubTicket.GateFlowSummary(context.Background(), logger, dp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

// SyncGateFlowSummaryToDb
func syncGateFlowSummaryToDb(t *testing.T) {
	dbFlow := query.Q.InsScanFlow
	var startDate time.Time
	err := dbFlow.Order(dbFlow.BusinessDay.Desc()).Select(dbFlow.BusinessDay).Limit(1).Scan(&startDate)
	if err != nil {
		return
	}
	if startDate.IsZero() {
		startDate = jtime.Str2Date("2024-06-17")
	}
	dp := insexternal.GateFlowSummaryParams{
		ExtGateFlowSummaryParams: insexternal.ExtGateFlowSummaryParams{
			StartDate: startDate,
			EndDate:   jtime.Str2Date("2024-08-09"),
		},
	}
	logger := global.GVA_LOG.With(zap.Any("dp", dp))
	err = insexternal.ClubTicket.SyncGateFlowSummaryToDb(context.Background(), logger, dp)
	if err != nil {
		t.Fatal(err)
	}
}

func syncExtStoreTicketToDb(t *testing.T) {
	dbTicket := query.Q.InsExtStoreTicket
	var startDate time.Time
	err := dbTicket.Order(dbTicket.BusinessDay.Desc()).Select(dbTicket.BusinessDay).Limit(1).Scan(&startDate)
	if err != nil {
		return
	}
	if startDate.IsZero() {
		startDate = jtime.Str2Date("2024-11-18")
	}
	params := insexternal.Ext4TicketParams{
		StartDate: jtime.Str2Date("2025-01-20"),
		EndDate:   jtime.Str2Date("2025-01-20"),
	}
	reportDailyStart := time.Hour * 9 //和合计数据保持一致
	if !params.StartDate.IsZero() {
		params.StartDate = params.StartDate.Add(reportDailyStart)
	}
	if !params.EndDate.IsZero() {
		params.EndDate = params.EndDate.Add(time.Hour*24 + reportDailyStart)
	}
	dp := insexternal.StoreTicketParams{
		Ext4TicketParams: params,
		PageReq: request.PageReq{
			PageSize: -1,
		},
	}
	logger := global.GVA_LOG.With(zap.Any("dp", dp))
	err = insexternal.ClubTicket.SyncExtStoreTicketToDb(context.Background(), logger, dp)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(2222)
}

func analyzeBusinessHours(t *testing.T) {
	dp := insreport.AnalysisProductParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-06-01"),
			EndDate:   jtime.Str2Date("2024-06-24"),
		},
	}
	resp, _, err := insreport.AnalyzeBusinessHours(context.Background(), query.Q, dp)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)

}

// AnalyzeOverall 总览
func analyzeOverall(t *testing.T) {
	ctx := context.Background()
	data, err := insreport.AnalyzeOverall(ctx, query.Q, insreport.AnalyzeOverallParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-01"),
			EndDate:   jtime.Str2Date("2024-07-31"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(data)
}

// AnalyzeRefund
func analyzeRefund(t *testing.T) {
	ctx := context.Background()
	resp, _, err := insreport.AnalyzeRefund(ctx, query.Q, insreport.AnalyzeRefundParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-01-26"),
			EndDate:   jtime.Str2Date("2024-06-27"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)

}

func Summary(t *testing.T) {
	srv := service.ServiceGroupApp.InsBuyServiceGroup.HyReport
	dp := insbuyReq.HyReportSummary{
		StartDate: "2024-06-01",
		EndDate:   "2024-06-26",
		StoreId:   "INS总部",
	}
	dto := &Dto{
		ID:          1,
		Username:    "admin",
		NickName:    "admin",
		AuthorityId: 1,
	}
	dp.SetAuthUser(dto)
	res, _, err := srv.Summary(context.Background(), dp)
	if err != nil {
		t.Fatalf("err= %v", err)
		return
	}
	t.Log(res)
}

type Dto struct {
	ID          uint
	Username    string
	NickName    string
	AuthorityId uint
}

func (R *Dto) GetUserID() uint {
	return R.ID
}
func (R *Dto) GetUserAuthorityId() uint {
	return R.AuthorityId
}
func (R *Dto) GetUserName() string {
	return R.Username
}
func (R *Dto) GetNickName() string {
	return R.NickName
}

func AnalysisRecharge(t *testing.T) {
	ctx := context.Background()
	resp, err := insreport.AnalysisRecharge(ctx, query.Q, insreport.AnalyzeRechargeParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-01-26"),
			EndDate:   jtime.Str2Date("2024-06-27"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)
}

// AnalyzeReserve
func analyzeReserve(t *testing.T) {
	ctx := context.Background()
	resp, _, err := insreport.AnalyzeReserve(ctx, query.Q, insreport.AnalyzeReserveParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-09"),
			EndDate:   jtime.Str2Date("2024-07-09"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)
}

// AnalyzeDeskConsume
func analyzeDeskConsume(t *testing.T) {
	ctx := context.Background()
	resp, _, err := insreport.AnalyzeDeskRange(ctx, query.Q, insreport.AnalyzeDeskRangeParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-09"),
			EndDate:   jtime.Str2Date("2024-07-09"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)
}

// AnalyzeTypeReserve
func AnalyzeTypeReserve(t *testing.T) {
	ctx := context.Background()
	resp, _, err := insreport.AnalyzeTypeReserve(ctx, query.Q, insreport.AnalyzeTypeReserveParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-09"),
			EndDate:   jtime.Str2Date("2024-07-09"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)
}

// AnalyzeTypeOpen
func AnalyzeTypeOpen(t *testing.T) {
	ctx := context.Background()
	resp, _, err := insreport.AnalyzeTypeOpen(ctx, query.Q, insreport.AnalyzeTypeOpenParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-09"),
			EndDate:   jtime.Str2Date("2024-07-09"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)
}

// AnalyzeGiveRemark
func AnalyzeGiveRemark(t *testing.T) {
	ctx := context.Background()
	resp, _, err := insreport.AnalyzeGiveRemark(ctx, query.Q, insreport.AnalyzeGiveRemarkParams{
		AnalysisReportParams: insreport.AnalysisReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-09"),
			EndDate:   jtime.Str2Date("2024-07-09"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, resp.List)
}

// StateDeposit
func stateDeposit(t *testing.T) {
	ctx := context.Background()
	resp, data, err := insreport.StateDeposit(ctx, query.Q, insreport.StateDepositParams{
		StateReportParams: insreport.StateReportParams{
			PageNum:   1,
			PageSize:  20,
			StartDate: jtime.Str2Date("2024-07-09"),
			EndDate:   jtime.Str2Date("2024-07-09"),
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
	header := kitHeaders(resp.Headers)
	dumpTable(header, data)
}

func getOpenDeskPay(t *testing.T) {
	res := insreport.GetOpenDeskPay(query.Q, insreport.OpenParams{
		StartDate:  jtime.Str2Date("2024-09-04"),
		EndDate:    jtime.Str2Date("2024-09-04"),
		StoreIds:   []uint{6},
		SalesmanId: []uint{39},
		IsOrg:      true,
	})
	for _, v := range res {
		fmt.Printf("%+v\n", v)
	}

}

func OpenDeskSnapshot1(t *testing.T) {
	inService := insbuy.InsBookInService{}
	inService.OpenDeskSnapshot(insbuyReq.OpenDeskSnapshotReq{
		StartDate: "2024-09-01",
		EndDate:   "2024-09-05",
		StoreId:   6,
	})
}

func StateVipCardBalance(t *testing.T) {
	resp, _, err := insreport.StateVipCardBalance(context.Background(), query.Q, insreport.StateReportParams{
		StartDate: jtime.Str2Date("2024-07-09"),
		EndDate:   jtime.Str2Date("2024-09-26"),
		StoreIds:  []uint{6},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Fatalf("%+v\n", resp)
	return
}

func TestGenerateSQL_SimpleQuery(t *testing.T) {
	meta := insdata.ReportMeta{
		BaseTable: "sales_snapshot",
		Fields: []insdata.ReportField{
			{FieldName: "Region", FieldExpression: "region_name", Alias: "region_name"},
			{FieldName: "Total Sales", FieldExpression: "amount", AggregationType: "SUM", Alias: "total_sales"},
		},
		Filters: []insdata.Filter{
			{Key: "region", Operator: "=", Value: "North America", Logic: "AND"},
			{Key: "sale_date", Operator: "BETWEEN", Value: []interface{}{"2024-01-01", "2024-12-31"}, Logic: "AND"},
			{Key: "amount", Operator: ">", Value: 1000, Logic: "OR"},
		},
		Sorts: []string{"total_sales DESC"},
	}

	query, err := insdata.GenerateSQL(meta)
	if err != nil {
		t.Fatalf("Error generating SQL: %v", err)
	}
	fmt.Println(query)
	expectedQuery := `SELECT region_name AS region_name, SUM(amount) AS total_sales FROM sales_snapshot WHERE region_name = 'North America' ORDER BY total_sales DESC`
	if query != expectedQuery {
		t.Errorf("Got: %s, Expected: %s", query, expectedQuery)
	}
}

func Test_templates(t *testing.T) {
	prepare()
	meta := insdata.ReportMeta{
		BaseTable: "ins_order_info",
		Alias:     "ioi",
		Fields: []insdata.ReportField{
			{FieldName: "店铺", FieldExpression: "ioi.store_id", Alias: "store_id"},
			{FieldName: "编号", FieldExpression: "ioi.order_sn", Alias: "order_sn"},
		},
		JoinConditions: []insdata.JoinCondition{
			{
				JoinType: "left join",
				Table:    "ins_order_info_details",
				On:       "ioi.order_id = ins_order_info_details.order_id",
			},
		},
		Filters: []insdata.Filter{
			{Key: "ioi.business_day", Operator: ">=", Value: "2024-11-26", Logic: "AND"},
			{Key: "ioi.business_day", Operator: "<=", Value: "2024-11-26", Logic: "AND"},
		},
		Sorts: []string{"ioi.business_day DESC"},
	}
	// 渲染模板
	query, err := insdata.RenderTemplate("templates/template.sql", meta)
	if err != nil {
		log.Fatalf("Error rendering template: %v", err)
	}
	db, err := global.GVA_DB.DB()
	if err != nil {
		return
	}
	t.Logf(query)
	rows, err := db.Query(query)
	if err != nil {
		t.Fatal(err)
		return
	}
	defer rows.Close()

	// 解析结果
	columns, err := rows.Columns()
	if err != nil {
		return
	}
	var results []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return
		}
		row := map[string]interface{}{}
		for i, col := range columns {
			row[col] = values[i]
		}
		results = append(results, row)
	}
	fmt.Println("Generated SQL:")
	fmt.Println(results)
}

func Test_weeklyReport(t *testing.T) {
	ctx := context.Background()
	resp, err := insreport.WeeklyReport(ctx, query.Q, insreport.WeeklyReportParams{
		StartDate: jtime.Str2Date("2025-04-09"),
		EndDate:   jtime.Str2Date("2025-04-26"),
		StoreIds:  []uint{6},
	})
	if err != nil {
		return
	}
	t.Log(resp)
}

func TestStateVipCardGeneralBalanceReportDownload(t *testing.T) {
	prepare()
	insreport.StateVipCardGeneralBalanceReportFile("", nil)
}
