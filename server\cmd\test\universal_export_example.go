package test

import (
	"fmt"
	"log"
)

// ExampleUniversalDataExport 展示如何使用通用数据导出方法
func ExampleUniversalDataExport() {
	// 创建测试器实例
	tester := NewContractTransformerTester()

	// 示例实例代码列表
	instanceCodes := []string{
		"C457E806-AC54-46AB-ADA7-90E3BFBEBC92", // 付款申请示例
		// 可以添加更多不同类型的实例代码
	}

	fmt.Println("=== 通用数据导出示例 ===")
	fmt.Printf("配置目录: %s\n", tester.configMgr.GetConfigPath())

	for i, instanceCode := range instanceCodes {
		fmt.Printf("\n--- 处理第 %d 个实例 ---\n", i+1)
		fmt.Printf("实例代码: %s\n", instanceCode)

		// 使用通用方法导出数据
		err := tester.TestUniversalDataExport(instanceCode)
		if err != nil {
			fmt.Printf("❌ 导出失败: %v\n", err)
		} else {
			fmt.Printf("✅ 导出成功\n")
		}
	}

	fmt.Println("\n=== 示例完成 ===")
}

// RunUniversalExportExample 运行通用导出示例
func RunUniversalExportExample() {
	fmt.Println("开始运行通用数据导出示例...")

	defer func() {
		if r := recover(); r != nil {
			log.Printf("示例运行出现错误: %v", r)
		}
	}()

	ExampleUniversalDataExport()
}

// GetConfigPath 获取配置管理器的配置路径（用于调试）
func (tester *ContractTransformerTester) GetConfigPath() string {
	return tester.configMgr.GetConfigPath()
}

// 使用说明：
//
// 1. 通用数据导出的优势：
//    - 自动识别审批类型
//    - 自动加载对应的配置文件
//    - 支持模糊匹配和默认规则
//    - 无需硬编码审批代码
//
// 2. 添加新审批类型的步骤：
//    a) 在 config/contract_mappings/ 目录下创建新的配置文件
//    b) 配置文件中定义审批代码和字段映射规则
//    c) 可选：在 isApprovalCodeMatch 方法中添加关键词匹配规则
//    d) 可选：在 getApprovalTypeFromCode 方法中添加类型名称映射
//
// 3. 配置文件命名建议：
//    - payment_application.yaml (付款申请)
//    - contract_approval.yaml (合同审批)
//    - purchase_approval.yaml (采购审批)
//    - expense_approval.yaml (费用审批)
//
// 4. 测试方法：
//    - TestUniversalDataExportWithInstanceCode: 单个实例测试
//    - TestUniversalDataExportBatch: 批量测试
//    - TestPaymentApplicationConfig: 配置文件验证
//
// 5. 文件输出：
//    - 文件名格式: {审批类型}_export_{实例代码}_{时间戳}.xlsx
//    - 保存位置: 项目根目录
//    - 包含完整的转换结果和附件信息
