# github.com/flipped-aurora/gin-vue-admin/server Global Configuration

ins-buy:
  store-code:
  ext-pay:
    db:
      dsn: "insbuy_root:9R1#nQoMsPX7Op!OiS@tcp(rm-uf6jf1u63b1850292.mysql.rds.aliyuncs.com:8904)/ins-pay?charset=utf8mb4&parseTime=True&loc=Asia%2FShanghai"
      prefix: ""
      singular: true
kafka:
  topicmap:
    bookdesk: bookdesk
  groupid: bookdesk-cd-group
  #  srv:
  #    - "alikafka-pre-cn-5yd3iirzq001-1-vpc.alikafka.aliyuncs.com:9092"
  #    - "alikafka-pre-cn-5yd3iirzq001-2-vpc.alikafka.aliyuncs.com:9092"
  #    - "alikafka-pre-cn-5yd3iirzq001-3-vpc.alikafka.aliyuncs.com:9092"
  #  securityprotocol: plaintext
  srv:
    - "alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093"
    - "alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093"
    - "alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"
  securityprotocol: sasl_ssl
  cafile: "./kafka-ali-only-4096-ca-cert.pem"
  sasl:
    mechanism: "PLAIN"
    username: "alikafka_pre-cn-5yd3iirzq001"
    password: "rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"
grpc:
  addr: ":8889"
  log-level: info

# jwt configuration
jwt:
  signing-key: qmPlus
  expires-time: 7d
  buffer-time: 1d
  issuer: qmPlus
# zap logger configuration
zap:
  level: info
  format: console
  prefix: "[insbuy]"
  director: log
  show-line: true
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  log-in-console: true

# redis configuration
redis:
  db: 0
  addr: r-uf6fy3h33bdhopteue.redis.rds.aliyuncs.com:6379
  username: "insbuy_root"
  password: "L&#tX#%ei%1OPC58P#"

# email configuration
email:
  to: <EMAIL>
  port: 465
  from: <EMAIL>
  host: smtp.163.com
  is-ssl: true
  secret: xxx
  nickname: test

# system configuration
system:
  #env: public  # Change to "develop" to skip authentication for development mode
  env: develop # 开发测试用
  addr: 8888
  db-type: mysql
  oss-type: aliyun-oss # 控制oss选择走本地还是 七牛等其他仓 自行增加其他oss仓可以在 server/utils/upload/upload.go 中 NewOss函数配置
  use-redis: true     # 使用redis
  use-multipoint: false
  # IP限制次数 一个小时15000次
  iplimit-count: 15000
  #  IP限制一个小时
  iplimit-time: 3600
  #  路由全局前缀
  router-prefix: "/insbuy-api"
  domain: https://pos.mmweb.top/insbuy-api/
  swagger-disabled: true

# captcha configuration
captcha:
  key-long: 6
  img-width: 240
  img-height: 80
  open-captcha: 0 # 0代表一直开启，大于0代表限制次数
  open-captcha-timeout: 3600 # open-captcha大于0时才生效
  super-captcha: [ jar0,insVip,a,616666 ]

# mysql connect configuration
# 未初始化之前请勿手动修改数据库信息！！！如果一定要手动初始化请看（https://gin-vue-admin.com/docs/first_master）
mysql:
  path: rm-uf6jf1u63b1850292.mysql.rds.aliyuncs.com
  port: "8904"
  config: charset=utf8mb4&parseTime=True&loc=Asia%2FShanghai
  db-name: ins-prod
  username: insbuy_root
  password: 9R1#nQoMsPX7Op!OiS
  prefix: ""
  singular: false
  engine: ""
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: error
  log-zap: false

# pgsql connect configuration
# 未初始化之前请勿手动修改数据库信息！！！如果一定要手动初始化请看（https://gin-vue-admin.com/docs/first_master）
pgsql:
  path: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false

db-list:
  - disable: true # 是否禁用
    type: "" # 数据库的类型,目前支持mysql、pgsql
    alias-name: "" # 数据库的名称,注意: alias-name 需要在db-list中唯一
    path: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    max-idle-conns: 10
    max-open-conns: 100
    log-mode: ""
    log-zap: false


# local configuration
local:
  path: uploads/file
  store-path: uploads/file

# autocode configuration
autocode:
  transfer-restart: true
  # root 自动适配项目根目录
  # 请不要手动配置,他会在项目加载的时候识别出根路径
  root: ""
  server: /server
  server-plug: /plugin/%s
  server-api: /api/v1/%s
  server-initialize: /initialize
  server-model: /model/%s
  server-request: /model/%s/request/
  server-router: /router/%s
  server-service: /service/%s
  web: /web/src
  web-api: /api
  web-form: /view
  web-table: /view

# qiniu configuration (请自行七牛申请对应的 公钥 私钥 bucket 和 域名地址)
qiniu:
  zone: ZoneHuaDong
  bucket: ""
  img-path: ""
  use-https: false
  access-key: ""
  secret-key: ""
  use-cdn-domains: false

# aliyun oss configuration
aliyun-oss:
  endpoint: oss-cn-shanghai.aliyuncs.com
  access-key-id: LTAI5tR7D7BFrTRjKJggPvkC
  access-key-secret: ******************************
  bucket-name: insbuy-oss
  bucket-url: https://static.mmweb.top
  base-path: insbuy-srv

# tencent cos configuration
tencent-cos:
  bucket: xxxxx-10005608
  region: ap-shanghai
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: github.com/flipped-aurora/gin-vue-admin/server

# aws s3 configuration (minio compatible)
aws-s3:
  bucket: xxxxx-10005608
  region: ap-shanghai
  endpoint: ""
  s3-force-path-style: false
  disable-ssl: false
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: github.com/flipped-aurora/gin-vue-admin/server

# huawei obs configuration
hua-wei-obs:
  path: you-path
  bucket: you-bucket
  endpoint: you-endpoint
  access-key: you-access-key
  secret-key: you-secret-key

# excel configuration
excel:
  dir: ./resource/excel/

resource:
  sqlTemplates: ./resource/templates/

# timer task db clear table
Timer:
  start: true
  spec: "@daily"  # 定时任务详细配置参考 https://pkg.go.dev/github.com/robfig/cron/v3
  detail:
    - tableName: sys_operation_records
      compareField: created_at
      interval: 2160h
    - tableName: jwt_blacklists
      compareField: created_at
      interval: 168h
  insTask:
    - taskName: "MaterialInventory"
      spec: "46 11 * * *"
      withSeconds: false
    - taskName: "MaterialInventoryTime"
      spec: "@every 5m"
      withSeconds: false
    - taskName: "DepositReport"
      spec: "@every 1m"
      withSeconds: false
    - taskName: "RefreshBusinessDay"
      spec: "@every 10m"
      withSeconds: false
    - taskName: "AutoEndBusiness"
      spec: "1 9 * * *"
      withSeconds: false
    - taskName: "AutoReviseOrderAmount"
      spec: "@every 5m"
      withSeconds: false
    - taskName: "CheckMaterialConfig"
      spec: "@every 60m"
      withSeconds: false
    - taskName: "RetryPrintTask"
      spec: "@every 1m"
      withSeconds: false
    #    - taskName: "SyncOrder2HyCheckout"
    #      spec: "@every 5m"
    #      withSeconds: false
    - taskName: "DepositExpireSms"
      spec: "5 20 * * *" # 每天晚上8点5分执行
      withSeconds: false
    - taskName: "SyncOrderFromKezee"
      spec: "*/5 17-23,0-8 * * *" # 每天晚上8点5分执行
      withSeconds: false
    - taskName: "SyncGateFlowSummary"
      spec: "@every 5m"
      withSeconds: false
    - taskName: "CacheOpenDeskSnapshot"
      spec: "@every 10m"
      withSeconds: false
    - taskName: "CacheMiniBusinessData"
      spec: "55 8 * * *"
      withSeconds: false
    - taskName: "CacheOpenDeskSnapshotEnd"
      spec: "2 9 * * *"
      withSeconds: false
    - taskName: "GenStoreSaleLog"##必须在店铺成本价计算成功之后才计算
      spec: "15 9 * * *"
      withSeconds: false
    - taskName: "GenStoreCostPrice"
      spec: "5 9 * * *"
      withSeconds: false
    - taskName: "CalculateSaleMonthQuota"
      spec: "0 8 1 * *"
      withSeconds: false
    - taskName: "SyncTicketSummary"
      spec: "@every 5m"
      withSeconds: false
    - taskName: "ClearAuthUserUseAmount"
      spec: "5 9 1 * *"##每月1号9点5分清除总部用户使用金额
      withSeconds: false
    - taskName: "PullReconciliation"
      spec: "30 9 * * *"  # 每天9:30拉取对账单
      withSeconds: false
    - taskName: "HandleReconciliation"
      spec: "*/1 9-14 * * *"  # 每天9点到下午2点，每分钟执行一次处理对账任务
      withSeconds: false
    - taskName: "CacheSalesShareData"
      spec: "0 20-23/2,0-12/2 * * *"  # 缓存销售订单基础数据
      withSeconds: false
    - taskName: "CacheSalesShare"
      spec: "15 20-23/2,0-12/2 * * *"  # 缓存销售订单提成计算
      withSeconds: false
    - taskName: "AutoInventoryCheck"
      spec: "5 9 * * *"  # 每天早上9点执行自动盘点
      withSeconds: false

# 跨域配置
# 需要配合 server/initialize/router.go -> `Router.Use(middleware.CorsByRules())` 使用
cors:
  mode: whitelist # 放行模式: allow-all, 放行全部; whitelist, 白名单模式, 来自白名单内域名的请求添加 cors 头; strict-whitelist 严格白名单模式, 白名单外的请求一律拒绝
  whitelist:
    - allow-origin: example1.com
      allow-headers: content-type
      allow-methods: GET, POST
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true # 布尔值
    - allow-origin: example2.com
      allow-headers: content-type
      allow-methods: GET, POST
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true # 布尔值

# ins-buy configuration 支付配置
pay:
  env: prod

## 阿里云翻译
aliyun-translation:
  access-key-id: LTAI5tFTKvLshrE3L6zSEXr3
  access-key-secret: ******************************

# 私人仓库配置
pri-inventory:
  apply-user-id: ["1075", "1132", "4", "1354", "2055"]

# 飞书配置
feishu-app:
  app-id: cli_a8084e417e9f101c
  app-secret: Xgl2hN6B37XxPFpQXuEFkdWpWkKd8pIY