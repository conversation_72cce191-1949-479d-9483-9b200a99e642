# 费用报销申请单配置文件
# 用于将飞书审批表单数据转换为标准化的费用报销数据
# 审批代码: 0B92F2B5-922F-4570-8A83-489E476FF811
0B92F2B5-922F-4570-8A83-489E476FF811:
  approval_code: "0B92F2B5-922F-4570-8A83-489E476FF811"
  approval_name: "费用报销申请单"
  description: "费用报销申请审批流程的数据映射配置"
  
  # 字段映射配置
  field_mappings:
    # 基础信息
    application_number:
      source_path: "basic.instance_code"
      target_field: "application_number"
      data_type: "string"
      required: true
      description: "申请编号"
    
    title:
      source_path: "basic.approval_name"
      target_field: "title"
      data_type: "string"
      required: true
      description: "申请标题"
    
    application_status:
      source_path: "basic.status"
      target_field: "application_status"
      data_type: "string"
      required: true
      description: "申请状态"
    
    initiate_time:
      source_path: "basic.start_time"
      target_field: "initiate_time"
      data_type: "time"
      required: true
      description: "发起时间"
    
    complete_time:
      source_path: "basic.end_time"
      target_field: "complete_time"
      data_type: "time"
      required: false
      description: "完成时间"
    
    # 人员信息
    initiator_user_id:
      source_path: "basic.user_id"
      target_field: "initiator_user_id"
      data_type: "string"
      required: true
      description: "发起人User ID"
    
    initiator_department_id:
      source_path: "basic.department_id"
      target_field: "initiator_department_id"
      data_type: "string"
      required: false
      description: "发起人部门ID"
    
    # 费用报销编号 - serialNumber类型
    serial_number:
      source_path: "form.widget16808486004060001.value"
      target_field: "serial_number"
      data_type: "string"
      required: false
      description: "费用报销编号"
    
    # 报销事由 - textarea类型
    payment_reason:
      source_path: "form.widget0.value"
      target_field: "payment_reason"
      data_type: "string"
      required: false
      description: "报销事由"
    
    # 费用所属部门 - department类型
    expense_department:
      source_path: "form.widget16709264180350001.value[0].name"
      target_field: "expense_department"
      data_type: "string"
      required: false
      description: "费用所属部门"
    
    # 付款公司 - radioV2类型
    payment_company:
      source_path: "form.widget16423899380530001.value"
      target_field: "payment_company"
      data_type: "string"
      required: false
      description: "付款公司"
    
    # 费用明细列表 - fieldList类型
    expense_details:
      source_path: "form.widget16710060410180001.value"
      target_field: "expense_details"
      data_type: "fieldList"
      required: true
      description: "费用明细列表"
      field_list_config:
        # 费用类型
        expense_type:
          source_path: "widget16710060735460001.value"
          target_field: "expense_type"
          data_type: "string"
          required: true
          description: "费用类型"
        
        # 增值税发票类型
        vat_invoice_type:
          source_path: "widget17455770530670001.value"
          target_field: "vat_invoice_type"
          data_type: "string"
          required: false
          description: "增值税发票类型"
        
        # 金额
        amount:
          source_path: "widget16710062249940001.value"
          target_field: "amount"
          data_type: "float"
          required: true
          description: "费用金额"
    
    # 汇总金额信息
    total_amount:
      source_path: "form.widget16710060410180001.ext[0].value"
      target_field: "total_amount"
      data_type: "float"
      required: false
      description: "费用总金额"
    
    # 收款账号信息 - account类型
    account_holder:
      source_path: "form.widget16657399953960001.value.widgetAccountName"
      target_field: "account_holder"
      data_type: "string"
      required: false
      description: "收款方户名"
    
    account_type:
      source_path: "form.widget16657399953960001.value.widgetAccountType.text"
      target_field: "account_type"
      data_type: "string"
      required: false
      description: "账户类型"
    
    account_number:
      source_path: "form.widget16657399953960001.value.widgetAccountNumber"
      target_field: "account_number"
      data_type: "string"
      required: false
      description: "账户号码"
    
    bank_name:
      source_path: "form.widget16657399953960001.value.widgetAccountBankName"
      target_field: "bank_name"
      data_type: "string"
      required: false
      description: "银行名称"
      transform: "extractBankName"
    
    bank_branch:
      source_path: "form.widget16657399953960001.value.widgetAccountBankBranch"
      target_field: "bank_branch"
      data_type: "string"
      required: false
      description: "银行支行"
      transform: "extractBankBranch"
    
    bank_region:
      source_path: "form.widget16657399953960001.value.widgetAccountBankArea"
      target_field: "bank_region"
      data_type: "string"
      required: false
      description: "银行地区"
      transform: "extractBankRegion"
    
    # 说明备注 - input类型
    remarks:
      source_path: "form.widget16425590849800001.value"
      target_field: "remarks"
      data_type: "string"
      required: false
      description: "说明备注"
    
    # 附件 - attachmentV2类型
    attachments:
      source_path: "form.widget15828099482720001.value"
      target_field: "attachments"
      data_type: "array"
      required: false
      description: "附件"
      transform: "parseAttachmentsWithUrls"
  
  # 默认值配置
  default_values:
    business_type: "费用报销"
    data_version: "1.0"
    currency: "人民币"
    record_type: "expense_reimbursement"
  
  # 必填字段列表s
  required_fields:
    - "application_number"
    - "title"
    - "expense_department"
    - "total_amount"
    - "account_holder"
    - "account_number"
    - "bank_name"
    - "initiator_user_id"
    - "expense_details"
  
  # 验证规则
  validation_rules:
    - field: "application_number"
      rule: "required"
      parameter: ""
      message: "申请编号不能为空"
    
    - field: "reimbursement_reason"
      rule: "required"
      parameter: ""
      message: "报销事由不能为空"
    
    - field: "expense_department"
      rule: "required"
      parameter: ""
      message: "费用所属部门不能为空"
    
    - field: "payment_company"
      rule: "required"
      parameter: ""
      message: "付款公司不能为空"
    
    - field: "total_amount"
      rule: "range"
      parameter: "0.01,*********"
      message: "费用总金额必须大于0"
    
    - field: "expense_details"
      rule: "required"
      parameter: ""
      message: "费用明细不能为空"
    
    - field: "account_number"
      rule: "format"
      parameter: "^[0-9]{10,30}$"
      message: "账户号码格式不正确"

  # fieldList 特殊处理配置
  field_list_processing:
    enabled: true
    split_records: true  # 是否将列表拆分为独立记录
    preserve_parent_data: true  # 是否保留父级数据
    record_prefix: "expense_detail"  # 记录前缀

    # 拆分规则
    split_rules:
      - source_field: "expense_details"
        target_record_type: "expense_detail_record"
        merge_parent_fields:
          - "application_number"
          - "title"
          - "initiator_user_id"
          - "reimbursement_reason"
          - "expense_department"
          - "payment_company"
          - "account_holder"
          - "account_number"
          - "bank_name"
          - "total_amount"

  # 数据转换规则
  transform_rules:
    # 银行信息提取规则
    - field: "bank_name"
      transform: "extractBankName"
      description: "从银行全称中提取银行名称"

    - field: "bank_branch"
      transform: "extractBankBranch"
      description: "从银行全称中提取支行信息"

    - field: "bank_region"
      transform: "extractBankRegion"
      description: "从银行全称中提取地区信息"

    # 附件处理规则
    - field: "attachments"
      transform: "parseAttachmentsWithUrls"
      description: "解析附件信息并生成访问URL"

    # 金额大写转换
    - field: "amount_capital"
      transform: "convertToCapital"
      description: "将数字金额转换为中文大写"

  # 业务规则配置
  business_rules:
    # 费用明细验证
    expense_details_validation:
      min_items: 1
      max_items: 50
      required_fields: ["expense_type", "amount"]
      amount_precision: 2

    # 总金额验证
    total_amount_validation:
      must_equal_sum: true  # 总金额必须等于明细金额之和
      tolerance: 0.01  # 允许的误差范围

    # 部门权限验证
    department_validation:
      check_permission: true  # 检查发起人是否有权限为该部门申请报销
      allow_cross_department: false  # 是否允许跨部门申请

  # 导出配置
  export_config:
    excel_template: "expense_reimbursement_template.xlsx"
    sheets:
      - name: "费用报销主表"
        type: "main"
        include_fields: "all"
      - name: "费用明细表"
        type: "detail"
        source_field: "expense_details"
        include_parent_fields: ["application_number", "title", "reimbursement_reason"]
      - name: "附件清单"
        type: "attachment"
        source_field: "attachments"
        include_parent_fields: ["application_number", "title"]

    filename_pattern: "expense_reimbursement_export_{instance_code}_{timestamp}.xlsx"
