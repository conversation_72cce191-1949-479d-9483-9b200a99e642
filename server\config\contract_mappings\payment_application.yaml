# 付款申请审批配置文件
# 用于将飞书审批表单数据转换为标准化的付款申请数据
F523F053-7AC6-4280-A4E7-B35E0C0431B5:
  approval_code: "F523F053-7AC6-4280-A4E7-B35E0C0431B5"
  approval_name: "付款申请审批"
  description: "付款申请审批流程的数据映射配置"
  
  # 字段映射配置
  field_mappings:
    # 基础信息
    application_number:
      source_path: "basic.instance_code"
      target_field: "application_number"
      data_type: "string"
      required: true
      description: "申请编号"
    
    title:
      source_path: "basic.approval_name"
      target_field: "title"
      data_type: "string"
      required: true
      description: "申请标题"
    
    application_status:
      source_path: "basic.status"
      target_field: "application_status"
      data_type: "string"
      required: true
      description: "申请状态"
    
    initiate_time:
      source_path: "basic.start_time"
      target_field: "initiate_time"
      data_type: "time"
      required: true
      description: "发起时间"
    
    complete_time:
      source_path: "basic.end_time"
      target_field: "complete_time"
      data_type: "time"
      required: false
      description: "完成时间"
    
    # 人员信息
    initiator_user_id:
      source_path: "basic.user_id"
      target_field: "initiator_user_id"
      data_type: "string"
      required: true
      description: "发起人User ID"
    
    initiator_department_id:
      source_path: "basic.department_id"
      target_field: "initiator_department_id"
      data_type: "string"
      required: false
      description: "发起人部门ID"
    
    serial_number:
      source_path: "form.widget16856053045110001.value"
      target_field: "serial_number"
      data_type: "string"
      required: false
      description: "流水号"
    
    # 业务信息
    payment_reason:
      source_path: "form.widget0.value"
      target_field: "payment_reason"
      data_type: "string"
      required: true
      description: "付款事由"
    
    payment_entity:
      source_path: "form.widget16423899380530001.value"
      target_field: "payment_entity"
      data_type: "string"
      required: true
      description: "付款主体"
    
    business_type:
      source_path: "form.widget16701426685000001.value"
      target_field: "business_type"
      data_type: "string"
      required: false
      description: "业务类型"
    
    payment_currency:
      source_path: "form.widget16799083691540001.value"
      target_field: "payment_currency"
      data_type: "string"
      required: true
      description: "付款币种"
    
    # 金额信息
    contract_sign_amount:
      source_path: "form.widget16671961125560001.value"
      target_field: "contract_sign_amount"
      data_type: "float"
      required: true
      description: "合同签约金额"
    
    contract_paid_amount:
      source_path: "form.widget16671961212400001.value"
      target_field: "contract_paid_amount"
      data_type: "float"
      required: false
      description: "合同已付金额"
    
    current_request_amount:
      source_path: "form.widget1.value"
      target_field: "current_request_amount"
      data_type: "float"
      required: true
      description: "本次请款金额"
    
    # 税务信息
    vat_invoice_type:
      source_path: "form.widget17466051580200001.value"
      target_field: "vat_invoice_type"
      data_type: "string"
      required: false
      description: "增值税发票类型"
    
    tax_rate:
      source_path: "form.widget17466050486380001.value"
      target_field: "tax_rate"
      data_type: "float"
      required: false
      description: "税率"
    
    tax_excluded_amount:
      source_path: "form.widget17466051470100001.value"
      target_field: "tax_excluded_amount"
      data_type: "float"
      required: false
      description: "不含税金额"
    
    # 银行账户信息
    account_holder:
      source_path: "form.widget16657399953960001.value.widgetAccountName"
      target_field: "account_holder"
      data_type: "string"
      required: true
      description: "收款方户名"
    
    account_type:
      source_path: "form.widget16657399953960001.value.widgetAccountType.text"
      target_field: "account_type"
      data_type: "string"
      required: false
      description: "账户类型"
    
    account_number:
      source_path: "form.widget16657399953960001.value.widgetAccountNumber"
      target_field: "account_number"
      data_type: "string"
      required: true
      description: "账户号码"
    
    bank_name:
      source_path: "form.widget16657399953960001.value.widgetAccountBankName"
      target_field: "bank_name"
      data_type: "string"
      required: true
      description: "银行名称"
      transform: "extractBankName"
    
    bank_branch:
      source_path: "form.widget16657399953960001.value.widgetAccountBankBranch"
      target_field: "bank_branch"
      data_type: "string"
      required: false
      description: "银行支行"
      transform: "extractBankBranch"
    
    bank_region:
      source_path: "form.widget16657399953960001.value.widgetAccountBankArea"
      target_field: "bank_region"
      data_type: "string"
      required: false
      description: "银行地区"
      transform: "extractBankRegion"
    
    # 其他信息
    expected_payment_date:
      source_path: "form.widget3.value"
      target_field: "expected_payment_date"
      data_type: "time"
      required: false
      description: "期望付款日期"
    
    attachments:
      source_path: "form.widget15828099482720001.value"
      target_field: "attachments"
      data_type: "array"
      required: false
      description: "附件"
      transform: "parseAttachmentsWithUrls"
    
    remarks:
      source_path: "form.widget16638253466950001.value"
      target_field: "remarks"
      data_type: "string"
      required: false
      description: "说明备注"
  
  # 默认值配置
  default_values:
    business_type: "付款申请"
    data_version: "1.0"
    payment_currency: "人民币"
  
  # 必填字段列表
  required_fields:
    - "application_number"
    - "title"
    - "payment_reason"
    - "payment_entity"
    - "contract_sign_amount"
    - "current_request_amount"
    - "account_holder"
    - "account_number"
    - "bank_name"
    - "initiator_user_id"
  
  # 验证规则
  validation_rules:
    - field: "application_number"
      rule: "required"
      parameter: ""
      message: "申请编号不能为空"
    
    - field: "payment_reason"
      rule: "required"
      parameter: ""
      message: "付款事由不能为空"
    
    - field: "current_request_amount"
      rule: "range"
      parameter: "0.01,*********"
      message: "本次请款金额必须大于0"
    
    - field: "contract_sign_amount"
      rule: "range"
      parameter: "0.01,*********"
      message: "合同签约金额必须大于0"
    
    - field: "account_number"
      rule: "format"
      parameter: "^[0-9]{10,30}$"
      message: "账户号码格式不正确"
