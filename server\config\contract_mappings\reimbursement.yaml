# 付款申请审批映射规则配置
# 基于实际表单数据生成的配置
PAYMENT_REQUEST_APPROVAL:
  approval_code: "PAYMENT_REQUEST_APPROVAL"
  approval_name: "付款申请审批"
  
  # 字段映射配置
  field_mappings:
    # 基础信息字段
    application_number:
      source_path: "basic.instance_code"
      target_field: "application_number"
      data_type: "string"
      required: true
      description: "申请编号"
    
    title:
      source_path: "basic.approval_name"
      target_field: "title"
      data_type: "string"
      required: true
      description: "标题"
    
    application_status:
      source_path: "basic.status"
      target_field: "application_status"
      data_type: "string"
      required: true
      description: "申请状态"
    
    initiate_time:
      source_path: "basic.start_time"
      target_field: "initiate_time"
      data_type: "time"
      required: true
      description: "发起时间"
    
    complete_time:
      source_path: "basic.end_time"
      target_field: "complete_time"
      data_type: "time"
      required: false
      description: "完成时间"
    
    # 人员信息字段
    initiator_user_id:
      source_path: "basic.user_id"
      target_field: "initiator_user_id"
      data_type: "string"
      required: true
      description: "发起人User ID"
    
    initiator_department_id:
      source_path: "basic.department_id"
      target_field: "initiator_department_id"
      data_type: "string"
      required: false
      description: "发起人部门ID"
    
    # 流水号 - widget16856053045110001
    serial_number:
      source_path: "form.widget16856053045110001.value"
      target_field: "serial_number"
      data_type: "string"
      required: false
      description: "流水号"
    
    # 付款事由 - widget0
    payment_reason:
      source_path: "form.widget0.value"
      target_field: "payment_reason"
      data_type: "string"
      required: false
      description: "付款事由"

    # 付款主体 - widget16423899380530001
    payment_entity:
      source_path: "form.widget16423899380530001.value"
      target_field: "payment_entity"
      data_type: "string"
      required: false
      description: "付款主体"

    # 业务类型 - widget16701426685000001
    business_type:
      source_path: "form.widget16701426685000001.value"
      target_field: "business_type"
      data_type: "string"
      required: false
      description: "业务类型"

    # 费用所属部门 - widget16709264180350001
    expense_department:
      source_path: "form.widget16709264180350001.value[0].name"
      target_field: "expense_department"
      data_type: "string"
      required: false
      description: "费用所属部门"

    # 付款币种 - widget16799083691540001
    payment_currency:
      source_path: "form.widget16799083691540001.value"
      target_field: "payment_currency"
      data_type: "string"
      required: false
      description: "付款币种"

    # 合同签约金额 - widget16671961125560001
    contract_sign_amount:
      source_path: "form.widget16671961125560001.value"
      target_field: "contract_sign_amount"
      data_type: "float"
      required: false
      description: "合同签约金额"

    # 合同已付金额 - widget16671961212400001
    contract_paid_amount:
      source_path: "form.widget16671961212400001.value"
      target_field: "contract_paid_amount"
      data_type: "float"
      required: false
      default_value: 0
      description: "合同已付金额"

    # 本次请款金额 - widget1
    current_request_amount:
      source_path: "form.widget1.value"
      target_field: "current_request_amount"
      data_type: "float"
      required: false
      description: "本次请款金额"

    # 增值税发票类型 - widget17466051580200001
    vat_invoice_type:
      source_path: "form.widget17466051580200001.value"
      target_field: "vat_invoice_type"
      data_type: "string"
      required: false
      description: "增值税发票类型"

    # 银行信息 - widget16657399953960001
    account_holder:
      source_path: "form.widget16657399953960001.value.widgetAccountName"
      target_field: "account_holder"
      data_type: "string"
      required: false
      description: "收款方户名"

    account_type:
      source_path: "form.widget16657399953960001.value.widgetAccountType.text"
      target_field: "account_type"
      data_type: "string"
      required: false
      description: "账户类型"

    account_number:
      source_path: "form.widget16657399953960001.value.widgetAccountNumber"
      target_field: "account_number"
      data_type: "string"
      required: false
      description: "账户号码"

    bank_name:
      source_path: "form.widget16657399953960001.value.widgetAccountBankName.text"
      target_field: "bank_name"
      data_type: "string"
      required: false
      transform: "extractBankName"
      description: "银行名称"

    bank_branch:
      source_path: "form.widget16657399953960001.value.widgetAccountBankBranch.text"
      target_field: "bank_branch"
      data_type: "string"
      required: false
      transform: "extractBankBranch"
      description: "银行支行"

    bank_region:
      source_path: "form.widget16657399953960001.value.widgetAccountBankArea.text"
      target_field: "bank_region"
      data_type: "string"
      required: false
      transform: "extractBankRegion"
      description: "银行所在地区"

    # 期望付款日期 - widget3
    expected_payment_date:
      source_path: "form.widget3.value"
      target_field: "expected_payment_date"
      data_type: "time"
      required: false
      description: "期望付款日期"

    # 附件 - widget15828099482720001
    attachments:
      source_path: "form.widget15828099482720001"
      target_field: "attachments"
      data_type: "array"
      required: false
      transform: "parseAttachmentsWithUrls"
      description: "附件列表（包含文件名和URL）"

  # 默认值配置
  default_values:
    data_version: "1.0"
    payment_currency: "人民币"
    contract_paid_amount: 0

  # 必填字段列表
  required_fields:
    - "application_number"
    - "title"
    - "application_status"
    - "initiate_time"
    - "initiator_user_id"
    - "payment_reason"
    - "payment_entity"
    - "payment_currency"

  # 验证规则
  validation_rules:
    - field: "application_number"
      rule: "required"
      parameter: ""
      message: "申请编号不能为空"