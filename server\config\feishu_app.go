package config

// FeishuApp 飞书应用配置
type FeishuApp struct {
	Appid         string               `mapstructure:"app-id" json:"app-id" yaml:"app-id"`
	AppSecret     string               `mapstructure:"app-secret" json:"app-secret" yaml:"app-secret"`
	ApprovalCodes []ApprovalCodeConfig `mapstructure:"approval-codes" json:"approval-codes" yaml:"approval-codes"`
}

// ApprovalCodeConfig 审批代码配置
type ApprovalCodeConfig struct {
	// 审批代码
	Code string `mapstructure:"code" json:"code" yaml:"code"`
	// 审批名称
	Name string `mapstructure:"name" json:"name" yaml:"name"`
	// 审批描述
	Description string `mapstructure:"description" json:"description" yaml:"description"`
	// 是否启用
	Enabled bool `mapstructure:"enabled" json:"enabled" yaml:"enabled"`
	// 业务类型标签（可选，用于分类）
	Tags []string `mapstructure:"tags" json:"tags" yaml:"tags"`
}
