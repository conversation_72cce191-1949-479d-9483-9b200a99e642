[insbuy]2025/08/01 - 10:11:20.776	[31<PERSON><PERSON>r[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 10:23:01.769	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:32:04.861	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:35:16.217	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:35:16.509	[31merror[0m	test/contract_transformer_test.go:574	未找到匹配的映射规则	{"traceId": "58ac31309ac0bf147e20d0b4a0b97468", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "available_rules": 2}
[insbuy]2025/08/01 - 15:35:16.509	[31merror[0m	test/contract_transformer_test.go:442	选择映射规则失败	{"traceId": "0592e5966fe7c08071f7b434677b85b9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "error": "未找到审批代码 0B92F2B5-922F-4570-8A83-489E476FF811 的映射规则，可用规则: [F523F053-7AC6-4280-A4E7-B35E0C0431B5 PAYMENT_REQUEST_APPROVAL]"}
[insbuy]2025/08/01 - 15:54:56.337	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:54:56.622	[31merror[0m	insbuy/contract_transformer.go:252	数据转换失败	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "error": "提取必填字段 account_number 失败: 字段 widget_account 不存在"}
[insbuy]2025/08/01 - 15:54:56.622	[31merror[0m	test/contract_transformer_test.go:461	数据转换失败	{"traceId": "e56274babb794d7db2ddab27743b61db", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "error": "提取必填字段 account_number 失败: 字段 widget_account 不存在"}
[insbuy]2025/08/01 - 15:57:11.355	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:57:11.594	[31merror[0m	insbuy/contract_transformer.go:252	数据转换失败	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "error": "提取必填字段 reimbursement_reason 失败: 字段 widget_reason 不存在"}
[insbuy]2025/08/01 - 15:57:11.594	[31merror[0m	test/contract_transformer_test.go:461	数据转换失败	{"traceId": "984b271cef3331a8b95b46f6f8231dd6", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "error": "提取必填字段 reimbursement_reason 失败: 字段 widget_reason 不存在"}
[insbuy]2025/08/01 - 15:59:19.322	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 15:59:19.607	[31merror[0m	insbuy/contract_transformer.go:252	数据转换失败	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "error": "提取必填字段 expense_details 失败: 字段 widget_expense_list 不存在"}
[insbuy]2025/08/01 - 15:59:19.607	[31merror[0m	test/contract_transformer_test.go:461	数据转换失败	{"traceId": "b30496c1b864351f61a6b2a8a140e0cf", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "error": "提取必填字段 expense_details 失败: 字段 widget_expense_list 不存在"}
[insbuy]2025/08/01 - 16:02:25.173	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 16:02:25.461	[31merror[0m	insbuy/contract_transformer.go:252	数据转换失败	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "error": "提取必填字段 payment_company 失败: 字段 widget_payment_company 不存在"}
[insbuy]2025/08/01 - 16:02:25.461	[31merror[0m	test/contract_transformer_test.go:461	数据转换失败	{"traceId": "8b517be81c511191a6f7e992a0bdf771", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "error": "提取必填字段 payment_company 失败: 字段 widget_payment_company 不存在"}
[insbuy]2025/08/01 - 16:04:15.066	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 16:04:15.351	[31merror[0m	insbuy/contract_transformer.go:252	数据转换失败	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "error": "提取必填字段 expense_department 失败: 字段 widget_department 不存在"}
[insbuy]2025/08/01 - 16:04:15.351	[31merror[0m	test/contract_transformer_test.go:461	数据转换失败	{"traceId": "1100e5dc14b3af0b29150a026d34cf48", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "error": "提取必填字段 expense_department 失败: 字段 widget_department 不存在"}
[insbuy]2025/08/01 - 16:06:17.967	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 16:11:11.041	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 16:11:11.411	[31merror[0m	insbuy/contract_transformer.go:252	数据转换失败	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "error": "设置字段 payment_company 失败: 未知字段: payment_company"}
[insbuy]2025/08/01 - 16:11:11.411	[31merror[0m	test/contract_transformer_test.go:461	数据转换失败	{"traceId": "d4b44bbd8559e07147a71840ca017fb8", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "error": "设置字段 payment_company 失败: 未知字段: payment_company"}
[insbuy]2025/08/01 - 16:27:00.884	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 16:29:37.543	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 17:00:00.376	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
[insbuy]2025/08/01 - 17:23:41.701	[31merror[0m	initialize/redis.go:22	redis connect ping failed, err:	{"error": "dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it."}
