[insbuy]2025/08/01 - 10:11:20.852	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 10:23:01.856	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 15:35:16.303	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 15:35:16.490	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "0592e5966fe7c08071f7b434677b85b9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 15:35:16.505	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "0592e5966fe7c08071f7b434677b85b9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 15:35:16.505	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "84ded430fb63dc97743fb43c01d68976", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 15:35:16.505	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "c784671b1c41114e2a0772f5879f8a7d", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 15:35:16.506	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "2918877334b6bcfeaab7e401c66231a1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 15:35:16.507	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "2918877334b6bcfeaab7e401c66231a1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 15:35:16.507	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "2918877334b6bcfeaab7e401c66231a1", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 15:35:16.507	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ed76d5044853b272051b7156767a2386", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "ed76d5044853b272051b7156767a2386", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "ed76d5044853b272051b7156767a2386", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "c784671b1c41114e2a0772f5879f8a7d", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 2, "total_rules": 2}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "84ded430fb63dc97743fb43c01d68976", "task": "LoadAllMappingConfigs", "total_rules": 2}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "84ded430fb63dc97743fb43c01d68976", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "84ded430fb63dc97743fb43c01d68976", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 15:35:16.509	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "58ac31309ac0bf147e20d0b4a0b97468", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 15:54:56.412	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 15:54:56.599	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "e56274babb794d7db2ddab27743b61db", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 15:54:56.614	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "e56274babb794d7db2ddab27743b61db", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 15:54:56.614	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "2cd284f64bd25a5fb4b44fab500e54da", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 15:54:56.614	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "706b1db8946f31d22dac17673c324cec", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 15:54:56.614	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "14b05a1aa328e91eee01c5824f299db8", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 15:54:56.618	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "14b05a1aa328e91eee01c5824f299db8", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 15:54:56.618	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "14b05a1aa328e91eee01c5824f299db8", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 15:54:56.618	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "dff20027286125d60837a4a9d9e213e4", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 15:54:56.620	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "dff20027286125d60837a4a9d9e213e4", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 15:54:56.620	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "dff20027286125d60837a4a9d9e213e4", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 15:54:56.620	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "b52e1d8c49276e498b31cfcfb1722fd9", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "b52e1d8c49276e498b31cfcfb1722fd9", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "b52e1d8c49276e498b31cfcfb1722fd9", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "706b1db8946f31d22dac17673c324cec", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "2cd284f64bd25a5fb4b44fab500e54da", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "2cd284f64bd25a5fb4b44fab500e54da", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 22}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "2cd284f64bd25a5fb4b44fab500e54da", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "2cd284f64bd25a5fb4b44fab500e54da", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "1e206db7e94ba7e5f3317c1c31deeddd", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "1e206db7e94ba7e5f3317c1c31deeddd", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 15:54:56.621	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "e56274babb794d7db2ddab27743b61db", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 15:54:56.622	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 15:57:11.426	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 15:57:11.569	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "984b271cef3331a8b95b46f6f8231dd6", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 15:57:11.588	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "984b271cef3331a8b95b46f6f8231dd6", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 15:57:11.589	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "68028639fbf217d9f551b60ddbed9a68", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 15:57:11.589	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "52e7bef76d4a987083cc8024a2f8abac", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 15:57:11.589	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "3161cc58ab6877c8aea3553d760eb031", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 15:57:11.591	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "3161cc58ab6877c8aea3553d760eb031", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 15:57:11.591	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "3161cc58ab6877c8aea3553d760eb031", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 15:57:11.591	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "88a9d933d394ee942b76ed58d041e066", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 15:57:11.591	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "88a9d933d394ee942b76ed58d041e066", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 15:57:11.592	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "88a9d933d394ee942b76ed58d041e066", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 15:57:11.592	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "21dbce6592fd89381ae2dde17da13454", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 15:57:11.592	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "21dbce6592fd89381ae2dde17da13454", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 15:57:11.592	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "21dbce6592fd89381ae2dde17da13454", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 15:57:11.592	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "52e7bef76d4a987083cc8024a2f8abac", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 15:57:11.592	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "68028639fbf217d9f551b60ddbed9a68", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 15:57:11.592	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "68028639fbf217d9f551b60ddbed9a68", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 22}
[insbuy]2025/08/01 - 15:57:11.592	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "68028639fbf217d9f551b60ddbed9a68", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 15:57:11.593	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "68028639fbf217d9f551b60ddbed9a68", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 15:57:11.593	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "d0984bd88c877762dff735b11780ea0a", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 15:57:11.593	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "d0984bd88c877762dff735b11780ea0a", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 15:57:11.593	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "984b271cef3331a8b95b46f6f8231dd6", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 15:57:11.594	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 15:59:19.396	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 15:59:19.584	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "b30496c1b864351f61a6b2a8a140e0cf", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 15:59:19.599	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "b30496c1b864351f61a6b2a8a140e0cf", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 15:59:19.599	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "40378364cf4b1157240c99dd57716a4c", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 15:59:19.599	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "195105bafc3aa8c09c1dbec265396011", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 15:59:19.600	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "9471b91d9d036002dcb6c40dc973a760", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 15:59:19.602	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "9471b91d9d036002dcb6c40dc973a760", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 15:59:19.603	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "9471b91d9d036002dcb6c40dc973a760", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 15:59:19.603	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "61ad7f07f154c2c9e66e12c03f2a6448", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 15:59:19.604	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "61ad7f07f154c2c9e66e12c03f2a6448", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 15:59:19.604	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "61ad7f07f154c2c9e66e12c03f2a6448", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 15:59:19.605	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "c26343c59a325b73e326a69dc87511f8", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 15:59:19.605	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "c26343c59a325b73e326a69dc87511f8", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "c26343c59a325b73e326a69dc87511f8", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "195105bafc3aa8c09c1dbec265396011", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "40378364cf4b1157240c99dd57716a4c", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "40378364cf4b1157240c99dd57716a4c", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 22}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "40378364cf4b1157240c99dd57716a4c", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "40378364cf4b1157240c99dd57716a4c", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "b94f5ec692852d68e49da6517192a7c6", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "b94f5ec692852d68e49da6517192a7c6", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 15:59:19.606	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "b30496c1b864351f61a6b2a8a140e0cf", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 15:59:19.607	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:02:25.248	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 16:02:25.441	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "8b517be81c511191a6f7e992a0bdf771", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 16:02:25.454	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "8b517be81c511191a6f7e992a0bdf771", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 16:02:25.454	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "13c7c3b82f6baa47da6e83dd393ac8d2", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 16:02:25.454	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "8aa36ef0871e81609bc6d588076fa607", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 16:02:25.455	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ccae27ae0a40054165f85e4da91a80a3", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 16:02:25.457	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "ccae27ae0a40054165f85e4da91a80a3", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 16:02:25.457	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "ccae27ae0a40054165f85e4da91a80a3", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 16:02:25.457	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "6a3f492a3a5bce8e301a9c32e4e78b20", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 16:02:25.458	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "6a3f492a3a5bce8e301a9c32e4e78b20", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 16:02:25.458	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "6a3f492a3a5bce8e301a9c32e4e78b20", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 16:02:25.458	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "82c5e95c1b17650a0d8ebbe9179e5a26", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "82c5e95c1b17650a0d8ebbe9179e5a26", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "82c5e95c1b17650a0d8ebbe9179e5a26", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "8aa36ef0871e81609bc6d588076fa607", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "13c7c3b82f6baa47da6e83dd393ac8d2", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "13c7c3b82f6baa47da6e83dd393ac8d2", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 22}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "13c7c3b82f6baa47da6e83dd393ac8d2", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "13c7c3b82f6baa47da6e83dd393ac8d2", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "c6be7f934b2c7de80e6e47d3fb04f357", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "c6be7f934b2c7de80e6e47d3fb04f357", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 16:02:25.460	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "8b517be81c511191a6f7e992a0bdf771", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 16:02:25.461	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:04:15.140	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 16:04:15.329	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "1100e5dc14b3af0b29150a026d34cf48", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 16:04:15.344	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "1100e5dc14b3af0b29150a026d34cf48", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 16:04:15.344	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "f8c886af21eee5044006085b5c0a7096", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 16:04:15.344	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "0712fa2d622e238c545de121f7a14084", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 16:04:15.345	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "b429fe0a069251bda859c48034b58af8", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 16:04:15.347	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "b429fe0a069251bda859c48034b58af8", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 16:04:15.348	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "b429fe0a069251bda859c48034b58af8", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 16:04:15.348	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "a3eb287d88b3e2a2e944131d29193e84", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 16:04:15.349	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "a3eb287d88b3e2a2e944131d29193e84", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 16:04:15.349	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "a3eb287d88b3e2a2e944131d29193e84", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 16:04:15.349	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "00044777478c75330bccbd07068ab2cf", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 16:04:15.350	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "00044777478c75330bccbd07068ab2cf", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 16:04:15.350	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "00044777478c75330bccbd07068ab2cf", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 16:04:15.350	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "0712fa2d622e238c545de121f7a14084", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 16:04:15.350	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "f8c886af21eee5044006085b5c0a7096", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 16:04:15.350	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "f8c886af21eee5044006085b5c0a7096", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 22}
[insbuy]2025/08/01 - 16:04:15.350	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "f8c886af21eee5044006085b5c0a7096", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 16:04:15.350	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "f8c886af21eee5044006085b5c0a7096", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 16:04:15.351	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "125d5482901213f8398308348e4063b5", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:04:15.351	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "125d5482901213f8398308348e4063b5", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 16:04:15.351	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "1100e5dc14b3af0b29150a026d34cf48", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 16:04:15.351	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:06:18.058	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 16:06:18.213	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "f5e8c33d743a1d07917e4c3a997765fd", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 16:06:18.228	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "f5e8c33d743a1d07917e4c3a997765fd", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 16:06:18.228	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "04f488beaf9a7c7705c58c23ce0fdb59", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 16:06:18.229	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "6618f1690a7b66f26b20dd1899640897", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 16:06:18.229	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "cf3db290a9de3fdc4c5c3578b488f6f6", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 16:06:18.230	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "cf3db290a9de3fdc4c5c3578b488f6f6", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 16:06:18.230	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "cf3db290a9de3fdc4c5c3578b488f6f6", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 16:06:18.230	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "c50df209f622cf5c637f97d59d225276", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 16:06:18.231	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "c50df209f622cf5c637f97d59d225276", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 16:06:18.232	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "c50df209f622cf5c637f97d59d225276", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 16:06:18.232	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "7841a7140d36299dca859137b00b437f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 16:06:18.233	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "7841a7140d36299dca859137b00b437f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 16:06:18.233	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "7841a7140d36299dca859137b00b437f", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 16:06:18.233	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "6618f1690a7b66f26b20dd1899640897", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 16:06:18.233	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "04f488beaf9a7c7705c58c23ce0fdb59", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 16:06:18.233	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "04f488beaf9a7c7705c58c23ce0fdb59", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 22}
[insbuy]2025/08/01 - 16:06:18.233	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "04f488beaf9a7c7705c58c23ce0fdb59", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 16:06:18.233	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "04f488beaf9a7c7705c58c23ce0fdb59", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 16:06:18.234	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "fc02565e1c22e9dc37900f2c5d14a213", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:06:18.234	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "fc02565e1c22e9dc37900f2c5d14a213", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 16:06:18.234	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "f5e8c33d743a1d07917e4c3a997765fd", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 22}
[insbuy]2025/08/01 - 16:06:18.234	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:06:18.234	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "", "account_type": "", "account_number": "", "bank_name": "", "bank_branch": "", "bank_region": ""}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [], "remarks": ""}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/01 - 16:06:18.234", "data_version": "1.0"}
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/01 - 16:06:18.268	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "f5e8c33d743a1d07917e4c3a997765fd", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250801_160618.xlsx", "success": false, "processing_time": 0}
[insbuy]2025/08/01 - 16:11:11.156	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 16:11:11.389	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "d4b44bbd8559e07147a71840ca017fb8", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 16:11:11.402	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "d4b44bbd8559e07147a71840ca017fb8", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 16:11:11.402	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "fc79f6456e53dede1c4bf146eff73064", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 16:11:11.403	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "d58df3933aa739251893e73ab79538dc", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 16:11:11.403	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "4fec4a5f472cc454e1e68d7628338472", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 16:11:11.405	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "4fec4a5f472cc454e1e68d7628338472", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 16:11:11.406	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "4fec4a5f472cc454e1e68d7628338472", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 16:11:11.406	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "06c66ccb49f993547435447b5f4378df", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 16:11:11.408	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "06c66ccb49f993547435447b5f4378df", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 16:11:11.408	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "06c66ccb49f993547435447b5f4378df", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 16:11:11.408	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "8723b72ebf591eb63b72441419d32bae", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "8723b72ebf591eb63b72441419d32bae", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "8723b72ebf591eb63b72441419d32bae", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "d58df3933aa739251893e73ab79538dc", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "fc79f6456e53dede1c4bf146eff73064", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "fc79f6456e53dede1c4bf146eff73064", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "fc79f6456e53dede1c4bf146eff73064", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "fc79f6456e53dede1c4bf146eff73064", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "d169dbe3de727a03ac6948e961f0232e", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "d169dbe3de727a03ac6948e961f0232e", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 16:11:11.410	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "d4b44bbd8559e07147a71840ca017fb8", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 16:11:11.411	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:27:00.979	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 16:27:01.198	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "df1354371dad45b8b2d83c3984b7c3d9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 16:27:01.221	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "df1354371dad45b8b2d83c3984b7c3d9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 16:27:01.221	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "8d153de9206f7028eb964f51cc5d2e97", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 16:27:01.221	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "1f8f18695578ff54a30e53e1c86ca314", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 16:27:01.221	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "62f919d246153cfda5b91a32be69e9d3", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 16:27:01.228	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "62f919d246153cfda5b91a32be69e9d3", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 16:27:01.228	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "62f919d246153cfda5b91a32be69e9d3", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 16:27:01.228	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ab5965b85e2213a0cf01159dd80b6251", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 16:27:01.230	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "ab5965b85e2213a0cf01159dd80b6251", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 16:27:01.230	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "ab5965b85e2213a0cf01159dd80b6251", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 16:27:01.230	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "11c3234ef08b417241cc6c012912e2f4", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 16:27:01.231	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "11c3234ef08b417241cc6c012912e2f4", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 16:27:01.231	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "11c3234ef08b417241cc6c012912e2f4", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 16:27:01.232	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "1f8f18695578ff54a30e53e1c86ca314", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 16:27:01.232	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "8d153de9206f7028eb964f51cc5d2e97", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 16:27:01.232	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "8d153de9206f7028eb964f51cc5d2e97", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/01 - 16:27:01.232	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "8d153de9206f7028eb964f51cc5d2e97", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 16:27:01.232	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "8d153de9206f7028eb964f51cc5d2e97", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 16:27:01.232	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "cbaec6c470f79499328f10f504f07dd5", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:27:01.232	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "cbaec6c470f79499328f10f504f07dd5", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 16:27:01.232	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "df1354371dad45b8b2d83c3984b7c3d9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 16:27:01.233	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:27:01.233	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0010001}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0010001, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/01 - 16:27:01.233", "data_version": "1.0"}
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/01 - 16:27:01.266	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "df1354371dad45b8b2d83c3984b7c3d9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250801_162701.xlsx", "success": false, "processing_time": 0.0010001}
[insbuy]2025/08/01 - 16:29:37.634	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 16:29:37.787	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "5babb57a2bd35d11d1dbe4e9fa2d0ba1", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 16:29:37.867	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "5babb57a2bd35d11d1dbe4e9fa2d0ba1", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 16:29:37.867	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "729821c50b2c240fb9399d56a0fb553f", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 16:29:37.867	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "df2627cd4c1fcf640576ea6adc2c14a7", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 16:29:37.868	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "5800169eb083d11a3109c140e69f22e0", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 16:29:37.875	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "5800169eb083d11a3109c140e69f22e0", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 16:29:37.875	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "5800169eb083d11a3109c140e69f22e0", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 16:29:37.875	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "256ed39940c708deb494e1f8289157c8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 16:29:37.877	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "256ed39940c708deb494e1f8289157c8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 16:29:37.877	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "256ed39940c708deb494e1f8289157c8", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 16:29:37.877	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "89a563cf702b917bb59ebabfdd892e02", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "89a563cf702b917bb59ebabfdd892e02", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "89a563cf702b917bb59ebabfdd892e02", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "df2627cd4c1fcf640576ea6adc2c14a7", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "729821c50b2c240fb9399d56a0fb553f", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "729821c50b2c240fb9399d56a0fb553f", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "729821c50b2c240fb9399d56a0fb553f", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "729821c50b2c240fb9399d56a0fb553f", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "dd5063ea93ca6b91933a28ecbe2b9113", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "dd5063ea93ca6b91933a28ecbe2b9113", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 16:29:37.879	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "5babb57a2bd35d11d1dbe4e9fa2d0ba1", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 16:29:37.880	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 16:29:37.880	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005355}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005355, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/01 - 16:29:37.880", "data_version": "1.0"}
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/01 - 16:29:37.914	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "5babb57a2bd35d11d1dbe4e9fa2d0ba1", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250801_162937.xlsx", "success": false, "processing_time": 0.0005355}
[insbuy]2025/08/01 - 17:00:00.461	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 17:00:00.681	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "5a4784aaeb58abd4596e7c47414d3e5a", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 17:00:00.696	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "5a4784aaeb58abd4596e7c47414d3e5a", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 17:00:00.697	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "815803b10e222690e6e3613970eb9f1e", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 17:00:00.697	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "379f96eafd0bd690f89a9c5908154ad5", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 17:00:00.697	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "9ebaf8c7a96d994157720a3ebe7da6b1", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 17:00:00.699	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "9ebaf8c7a96d994157720a3ebe7da6b1", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 17:00:00.699	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "9ebaf8c7a96d994157720a3ebe7da6b1", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 17:00:00.699	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "b3b6d712b39d5155ef18fb7d4d9649a3", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 17:00:00.700	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "b3b6d712b39d5155ef18fb7d4d9649a3", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 17:00:00.700	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "b3b6d712b39d5155ef18fb7d4d9649a3", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 17:00:00.701	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "e5e1287eebf61c627c19ab700b37f4c0", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "e5e1287eebf61c627c19ab700b37f4c0", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "e5e1287eebf61c627c19ab700b37f4c0", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "379f96eafd0bd690f89a9c5908154ad5", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "815803b10e222690e6e3613970eb9f1e", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "815803b10e222690e6e3613970eb9f1e", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "815803b10e222690e6e3613970eb9f1e", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "815803b10e222690e6e3613970eb9f1e", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "bd77aaead61f3bc2334becc468e7a01d", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "bd77aaead61f3bc2334becc468e7a01d", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "5a4784aaeb58abd4596e7c47414d3e5a", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 17:00:00.702	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 17:00:00.703	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0.0005085}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0.0005085, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/01 - 17:00:00.703", "data_version": "1.0"}
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/01 - 17:00:00.741	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "5a4784aaeb58abd4596e7c47414d3e5a", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250801_170000.xlsx", "success": false, "processing_time": 0.0005085}
[insbuy]2025/08/01 - 17:23:41.785	[34minfo[0m	jkafka/jkafka_nokafka.go:13	kafka未启用	{"cfg": {"Srv":["alikafka-pre-cn-5yd3iirzq001-1.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-2.alikafka.aliyuncs.com:9093","alikafka-pre-cn-5yd3iirzq001-3.alikafka.aliyuncs.com:9093"],"TopicMap":{"bookdesk":"bookdesk-test"},"GroupId":"bookdesk-test-cd-dev-group","SecurityProtocol":"sasl_ssl","CaFile":"./kafka-ali-only-4096-ca-cert.pem","SASL":{"Mechanism":"PLAIN","Username":"alikafka_pre-cn-5yd3iirzq001","Password":"rmwGqzVrr4E1D8R0UoiCU2vRP3X7WLqJ"}}}
[insbuy]2025/08/01 - 17:23:41.945	[34minfo[0m	test/contract_transformer_test.go:413	开始通用数据导出测试	{"traceId": "93621e78f67a87a10b1169c0f472ccc3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583"}
[insbuy]2025/08/01 - 17:23:41.961	[34minfo[0m	test/contract_transformer_test.go:426	成功查询到合同数据	{"traceId": "93621e78f67a87a10b1169c0f472ccc3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销", "status": "CANCELED"}
[insbuy]2025/08/01 - 17:23:41.961	[34minfo[0m	test/contract_transformer_test.go:503	开始加载所有映射配置文件	{"traceId": "44449a41455fd8a8d2c974690ef2e592", "task": "LoadAllMappingConfigs"}
[insbuy]2025/08/01 - 17:23:41.961	[34minfo[0m	insbuy/contract_mapping_config.go:93	开始加载映射规则目录	{"traceId": "ff9a5ab9cb1f5b98697a4b5401b92863", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings"}
[insbuy]2025/08/01 - 17:23:41.962	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "ee503a1719965f4f777b57e925d7c548", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml"}
[insbuy]2025/08/01 - 17:23:41.963	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "ee503a1719965f4f777b57e925d7c548", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 17:23:41.963	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "ee503a1719965f4f777b57e925d7c548", "task": "LoadMappingRulesFromFile", "filename": "expense_reimbursement_0B92F2B5.yaml", "loaded_rules": 1, "total_rules": 1}
[insbuy]2025/08/01 - 17:23:41.963	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "0af374290ef570d11d5ace5ff3269018", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml"}
[insbuy]2025/08/01 - 17:23:41.963	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "0af374290ef570d11d5ace5ff3269018", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings_count": 27}
[insbuy]2025/08/01 - 17:23:41.963	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "0af374290ef570d11d5ace5ff3269018", "task": "LoadMappingRulesFromFile", "filename": "payment_application.yaml", "loaded_rules": 1, "total_rules": 2}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	insbuy/contract_mapping_config.go:36	开始加载映射规则文件	{"traceId": "5649f48e703b3911003edcd2d1735449", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml"}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	insbuy/contract_mapping_config.go:68	成功加载映射规则	{"traceId": "5649f48e703b3911003edcd2d1735449", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings_count": 25}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	insbuy/contract_mapping_config.go:78	映射规则文件加载完成	{"traceId": "5649f48e703b3911003edcd2d1735449", "task": "LoadMappingRulesFromFile", "filename": "reimbursement.yaml", "loaded_rules": 1, "total_rules": 3}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	insbuy/contract_mapping_config.go:125	映射规则目录加载完成	{"traceId": "ff9a5ab9cb1f5b98697a4b5401b92863", "task": "LoadMappingRulesFromDirectory", "config_path": "./config/contract_mappings", "loaded_files": 3, "total_rules": 3}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	test/contract_transformer_test.go:514	成功加载映射配置	{"traceId": "44449a41455fd8a8d2c974690ef2e592", "task": "LoadAllMappingConfigs", "total_rules": 3}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "44449a41455fd8a8d2c974690ef2e592", "task": "LoadAllMappingConfigs", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_name": "费用报销申请单", "field_mappings": 21}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "44449a41455fd8a8d2c974690ef2e592", "task": "LoadAllMappingConfigs", "approval_code": "F523F053-7AC6-4280-A4E7-B35E0C0431B5", "approval_name": "付款申请审批", "field_mappings": 27}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	test/contract_transformer_test.go:520	已加载映射规则	{"traceId": "44449a41455fd8a8d2c974690ef2e592", "task": "LoadAllMappingConfigs", "approval_code": "PAYMENT_REQUEST_APPROVAL", "approval_name": "付款申请审批", "field_mappings": 25}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	test/contract_transformer_test.go:537	开始选择映射规则	{"traceId": "fb0f9130dd165041059eb59a82419757", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	test/contract_transformer_test.go:542	找到精确匹配的映射规则	{"traceId": "fb0f9130dd165041059eb59a82419757", "task": "SelectMappingRule", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_name": "费用报销申请单"}
[insbuy]2025/08/01 - 17:23:41.964	[34minfo[0m	test/contract_transformer_test.go:446	成功选择映射规则	{"traceId": "93621e78f67a87a10b1169c0f472ccc3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "rule_approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "rule_approval_name": "费用报销申请单", "field_mappings_count": 21}
[insbuy]2025/08/01 - 17:23:41.965	[34minfo[0m	insbuy/contract_transformer.go:231	开始转换合同数据	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811"}
[insbuy]2025/08/01 - 17:23:41.965	[34minfo[0m	insbuy/contract_transformer.go:286	合同数据转换完成	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "success": false, "error_count": 1, "warning_count": 0, "processing_time": 0}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:170	=== 数据库数据转换测试结果 ===
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:173	原始数据库数据:	{"instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_name": "费用报销", "status": "CANCELED", "created_at": "[insbuy]2025/07/28 - 18:08:16.725"}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:181	转换结果:	{"success": false, "processing_time": 0, "error_count": 1, "warning_count": 0}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:190	转换错误:
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:192	错误详情	{"index": 1, "field": "initiate_time", "message": "发起时间不能为空", "value": "0001-01-01 00:00:00 +0000 UTC"}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:215	标准化数据 - 基础信息:	{"application_number": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "title": "费用报销", "application_status": "CANCELED", "initiate_time": "[insbuy]0001/01/01 - 00:00:00.000", "complete_time": "[insbuy]0001/01/01 - 00:00:00.000"}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:223	标准化数据 - 人员信息:	{"initiator_user_id": "a121742e", "initiator_department_id": "12g84b14gdbd76a2", "serial_number": "20250629001737"}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:229	标准化数据 - 业务信息:	{"payment_reason": "绿洲OASIS舞台部4-6月PROGRAM制作费", "payment_entity": "卡谱笛目（上海）文化传播有限公司", "business_type": "", "expense_department": "卡谱-绿洲OASIS", "payment_currency": ""}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:237	标准化数据 - 金额信息:	{"contract_sign_amount": 0, "contract_paid_amount": 0, "current_request_amount": 0}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:243	标准化数据 - 财务信息:	{"vat_invoice_type": "", "tax_rate": 0}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:248	标准化数据 - 银行信息:	{"account_holder": "陶柯", "account_type": "1", "account_number": "****************", "bank_name": "CMB", "bank_branch": "112813", "bank_region": "1814991,1794299,1815286"}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:257	标准化数据 - 其他信息:	{"expected_payment_date": "[insbuy]0001/01/01 - 00:00:00.000", "attachments": [{"file_name":"https://internal-api-drive-stream.feishu.cn/space/api/box/stream/download/authcode/?code=YzJjY2M3MmUxNmM2OTJmMGE2YWIxM2IwN2NkNTFjZTNfZDRhM2EzNTRiMzVkN2E2MTc3M2IyYWYxMjIyYTk1NGNfSUQ6NzUyMTI0ODAyNTM0MDMxMzYyOF8xNzUzNjk3NDM2OjE3NTM3ODM4MzZfVjM","file_url":""}], "remarks": "4月合计program制作30首，5月合计program制作13首，6月合计program制作3首，共计46首"}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:263	标准化数据 - 元数据:	{"source_instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "approval_code": "0B92F2B5-922F-4570-8A83-489E476FF811", "transform_time": "[insbuy]2025/08/01 - 17:23:41.965", "data_version": "1.0"}
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:271	=== 转换测试结果结束 ===
[insbuy]2025/08/01 - 17:23:42.006	[34minfo[0m	test/contract_transformer_test.go:488	通用数据导出测试完成	{"traceId": "93621e78f67a87a10b1169c0f472ccc3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "output_file": "approval_export_72ECAA63-24BF-4E52-BE1C-74A64DA51583_20250801_172341.xlsx", "success": false, "processing_time": 0}
