[insbuy]2025/08/01 - 10:12:31.095	[33mwarn[0m	insreport/state_vipcard.go:332	礼品卡金额异常	{"traceId": "b559590afcf9bbd34430b64eddcc72ab", "task": "StatusMemberBalanceConsume", "balanceLogId": 2907, "amount": "0.01", "amountGift": "0"}
[insbuy]2025/08/01 - 10:12:31.132	[33mwarn[0m	insreport/state_vipcard.go:332	礼品卡金额异常	{"traceId": "b559590afcf9bbd34430b64eddcc72ab", "task": "StatusMemberBalanceConsume", "balanceLogId": 2909, "amount": "0.01", "amountGift": "0"}
[insbuy]2025/08/01 - 16:06:18.234	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "f5e8c33d743a1d07917e4c3a997765fd", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/01 - 16:27:01.233	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "df1354371dad45b8b2d83c3984b7c3d9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/01 - 16:29:37.880	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "5babb57a2bd35d11d1dbe4e9fa2d0ba1", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/01 - 17:00:00.703	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "5a4784aaeb58abd4596e7c47414d3e5a", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/01 - 17:23:41.965	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "93621e78f67a87a10b1169c0f472ccc3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
