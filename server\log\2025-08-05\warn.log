[insbuy]2025/08/05 - 10:43:56.452	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "2c132fe59c70710bda5cef0df38d6cbb", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 10:45:26.975	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "dfc3178a7a3432b360068355700ec203", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:07:24.813	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "526af0a532dc68dd8a401327bc3e5e6e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:27:24.558	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "6454c265640dd47b8fcf853fff8f9fa9", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:32:01.165	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "30d4c2579884317957abb1c543e903a3", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:33:20.009	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "ec4cec399fadce8f747e50b8be0a789e", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:35:14.923	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "47b90d52eb68c0776f1e665f19295334", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:38:55.490	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "7fa64a0731b77a6eb66d9b03470c31d7", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 11:46:38.903	[33mwarn[0m	insbuy/contract_transformer.go:1605	费用明细项类型不匹配	{"index": 0, "type": "[]interface {}", "value": [{"ext":null,"id":"widget16710060735460001","name":"费用类型","option":{"key":"ldx0r1uc-otiiug1095g-1","text":"办公费用"},"type":"radioV2","value":"办公费用"},{"ext":null,"id":"widget17455770530670001","name":"增值税发票类型","option":{"key":"m9wniaa7-cnls8f6jdzd-0","text":"普通发票"},"type":"radioV2","value":"普通发票"},{"ext":{"capitalValue":"壹万伍仟元整","currency":"CNY","currencyRange":["CNY"],"maxValue":"","minValue":""},"id":"widget16710062249940001","name":"金额","type":"amount","value":15000}]}
[insbuy]2025/08/05 - 11:46:38.965	[33mwarn[0m	insbuy/contract_transformer.go:1605	费用明细项类型不匹配	{"index": 1, "type": "[]interface {}", "value": [{"ext":null,"id":"widget16710060735460001","name":"费用类型","option":{"key":"ldx0r1uc-otiiug1095g-1","text":"办公费用"},"type":"radioV2","value":"办公费用"},{"ext":null,"id":"widget17455770530670001","name":"增值税发票类型","option":{"key":"m9wniaa7-cnls8f6jdzd-0","text":"普通发票"},"type":"radioV2","value":"普通发票"},{"ext":{"capitalValue":"陆仟伍佰元整","currency":"CNY","currencyRange":["CNY"],"maxValue":"","minValue":""},"id":"widget16710062249940001","name":"金额","type":"amount","value":6500}]}
[insbuy]2025/08/05 - 11:46:38.965	[33mwarn[0m	insbuy/contract_transformer.go:1605	费用明细项类型不匹配	{"index": 2, "type": "[]interface {}", "value": [{"ext":null,"id":"widget16710060735460001","name":"费用类型","option":{"key":"ldx0r1uc-otiiug1095g-1","text":"办公费用"},"type":"radioV2","value":"办公费用"},{"ext":null,"id":"widget17455770530670001","name":"增值税发票类型","option":{"key":"m9wniaa7-cnls8f6jdzd-0","text":"普通发票"},"type":"radioV2","value":"普通发票"},{"ext":{"capitalValue":"壹仟伍佰元整","currency":"CNY","currencyRange":["CNY"],"maxValue":"","minValue":""},"id":"widget16710062249940001","name":"金额","type":"amount","value":1500}]}
[insbuy]2025/08/05 - 11:46:38.966	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "393c8a87d0a9429148f2e24f1c3a3146", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 13:53:14.480	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "6b27157c3cb1d51e710ac2098562065b", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 13:54:21.429	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "f4fed404d374d946d163b0af97ae2767", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 13:53:37.976	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "0b6652a1e80657d33c9c65131ce092e0", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
[insbuy]2025/08/05 - 14:32:37.482	[33mwarn[0m	test/contract_transformer_test.go:467	转换存在警告或错误	{"traceId": "3e41df345fe89174cd4d3fbc7b505252", "task": "TestUniversalDataExport", "instance_code": "72ECAA63-24BF-4E52-BE1C-74A64DA51583", "warnings": [], "errors": [{"field":"initiate_time","message":"发起时间不能为空","value":"0001-01-01 00:00:00 +0000 UTC"}]}
