package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
	"time"
)

// InsContract 合同主表
type InsContract struct {
	global.GVA_MODEL

	// 飞书相关字段
	ApprovalCode string `gorm:"type:varchar(100);not null;index;comment:审批定义Code" json:"approval_code"`
	ApprovalName string `gorm:"type:varchar(200);not null;comment:审批定义名称" json:"approval_name"`
	InstanceCode string `gorm:"type:varchar(100);not null;unique;comment:审批实例Code" json:"instance_code"`
	SerialNumber string `gorm:"type:varchar(100);comment:审批序列号" json:"serial_number"`
	Uuid         string `gorm:"type:varchar(100);not null;unique;comment:审批实例UUID" json:"uuid"`

	// 状态相关
	Status   string `gorm:"type:varchar(50);not null;index;comment:审批状态" json:"status"`
	Reverted bool   `gorm:"type:tinyint(1);default:0;comment:是否被撤销" json:"reverted"`

	// 时间相关
	StartTime *time.Time `gorm:"type:datetime;comment:审批开始时间" json:"start_time"`
	EndTime   *time.Time `gorm:"type:datetime;comment:审批结束时间" json:"end_time"`

	// 人员相关
	UserId       string `gorm:"type:varchar(100);comment:发起人UserId" json:"user_id"`
	OpenId       string `gorm:"type:varchar(100);comment:发起人OpenId" json:"open_id"`
	DepartmentId string `gorm:"type:varchar(100);comment:发起部门ID" json:"department_id"`

	// 表单数据
	Form datatypes.JSON `gorm:"type:json;comment:审批表单数据" json:"form"`

	// 业务字段
	ContractType   string  `gorm:"type:varchar(50);comment:合同类型" json:"contract_type"`
	ContractTitle  string  `gorm:"type:varchar(500);comment:合同标题" json:"contract_title"`
	ContractAmount float64 `gorm:"type:decimal(16,2);comment:合同金额" json:"contract_amount"`
	Currency       string  `gorm:"type:varchar(10);default:'CNY';comment:币种" json:"currency"`

	// 关联关系
	Comments []InsContractComment  `gorm:"foreignKey:ContractId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"comments"`
	Tasks    []InsContractTask     `gorm:"foreignKey:ContractId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"tasks"`
	Timeline []InsContractTimeline `gorm:"foreignKey:ContractId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"timeline"`
	Files    []InsContractFile     `gorm:"foreignKey:ContractId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"files"`
}

func (InsContract) TableName() string {
	return "ins_contract"
}

func (InsContract) TableComment() string {
	return "飞书合同审批数据表"
}

// InsContractComment 合同评论表
type InsContractComment struct {
	global.GVA_MODEL

	ContractId uint   `gorm:"type:int unsigned;not null;index;comment:合同ID" json:"contract_id"`
	CommentId  string `gorm:"type:varchar(100);not null;comment:飞书评论ID" json:"comment_id"`
	Comment    string `gorm:"type:text;comment:评论内容" json:"comment"`
	CreateTime string `gorm:"type:varchar(50);comment:评论时间" json:"create_time"`
	OpenId     string `gorm:"type:varchar(100);comment:评论人OpenId" json:"open_id"`
	UserId     string `gorm:"type:varchar(100);comment:评论人UserId" json:"user_id"`
}

func (InsContractComment) TableName() string {
	return "ins_contract_comment"
}

func (InsContractComment) TableComment() string {
	return "合同评论表"
}

// InsContractFile 合同附件表
type InsContractFile struct {
	global.GVA_MODEL

	ContractId uint   `gorm:"type:int unsigned;not null;index;comment:合同ID" json:"contract_id"`
	CommentId  uint   `gorm:"type:int unsigned;index;comment:评论ID" json:"comment_id"`
	FileSize   int    `gorm:"type:int;comment:文件大小" json:"file_size"`
	Title      string `gorm:"type:varchar(500);comment:文件标题" json:"title"`
	Type       string `gorm:"type:varchar(50);comment:文件类型" json:"type"`
	Url        string `gorm:"type:varchar(1000);comment:文件URL" json:"url"`
}

func (InsContractFile) TableName() string {
	return "ins_contract_file"
}

func (InsContractFile) TableComment() string {
	return "合同附件表"
}

// InsContractTask 合同审批任务表
type InsContractTask struct {
	global.GVA_MODEL

	ContractId uint   `gorm:"type:int unsigned;not null;index;comment:合同ID" json:"contract_id"`
	TaskId     string `gorm:"type:varchar(100);not null;comment:飞书任务ID" json:"task_id"`
	NodeId     string `gorm:"type:varchar(100);comment:节点ID" json:"node_id"`
	NodeName   string `gorm:"type:varchar(200);comment:节点名称" json:"node_name"`
	Type       string `gorm:"type:varchar(50);comment:任务类型" json:"type"`
	Status     string `gorm:"type:varchar(50);comment:任务状态" json:"status"`
	StartTime  string `gorm:"type:varchar(50);comment:任务开始时间" json:"start_time"`
	EndTime    string `gorm:"type:varchar(50);comment:任务结束时间" json:"end_time"`
	OpenId     string `gorm:"type:varchar(100);comment:处理人OpenId" json:"open_id"`
	UserId     string `gorm:"type:varchar(100);comment:处理人UserId" json:"user_id"`
}

func (InsContractTask) TableName() string {
	return "ins_contract_task"
}

func (InsContractTask) TableComment() string {
	return "合同审批任务表"
}

// InsContractTimeline 合同审批时间线表
type InsContractTimeline struct {
	global.GVA_MODEL

	ContractId uint   `gorm:"type:int unsigned;not null;index;comment:合同ID" json:"contract_id"`
	CreateTime string `gorm:"type:varchar(50);comment:创建时间" json:"create_time"`
	Ext        string `gorm:"type:text;comment:扩展信息" json:"ext"`
	NodeKey    string `gorm:"type:varchar(100);comment:节点Key" json:"node_key"`
	OpenId     string `gorm:"type:varchar(100);comment:操作人OpenId" json:"open_id"`
	Type       string `gorm:"type:varchar(50);comment:操作类型" json:"type"`
	UserId     string `gorm:"type:varchar(100);comment:操作人UserId" json:"user_id"`
	TaskId     string `gorm:"type:varchar(100);comment:任务ID" json:"task_id"`

	// JSON字段存储列表数据
	CcUserList datatypes.JSON `gorm:"type:json;comment:抄送人列表" json:"cc_user_list"`
	OpenIdList datatypes.JSON `gorm:"type:json;comment:OpenId列表" json:"open_id_list"`
	UserIdList datatypes.JSON `gorm:"type:json;comment:UserId列表" json:"user_id_list"`
}

func (InsContractTimeline) TableName() string {
	return "ins_contract_timeline"
}

func (InsContractTimeline) TableComment() string {
	return "合同审批时间线表"
}

// InsContractSyncLog 合同同步日志表
type InsContractSyncLog struct {
	global.GVA_MODEL

	ApprovalCode string    `gorm:"type:varchar(100);not null;comment:审批定义Code" json:"approval_code"`
	SyncType     string    `gorm:"type:varchar(20);not null;comment:同步类型:list/detail" json:"sync_type"`
	Status       string    `gorm:"type:varchar(20);not null;comment:同步状态:success/failed" json:"status"`
	StartTime    time.Time `gorm:"type:datetime;comment:同步开始时间" json:"start_time"`
	EndTime      time.Time `gorm:"type:datetime;comment:同步结束时间" json:"end_time"`
	RecordCount  int       `gorm:"type:int;comment:同步记录数" json:"record_count"`
	ErrorMsg     string    `gorm:"type:text;comment:错误信息" json:"error_msg"`
	PageToken    string    `gorm:"type:varchar(200);comment:分页标记" json:"page_token"`
}

func (InsContractSyncLog) TableName() string {
	return "ins_contract_sync_log"
}

func (InsContractSyncLog) TableComment() string {
	return "合同同步日志表"
}

// 状态定义
const (
	StatusPending  = "PENDING"  //待审批
	StatusApproved = "APPROVED" // 通过
	StatusRejected = "REJECTED" //拒绝
	StatusCanceled = "CANCELED" //撤销
	StatusDeleted  = "DELETED"  //删除
)
