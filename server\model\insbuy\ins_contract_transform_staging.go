package insbuy

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// InsContractTransformStaging 合同转换中间表
type InsContractTransformStaging struct {
	global.GVA_MODEL

	// 源数据关联
	SourceContractId   uint   `gorm:"not null;index;comment:源合同ID" json:"source_contract_id"`
	SourceInstanceCode string `gorm:"type:varchar(100);not null;index;comment:源实例代码" json:"source_instance_code"`
	SourceApprovalCode string `gorm:"type:varchar(100);not null;index;comment:源审批代码" json:"source_approval_code"`
	DetailIndex        int    `gorm:"not null;default:0;comment:明细索引" json:"detail_index"`
	DetailTotal        int    `gorm:"not null;default:1;comment:明细总数" json:"detail_total"`

	// 转换状态
	TransformStatus string     `gorm:"type:varchar(20);not null;default:'pending';index;comment:转换状态" json:"transform_status"`
	TransformTime   *time.Time `gorm:"type:datetime;comment:转换时间" json:"transform_time"`
	TransformError  string     `gorm:"type:text;comment:转换错误信息" json:"transform_error"`

	// 审核状态
	AuditStatus string     `gorm:"type:varchar(20);not null;default:'pending';index;comment:审核状态" json:"audit_status"`
	AuditorId   *uint      `gorm:"comment:审核人ID" json:"auditor_id"`
	AuditTime   *time.Time `gorm:"type:datetime;comment:审核时间" json:"audit_time"`
	AuditNote   string     `gorm:"type:text;comment:审核备注" json:"audit_note"`

	// 存储状态
	StorageStatus string     `gorm:"type:varchar(20);not null;default:'pending';index;comment:存储状态" json:"storage_status"`
	StorageTime   *time.Time `gorm:"type:datetime;comment:存储时间" json:"storage_time"`
	StorageError  string     `gorm:"type:text;comment:存储错误信息" json:"storage_error"`

	// 转换后的标准化字段（与RegionalExpenseDetailItem保持一致）
	SerialNumber         int    `gorm:"comment:序号" json:"serial_number"`
	CompletionTime       string `gorm:"type:varchar(50);comment:完成时间" json:"completion_time"`
	ReportMonth          string `gorm:"type:varchar(20);index;comment:管报月份" json:"report_month"`
	Title                string `gorm:"type:varchar(500);comment:标题" json:"title"`
	PaymentEntity        string `gorm:"type:varchar(200);comment:付款主体" json:"payment_entity"`
	PaymentReason        string `gorm:"type:varchar(500);comment:付款事由" json:"payment_reason"`
	BusinessType         string `gorm:"type:varchar(100);comment:业务类型" json:"business_type"`
	ContractAmount       string `gorm:"type:varchar(50);comment:合同签约金额" json:"contract_amount"`
	ContractPaidAmount   string `gorm:"type:varchar(50);comment:合同已付金额" json:"contract_paid_amount"`
	CurrentRequestAmount string `gorm:"type:varchar(50);comment:本次请款金额" json:"current_request_amount"`
	PendingAmount        string `gorm:"type:varchar(50);comment:待付款金额" json:"pending_amount"`
	ReportConfirmAmount  string `gorm:"type:varchar(50);comment:管报确认金额" json:"report_confirm_amount"`
	TaxRate              string `gorm:"type:varchar(20);comment:税率" json:"tax_rate"`
	AmountExcludeTax     string `gorm:"type:varchar(50);comment:不含税金额" json:"amount_exclude_tax"`
	ExpenseCategory      string `gorm:"type:varchar(100);comment:费用类别" json:"expense_category"`
	IncludeInReport      string `gorm:"type:varchar(10);comment:是否纳入管报" json:"include_in_report"`
	AccountName          string `gorm:"type:varchar(200);comment:户名" json:"account_name"`
	ReportEntity         string `gorm:"type:varchar(200);comment:管报主体" json:"report_entity"`
	ReportEntityDetail   string `gorm:"type:varchar(200);comment:管报主体明细" json:"report_entity_detail"`
	ReportRegion         string `gorm:"type:varchar(100);comment:管报区域" json:"report_region"`
	InitiatorName        string `gorm:"type:varchar(100);comment:发起人姓名" json:"initiator_name"`
	InitiatorDepartment  string `gorm:"type:varchar(200);comment:发起人部门" json:"initiator_department"`
	Department           string `gorm:"type:varchar(200);comment:部门" json:"department"`

	// 明细相关字段
	ExpenseType      string     `gorm:"type:varchar(100);comment:费用类型" json:"expense_type"`
	ExpenseLocation  string     `gorm:"type:varchar(200);comment:费用地点" json:"expense_location"`
	ExpenseDateRange string     `gorm:"type:varchar(100);comment:费用日期区间" json:"expense_date_range"`
	ExpenseStartDate *time.Time `gorm:"type:datetime;comment:费用开始日期" json:"expense_start_date"`
	ExpenseEndDate   *time.Time `gorm:"type:datetime;comment:费用结束日期" json:"expense_end_date"`
	InvoiceType      string     `gorm:"type:varchar(50);comment:发票类型" json:"invoice_type"`

	// 原始数据备份
	OriginalFormData datatypes.JSON `gorm:"type:json;comment:原始表单数据" json:"original_form_data"`

	// 关联关系
	SourceContract *InsContract `gorm:"foreignKey:SourceContractId;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"source_contract"`
}

func (InsContractTransformStaging) TableName() string {
	return "ins_contract_transform_staging"
}

func (InsContractTransformStaging) TableComment() string {
	return "合同转换中间表"
}

// GetStandardizedFields 获取标准化字段数据，用于转换为RegionalExpenseDetailItem
// 注意：转换方法将在service层实现，避免循环依赖
func (s *InsContractTransformStaging) GetStandardizedFields() map[string]interface{} {
	return map[string]interface{}{
		"serial_number":          s.SerialNumber,
		"completion_time":        s.CompletionTime,
		"report_month":           s.ReportMonth,
		"title":                  s.Title,
		"payment_entity":         s.PaymentEntity,
		"payment_reason":         s.PaymentReason,
		"business_type":          s.BusinessType,
		"contract_amount":        s.ContractAmount,
		"contract_paid_amount":   s.ContractPaidAmount,
		"current_request_amount": s.CurrentRequestAmount,
		"pending_amount":         s.PendingAmount,
		"report_confirm_amount":  s.ReportConfirmAmount,
		"tax_rate":               s.TaxRate,
		"amount_exclude_tax":     s.AmountExcludeTax,
		"expense_category":       s.ExpenseCategory,
		"include_in_report":      s.IncludeInReport,
		"account_name":           s.AccountName,
		"report_entity":          s.ReportEntity,
		"report_entity_detail":   s.ReportEntityDetail,
		"report_region":          s.ReportRegion,
		"initiator_name":         s.InitiatorName,
		"initiator_department":   s.InitiatorDepartment,
		"department":             s.Department,
		"store_id":               0, // 统一设为总部ID
	}
}

// InsContractTransformBatch 批量转换记录表
type InsContractTransformBatch struct {
	global.GVA_MODEL

	BatchNo              string         `gorm:"type:varchar(50);not null;unique;comment:批次号" json:"batch_no"`
	BatchName            string         `gorm:"type:varchar(200);comment:批次名称" json:"batch_name"`
	ApprovalCode         string         `gorm:"type:varchar(100);index;comment:审批代码" json:"approval_code"`
	OperationType        string         `gorm:"type:varchar(20);not null;index;comment:操作类型" json:"operation_type"`
	TotalCount           int            `gorm:"not null;default:0;comment:总记录数" json:"total_count"`
	ProcessedCount       int            `gorm:"not null;default:0;comment:已处理记录数" json:"processed_count"`
	SuccessCount         int            `gorm:"not null;default:0;comment:成功记录数" json:"success_count"`
	FailedCount          int            `gorm:"not null;default:0;comment:失败记录数" json:"failed_count"`
	DetailRecordsCreated int            `gorm:"not null;default:0;comment:创建的明细记录数" json:"detail_records_created"`
	Status               string         `gorm:"type:varchar(20);not null;default:'pending';index;comment:批次状态" json:"status"`
	StartTime            *time.Time     `gorm:"type:datetime;index;comment:开始时间" json:"start_time"`
	EndTime              *time.Time     `gorm:"type:datetime;comment:结束时间" json:"end_time"`
	OperatorId           uint           `gorm:"not null;index;comment:操作人ID" json:"operator_id"`
	OperatorName         string         `gorm:"type:varchar(100);not null;comment:操作人姓名" json:"operator_name"`
	ConfigParams         datatypes.JSON `gorm:"type:json;comment:配置参数" json:"config_params"`
	SummaryReport        datatypes.JSON `gorm:"type:json;comment:汇总报告" json:"summary_report"`
}

func (InsContractTransformBatch) TableName() string {
	return "ins_contract_transform_batch"
}

func (InsContractTransformBatch) TableComment() string {
	return "批量转换记录表"
}

// InsContractTransformLog 转换操作日志表
type InsContractTransformLog struct {
	global.GVA_MODEL

	BatchNo          string         `gorm:"type:varchar(50);index;comment:批次号" json:"batch_no"`
	StagingId        uint           `gorm:"not null;index;comment:中间表ID" json:"staging_id"`
	SourceContractId uint           `gorm:"not null;index;comment:源合同ID" json:"source_contract_id"`
	OperationType    string         `gorm:"type:varchar(50);not null;index;comment:操作类型" json:"operation_type"`
	OperationStatus  string         `gorm:"type:varchar(20);not null;index;comment:操作状态" json:"operation_status"`
	ProcessingTimeMs int            `gorm:"default:0;comment:处理耗时" json:"processing_time_ms"`
	ErrorMessage     string         `gorm:"type:text;comment:错误信息" json:"error_message"`
	WarningMessage   string         `gorm:"type:text;comment:警告信息" json:"warning_message"`
	OperatorId       *uint          `gorm:"comment:操作人ID" json:"operator_id"`
	OperatorName     string         `gorm:"type:varchar(100);comment:操作人姓名" json:"operator_name"`
	BeforeData       datatypes.JSON `gorm:"type:json;comment:操作前数据" json:"before_data"`
	AfterData        datatypes.JSON `gorm:"type:json;comment:操作后数据" json:"after_data"`
}

func (InsContractTransformLog) TableName() string {
	return "ins_contract_transform_log"
}

func (InsContractTransformLog) TableComment() string {
	return "转换操作日志表"
}
