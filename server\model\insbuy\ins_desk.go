// 桌台
package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"time"
)

// InsDesk 桌台表
type InsDesk struct {
	//global.GVA_MODEL
	ID uint `gorm:"type:int unsigned;primarykey"` // 主键ID

	DeskName              string         `json:"deskName" form:"deskName" gorm:"column:desk_name;comment:桌台名称;size:120;"`
	DeskCode              string         `json:"deskCode" form:"deskCode" gorm:"column:desk_code;comment:桌台编号;size:120;"`
	DeskAreaId            int            `json:"deskAreaId" form:"deskAreaId" gorm:"type:int(11);column:desk_area_id;comment:桌台地区ID;index:daid"`
	DeskCategoryId        int            `json:"deskCategoryId" form:"deskCategoryId" gorm:"type:int(11);column:desk_category_id;comment:桌台分类ID;index:acgid"`
	DefaultMinConsumption float64        `json:"defaultMinConsumption" form:"defaultMinConsumption" gorm:"column:default_min_consumption;type:decimal(10,2);comment:默认低消;size:10;"`
	SortOrder             int            `json:"sortOrder" form:"sortOrder" gorm:"type:int(11);column:sort_order;comment:排序;"`
	DeskImg               string         `json:"deskImg" form:"deskImg" gorm:"column:desk_img;comment:桌台图片;size:255;default:''"`
	DeskDesc              string         `json:"deskDesc" form:"deskDesc" gorm:"column:desk_desc;comment:桌台描述;size:255;default:''"`
	Enable                int            `json:"enable" form:"enable" gorm:"column:enable;comment:状态 0：停用\n1：启用;type:tinyint(4);"`
	DeskStatusId          int            `json:"deskStatusId" form:"deskStatusId" gorm:"type:int(11);column:desk_status_id;comment:状态 0：停用\n1：启用;index:dsid"`
	StoreId               int            `json:"storeId" form:"storeId" gorm:"type:int(11);column:store_id;comment:店铺ID;index:sid"`
	OpenDeskId            uint           `gorm:"column:open_desk_id;type:int(11) unsigned;comment:对应的开台id;NOT NULL;default:0" json:"open_desk_id"`
	Remark                string         `json:"remark" form:"remark" gorm:"column:remark;comment:临时备注;"`
	GiveAttr              int            `json:"giveAttr" form:"giveAttr" gorm:"type:tinyint(4);column:give_attr;comment:赠送属性,标识桌台可赠送人 0=专属 1=任意;NOT NULL;default:0;index:giveattr"`
	DeskAttr              *int           `json:"deskAttr" form:"deskAttr" gorm:"type:tinyint(4);column:desk_attr;comment:桌台属性,0 真实台 1 虚拟台;NOT NULL;default:0;index:deskattr"`
	QrCodeKey             string         `json:"qrCodeKey" form:"qrCodeKey" gorm:"type:varchar(255);uniqueIndex:qr;column:qr_code_key;comment:二维码密钥;"` //二维码密钥
	Source                int            `json:"source" form:"source" gorm:"type:tinyint(4);column:source;comment:来源  1 管理后台 2 ins新乐园;default:1;"`
	Ext                   datatypes.JSON `gorm:"column:ext;comment:拓展字段" json:"ext"`
	IsSync                *int           `json:"isSync" form:"isSync" gorm:"column:is_sync;comment:是否同步 0 否 1 是;default:1;"`

	CreatedAt time.Time      // 创建时间
	UpdatedAt time.Time      // 更新时间
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"` // 删除时间
}

// TableName InsDesk 表名
func (InsDesk) TableName() string {
	return "ins_desk"
}

// InsDeskArea 桌台地区表
type InsDeskArea struct {
	Model0WithBy
	StoreId uint     `json:"storeId" form:"storeId" gorm:"type:int;not null;comment:店铺ID;"`
	Name    string   `json:"name" form:"name" gorm:"type:varchar(45);not null;comment:区域名称;"`
	Status  TypeStat `json:"status" form:"status" gorm:"type:tinyint;not null;default:1;comment:状态 1使用中 2停用;"`
	Type    int      `json:"type" form:"type" gorm:"type:tinyint;not null;default:1;comment:类型 1普通 2虚拟;"`
	//仓库
	WarehouseId uint `json:"warehouseId" form:"warehouseId" gorm:"type:int;not null;default:0;comment:仓库ID;"`
}

// TableName InsDeskArea 表名
func (InsDeskArea) TableName() string {
	return "ins_desk_area"
}

// InsDeskCategory 桌台分类表
type InsDeskCategory struct {
	Model1
	Name                  string  `json:"name" form:"name" gorm:"column:name;comment:桌台分类名称;size:45;"`
	DefaultMinConsumption float64 `json:"defaultMinConsumption" form:"defaultMinConsumption" gorm:"column:default_min_consumption;comment:低消;size:10;"`
	Status                int     `json:"status" form:"status" gorm:"column:status;comment:状态;"`
	StoreId               int     `json:"storeId" form:"storeId" gorm:"column:store_id;comment:店铺ID;"`
}

// TableName InsDeskCategory 表名
func (InsDeskCategory) TableName() string {
	return "ins_desk_category"
}

// InsDeskCategoryMinConsumption 桌台分类低消表
type InsDeskCategoryMinConsumption struct {
	Model1
	DeskCategoryId int     `json:"deskCategoryId" form:"deskCategoryId" gorm:"column:desk_category_id;comment:桌台分类ID;"`
	PeriodId       int     `json:"periodId" form:"periodId" gorm:"column:period_id;comment:时间ID;"`
	MinConsumption float64 `json:"minConsumption" form:"minConsumption" gorm:"column:min_consumption;comment:低消;size:10;"`
}

// TableName InsDeskCategoryMinConsumption 表名
func (InsDeskCategoryMinConsumption) TableName() string {
	return "ins_desk_category_min_consumption"
}

// InsDeskMinConsumption 桌台低消表
type InsDeskMinConsumption struct {
	Model1
	DeskId         int       `json:"deskId" form:"deskId" gorm:"column:desk_id;comment:桌台ID;"`
	PeriodId       int       `json:"periodId" form:"periodId" gorm:"column:period_id;comment:时间ID;"`
	MinConsumption float64   `json:"minConsumption" form:"minConsumption" gorm:"column:min_consumption;comment:低消;size:10;"`
	UpdateAt       time.Time `json:"updateAt" form:"updateAt" gorm:"column:update_at;comment:;"`
}

// TableName InsDeskMinConsumption 表名
func (InsDeskMinConsumption) TableName() string {
	return "ins_desk_min_consumption"
}

// InsDeskPeriodMinConsumption 桌台低消时段关联表
type InsDeskPeriodMinConsumption struct {
	Model1
	Name      string `json:"name" form:"name" gorm:"column:name;comment:低消时段别名;size:45;"`
	StartWeek int    `json:"startWeek" form:"startWeek" gorm:"column:start_week;comment:开始周;"`
	EndWeek   int    `json:"endWeek" form:"endWeek" gorm:"column:end_week;comment:结束周;"`
	StartTime string `json:"startTime" form:"startTime" gorm:"column:start_time;comment:开始时间 时分;"`
	EndTime   string `json:"endTime" form:"endTime" gorm:"column:end_time;comment:结束时间 时分;"`
	SortOrder int    `json:"sortOrder" form:"sortOrder" gorm:"column:sort_order;comment:排序;"`
}

// TableName InsDeskPeriodMinConsumption 表名
func (InsDeskPeriodMinConsumption) TableName() string {
	return "ins_desk_period_min_consumption"
}

// InsDeskStatus 桌台状态表
type InsDeskStatus struct {
	Model0WithBy
	StoreId uint     `json:"storeId" form:"storeId" gorm:"type:int;not null;default 0;comment:店铺ID 0表示总店共用;"`
	Name    string   `json:"name" form:"name" gorm:"type:varchar(32);index;comment:状态名称;"`
	Status  TypeStat `json:"status" form:"status" gorm:"type:tinyint;not null; default:1;comment:状态 1使用中 2停用;"`
	IsSys   int      `json:"isSys" form:"isSys" gorm:"type:tinyint;not null; default:1;comment: 1：系统预置，不可修改 2：用户创建，可以修改;"`
	Style   int      `json:"style" form:"style" gorm:"type:tinyint;not null; default:0;comment: 样式风格 0默认 1预定 2待清洁 3待下单 4消费中 5待上品;"`
	Remark  string   `gorm:"type:varchar(512);comment:描述;"`
	Color   string   `gorm:"column:color;type:varchar(100);comment:状态颜色" json:"color"`
}

// TableName InsDeskStatus 表名
func (InsDeskStatus) TableName() string {
	return "ins_desk_status"
}

// InsDeskStatusLog 桌台状态日志表
type InsDeskStatusLog struct {
	global.GVA_MODEL
	DeskId      *int       `json:"deskId" form:"deskId" gorm:"column:desk_id;comment:桌台ID;"`
	OldStatusId *int       `json:"oldStatusId" form:"oldStatusId" gorm:"column:old_status_id;comment:旧状态ID;"`
	NewStatusId *int       `json:"newStatusId" form:"newStatusId" gorm:"column:new_status_id;comment:新状态ID;"`
	OpTime      *time.Time `json:"opTime" form:"opTime" gorm:"column:op_time;comment:操作时间;"`
	Duration    string     `json:"duration" form:"duration" gorm:"column:duration;comment:阶段持续时间;size:45;"`
}

// TableName InsDeskStatusLog 表名
func (InsDeskStatusLog) TableName() string {
	return "ins_desk_status_log"
}

// InsDeskOpen 桌台开台表
type InsDeskOpen struct {
	global.GVA_MODEL
	FormDeskId      uint           `gorm:"column:form_desk_id;type:bigint(20) unsigned;comment:来源桌ID;index:formid" json:"form_desk_id"`
	DeskId          uint           `gorm:"column:desk_id;type:bigint(20) unsigned;comment:桌台ID;index:did" json:"desk_id"`
	MemberId        uint           `gorm:"column:member_id;type:bigint(20) unsigned;comment:会员ID;index:memid" json:"member_id"`
	SalesmanId      uint           `gorm:"column:salesman_id;type:bigint(20) unsigned;comment:销售ID;index:said" json:"salesman_id"`
	PeopleNum       int            `gorm:"column:people_num;type:bigint(20);comment:人数" json:"people_num"`
	MinAmount       float64        `gorm:"column:min_amount;type:double;comment:最低消费" json:"min_amount"`
	Remark          string         `gorm:"column:remark;type:varchar(191);comment:备注" json:"remark"`
	RemarkExt       datatypes.JSON `gorm:"column:remark_ext;type:json;comment:备注扩展信息" json:"remark_ext"`
	OpTime          *time.Time     `gorm:"column:op_time;type:datetime(3);comment:开台时间;index:optime" json:"op_time"`
	BookTime        *time.Time     `gorm:"column:book_time;type:datetime(3);comment:预约时间-开台选择预约类型创建时间" json:"book_time"`
	CloseTime       *time.Time     `gorm:"column:close_time;type:datetime(3);comment:关台时间" json:"close_time"`
	StoreId         uint           `gorm:"column:store_id;type:bigint(20);comment:店铺ID 0表示总店共用;NOT NULL;index:sid" json:"store_id"`
	OpenDeskSn      string         `gorm:"column:open_desk_sn;type:varchar(255);comment:开台编号;NOT NULL" json:"open_desk_sn"`
	OrderRemark     datatypes.JSON `gorm:"column:order_remark;type:json;comment:整单备注;" json:"order_remark"`
	Status          int            `gorm:"column:status;type:tinyint(4);comment:是否开启 0=关闭 1=开启;NOT NULL" json:"status"`
	TempMemberName  string         `gorm:"column:temp_member_name;type:varchar(100);comment:临时会员名称;NOT NULL;default:''" json:"temp_member_name"`
	TurnoverNum     int            `gorm:"column:turnover_num;type:int(11);comment:翻台数;NOT NULL;default:0" json:"turnover_num"`                                  //翻台数
	BookType        int            `gorm:"column:book_type;type:tinyint(4);comment:预约类型 0=无预约 1=普通预约 2=压台预约;NOT NULL;default:0;index:btype" json:"book_type"`    //预约类型
	BookArrivalTime *time.Time     `gorm:"column:book_arrival_time;type:datetime(3);comment:预约到店时间" json:"book_arrival_time"`                                    //预约到店时间
	BookAmount      float64        `gorm:"column:book_amount;type:decimal(20,4);comment:定金;NOT NULL;default:0" json:"book_amount"`                               //定金
	CustomerSource  int            `gorm:"column:customer_source;type:tinyint(4);comment:客人来源0 其他 1=餐厅 2通票 3点评 4集团销售;NOT NULL;default:0" json:"customer_source"` //客人来源
	RealDeskId      uint           `gorm:"column:real_desk_id;type:bigint(20) unsigned;comment:实际桌台ID" json:"real_desk_id"`

	BusinessDay jgorm.BusinessDayType `gorm:"type:date;comment:营业日;index:bd" json:"business_day"`
}

// TableName InsDeskOpen 表名
func (InsDeskOpen) TableName() string {
	return "ins_desk_open"
}

// InsDeskOpenLog 桌台开台记录表
type InsDeskOpenLog struct {
	global.GVA_MODEL
	OpenDeskId  uint   `json:"openDeskId" form:"openDeskId" gorm:"column:open_desk_id;comment:开台ID;index:opid"`
	FormDeskId  uint   `json:"formDeskId" form:"formDeskId" gorm:"column:form_desk_id;comment:来源桌台ID;default:0"`
	DeskId      uint   `json:"deskId" form:"deskId" gorm:"column:desk_id;comment:桌台ID;"`
	OldStatusId *int   `json:"oldStatusId" form:"oldStatusId" gorm:"column:old_status_id;comment:旧状态ID;"`
	NewStatusId *int   `json:"newStatusId" form:"newStatusId" gorm:"column:new_status_id;comment:新状态ID;"`
	StoreId     uint   `json:"storeId" form:"storeId" gorm:"column:store_id;comment:店铺ID;"`
	OperatorId  uint   `json:"operatorId" form:"operatorId" gorm:"column:operator_id;comment:操作人;"`                     //操作人
	Event       string `json:"event" form:"event" gorm:"column:event;type:varchar(191);comment:事件;NOT NULL;default:''"` //操作事件-换桌-并桌等
	Remark      string `json:"remark" form:"remark" gorm:"column:remark;type:varchar(191);comment:备注"`
}

// TableName InsDeskOpenLog 表名
func (InsDeskOpenLog) TableName() string {
	return "ins_desk_open_log"
}

// InsStoreRevenueReport 班结表报表
type InsStoreRevenueReport struct {
	Model1
	StoreId             uint      `gorm:"column:store_id;type:int(11) unsigned;comment:店铺ID;NOT NULL;index:sid" json:"store_id"`
	OpenDate            time.Time `gorm:"column:open_date;type:date;comment:营业日期;NOT NULL" json:"open_date"`                                    //营业日期
	OpenNum             int       `gorm:"column:open_num;type:int(11);comment:开台数;NOT NULL" json:"open_num"`                                    //开台数
	OrderNum            int       `gorm:"column:order_num;type:int(11);comment:订单量;NOT NULL" json:"order_num"`                                  //订单量
	Revenue             float64   `gorm:"column:revenue;type:decimal(20,4);comment:营收;NOT NULL" json:"revenue"`                                 //营收
	GiveAmount          float64   `gorm:"column:give_amount;type:decimal(20,4);comment:赠送金额;NOT NULL" json:"give_amount"`                       //赠送金额
	PaidAmount          float64   `gorm:"column:paid_amount;type:decimal(20,4);comment:已付金额;NOT NULL" json:"paid_amount"`                       //已付金额
	UnpaidAmount        float64   `gorm:"column:unpaid_amount;type:decimal(20,4);comment:未付金额;NOT NULL" json:"unpaid_amount"`                   //未付金额
	BalancePayAmount    float64   `gorm:"column:balance_pay_amount;type:decimal(20,4);comment:会员卡金额;NOT NULL" json:"balance_pay_amount"`        //会员卡支付金额
	BalanceGiveAmount   float64   `gorm:"column:balance_give_amount;type:decimal(20,4);comment:余额支付赠送金额;NOT NULL" json:"balance_give_amount"`   //余额支付赠送金额
	DiscountFee         float64   `gorm:"column:discount_fee;type:decimal(20,4);comment:优惠金额;NOT NULL" json:"discount_fee"`                     //优惠金额
	CouponFee           float64   `gorm:"column:coupon_fee;type:decimal(20,4);comment:优惠券金额;NOT NULL" json:"coupon_fee"`                        //优惠券金额
	ServiceFee          float64   `gorm:"column:service_fee;type:decimal(20,4);comment:服务费;NOT NULL" json:"service_fee"`                        //服务费
	ErasePrice          float64   `gorm:"column:erase_price;type:decimal(20,4);comment:抹零金额;NOT NULL" json:"erase_price"`                       //抹零金额
	PlayerPrice         float64   `gorm:"column:player_price;type:decimal(20,4);comment:头号玩家优惠;NOT NULL" json:"player_price"`                   //头号玩家优惠
	TicketAmount        float64   `gorm:"column:ticket_amount;type:decimal(20,4);comment:通票金额;NOT NULL" json:"ticket_amount"`                   //通票金额
	OfflineTicketAmount float64   `gorm:"column:offline_ticket_amount;type:decimal(20,4);comment:线下通票金额;NOT NULL" json:"offline_ticket_amount"` //线下通票金额
	StoreStatus         int       `gorm:"column:store_status;type:tinyint(4);comment:店铺状态 1=营业中 2=休息中 3=打烊中;NOT NULL" json:"store_status"`
	StartTime           time.Time `gorm:"column:start_time;type:datetime(3);comment:开始时间;index:stime" json:"start_time"` //开始时间
	EndTime             time.Time `gorm:"column:end_time;type:datetime(3);comment:结束时间" json:"end_time"`                 //结束时间
	//操作人
	OperatorId uint `gorm:"column:operator_id;type:int(11) unsigned;comment:操作人;NOT NULL" json:"operator_id"`
}

// TableName InsStoreRevenueReport 表名
func (InsStoreRevenueReport) TableName() string {
	return "ins_store_revenue_report"
}

// InsDeskPrintLog 桌台打印单据日志
type InsDeskPrintLog struct {
	Model0
	StoreId      uint                  `gorm:"column:store_id;type:int(11) unsigned;comment:店铺ID;NOT NULL" json:"store_id"`
	BusinessDay  jgorm.BusinessDayType `gorm:"type:date;comment:营业日;index:bd" json:"business_day"`
	PrinterName  string                `gorm:"column:printer_name;type:varchar(255);comment:打印机器名称" json:"printer_name"`
	PrinterSn    string                `gorm:"column:printer_sn;type:varchar(255);comment:打印机器编号" json:"printer_sn"`
	TemplateName string                `gorm:"column:template_name;type:varchar(100);comment:打印模板名称" json:"template_name"`
	TemplateCode string                `gorm:"column:template_code;type:varchar(100);comment:打印模板编号" json:"template_code"`
	TemplateType int                   `gorm:"column:template_type;type:tinyint(4);comment:打印模板类型" json:"template_type"`
	TemplateId   uint                  `gorm:"column:template_id;type:int(11);comment:打印模板ID;default:0;" json:"template_id"`
	ErrMsg       string                `gorm:"column:err_msg;type:varchar(600);comment:错误信息" json:"err_msg"`
	RetryTimes   int                   `gorm:"column:retry_times;type:int(11);comment:重试次数;default:0" json:"retry_times"` //重试次数
	PrintInfo    string                `gorm:"column:print_info;type:text;comment:打印信息" json:"print_info"`
	PrintNo      string                `gorm:"column:print_no;type:varchar(100);comment:打印序号" json:"print_no"` //打印序号
}

// TableName InsDeskPrintLog 表名
func (InsDeskPrintLog) TableName() string {
	return "ins_desk_print_log"
}

const (
	SourceAdmin = 1
	SourceIns   = 2
)
