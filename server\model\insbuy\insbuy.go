package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/organization/model"
	"gorm.io/gorm"
)

// GetInitTables 返回所有需要初始化的数据表结构体，如
//   - 订单相关
//   - 会员相关
//   - 库存相关
//   - 优惠券相关
//   - 支付相关
//   - 退款相关
//   - 商品相关
//   - 店铺配置相关
func GetInitTables() []interface{} {
	// TODO
	return []interface{}{
		&InsSupplier{},
		&InsWarehouse{},
		&InsStore{},
		&InsStoreTerminal{},
		&InsBook{},
		&InsDeskArea{},
		&InsDeskStatus{},
		&InsVipMember{},
		&system.SysUser{},
		&InsDeskOpen{},
		&InsDeskOpenLog{},
		&InsDesk{},
		//订单
		&InsOrderInfo{},
		&InsOrderInfoDetails{},
		&InsOrderInfoPackage{},
		&InsOrderDiscountDetail{},
		&InsOrderPayDetail{},
		&InsOrderShoppingCartDetail{},

		//反结账
		&InsTradeReverseLog{},
		//
		&InsSysCounter{},
		&InsSysPrintTemplate{},
		&InsSysStorePrintTemplate{},
		&InsSysDevPrinter{},

		//交易
		&InsPayment{},
		&InsBusinessPayment{},
		&InsCustomPayment{},
		&InsTradePay{},
		&InsTradeRefunds{},
		&InsTrade{},
		&InsTradeAccount{},
		&InsTradeAccountDetails{},
		&InsTradeCoupon{},
		&InsPayConfig{},
		&InsTradeCustom{},
		&InsTradeAccountConfig{},

		//商品
		&InsProduct{},
		&InsProductStore{},
		&InsProductPackage{},
		&InsProductPackageDetails{},
		&InsProductPackageItem{},
		&InsProductPackageItemDetails{},
		&InsProductPackageItemTemplate{},
		&InsProductPackageItemTemplateDetails{},
		&InsProductStall{},

		&InsProductSoldOut{},

		//原料
		&InsMaterial{},
		&InsMaterialHistory{},
		//采购
		&InsWarehouseInoutApply{},
		&InsWarehouseInout{},

		//充值
		&InsRechargeCard{},

		//备品
		&InsShipment{},
		&InsInventoryProductFlow{},
		&InsInventoryMaterialFlow{},

		//订单
		&InsOrderBill{},

		//退单
		&InsOrderReturnDetails{},
		&InsOrderReturn{},

		//存取酒
		&InsDepositLog{},
		&InsDepositRecord{},
		&InsDepositSmsRecord{},

		//会员卡
		&InsVipCard{},
		&InsBalanceLog{},
		&InsActivityContent{},

		//赠送记录
		&InsGiftLog{},
		&InsGiftLogDetail{},

		//赠送额度
		&InsGiftQuotaAssignUser{},
		&InsGiftQuotaAssignLogDetail{},
		&InsGiftQuotaApplyLog{},
		&InsGiftQuotaMonth{},

		//销售验证码
		&InsSalerCode{},
		&InsSalerStore{},

		//报表
		&InsStoreRevenueReport{},

		//赠送
		&InsGiftRuleDetail{},

		//备注管理
		&InsNotes{},
		&InsNotesDetails{},

		// 配置管理
		&InsConfHoliday{},

		// 飞书合同管理
		&InsContract{},
		&InsContractComment{},
		&InsContractFile{},
		&InsContractTask{},
		&InsContractTimeline{},
		&InsContractSyncLog{},

		// 飞书合同转换系统
		&InsContractTransformStaging{},
		&InsContractTransformBatch{},
		&InsContractTransformLog{},
	}
}

// GetInitTables2 返回所有需要初始化的数据表结构体，启用外键约束
func GetInitTables2() []interface{} {
	return []interface{}{
		&InsSysDevPrinterConfig{},
		&InsStore{},

		&InsProductDetails{},

		// 活动类
		&InsActBookDeskConf{},
		&InsActBookDeskConfBackup{},
		&InsActBookDeskConfPackage{},
		&InsActBookDeskRecord{},
		&InsActBookDeskTable{},

		//打印日志
		&InsDeskPrintLog{},

		//排队
		&InsQueueChannel{},
		&InsQueueItem{},
		&InsQueueChannelResource{},
		&InsQueueItemEventHistory{},

		//事件记录-溯源
		&InsEventsLog{},

		//活动促销
		&InsProductPromotion{},
		&InsProductPromotionApplicable{},
		&InsProductPromotionTime{},
		&InsProductPromotionExclusion{},
		&InsProductPromotionCategory{},
		&InsProductPromotionProduct{},

		// 报表相关
		&InsReportSalesShare{},
		&InsReportDataSalesValue{},
		&InsReportRuleSalesShare{},
		&InsReportRuleSalesShareTimeFrame{},
		&InsReportRuleSalesShareStaff{},
		&InsReportRuleSalesShareOrg{},
		&InsReportRuleSalesShareProduct{},
		&InsReportRuleSalesShareProductCategory{},
		&InsReportRuleSalesShareRecharge{},
		&InsStoreConsumeSnapshot{},
		&InsScanFlow{},
		&InsOpenDeskSnapshot{},

		// 优惠券相关
		&InsCoupon{},
		&InsCouponValidWeekDay{},
		&InsCouponDiscount{},
		&InsCouponCash{},
		&InsCouponProduct{},
		&InsCouponApplyScope{},
		&InsCouponSendRule{},
		&InsCouponSendDetail{},

		// 服务费
		&InsServiceFee{},
		&InsServiceFeeApplicable{},
		&InsServiceFeeTime{},
		&InsServiceFeeExclusion{},
		&InsServiceFeeCategory{},
		&InsServiceFeeProduct{},
		&InsServiceFeeDesk{},
		&InsServiceFeeDeskCurrent{},

		//
		&InsCostCardDetail{},

		// 会员卡余额
		&InsBalanceBulk{},
		&InsBalanceBulkDetails{},

		// 外部数据
		&InsExtConf{},
		&InsExtKezeeOrder{},
		&InsExtKezeeOrderPay{},
		&InsExtKezeeOrderItem{},
		&InsExtKezeeOpenDesk{},
		&InsExtKezeeAddition{},
		&InsExtStoreSales{},
		&InsExtStoreTicket{},

		//业务开关与设置
		&BusinessCate{},
		&InsBusinessConfig{},
		&InsStoreBusinessConfig{},

		// 赠送规则商品
		&InsGiftRuleProduct{},
		//
		InsDepositInoutReportDay{},

		// 仓储盘点
		&InsWarehouseCheckDay{},
		&InsWarehouseCheckDayDetail{},
		&InsWarehouseInventoryBatch{},
		&InsWarehouseInventoryBatchHistory{},
		&InsWarehouseSaleLog{},
		&InsStoreWarehouseCostPrice{},
		&InsCostPriceTotal{},
		&InsCostPriceHistory{},

		//海昌导入数据
		&InsWarehouseHaiChangImportLog{},
		&InsWarehouseHaiChang{},
		&InsWarehouseHaiChangDetails{},

		&InsUserRegister{},

		// 系统
		&system.SysAuthority2{},
		&system.SysAuthority2Item{},
		&system.SysAuthority2User{},

		//空瓶回收
		&InsWarehouseEmpties{},
		&InsWarehouseEmptiesDetail{},

		//sql报表
		&InsSqlReport{},
		&InsSqlReportCate{},
		&InsSqlRule{},
		&InsSqlRuleHasUser{},
		&InsSqlRuleHasReport{},
		&InsReportCategory{},
		&InsReportStatisticalRule{},
		&InsReportStatisticalRuleDetail{},
		//微信配置
		&InsWxConfig{},

		&InsTicketOfflineRecord{}, //线下门票文件存储路径
		&InsStoreTicketConfig{},   //门店线下配置信息
		&InsConfigCenter{},
		&InsConfigCenterDetail{},

		&InsStoreManualOp{},

		//报表相关
		&InsReport{},
		&InsReportField{},
		&InsReportIntermediateResult{},
		&InsReportIntermediateResultDetails{},
		//挂账
		&InsTradeAccountConfigStore{},
		&InsLangTranslation{},
		//对账
		&InsReconciliationRule{},
		&InsReconciliationTask{},
		&InsReconciliationResult{},
		&InsReconciliationDataSource{},
		&InsReconciliationAdjustment{},
		&InsProductPackageItemTemplateBind{},
		&InsLangMap{},
		//
		&InsSundriesMaterial{},
		&InsSundriesMaterialSupplier{},
		&InsMaterialStandard{},
		&InsMaterialStandardRel{},
		&InsSundriesMaterialHistory{},
		&InsTransferProcess{},
		&InsTransferProcessStep{},
		&InsTransferExecution{},
		&InsTransferExecutionStep{},
		&InsWarehouseAdmin{},
		&InsEventTrackingLogs{},
		&InsGiftQuotaMonthNow{},
		&InsOrderAdjustment{},
		&InsOrderAdjustmentDetail{},
	}
}

// OptionDbView
//
//	Query *DB 子查询，必须
//	Replace bool 如果是 true, 执行 `CREATE`, 否则执行 `CREATE OR REPLACE`
//	CheckOption string 可选，如 `WITH [ CASCADED | LOCAL ] CHECK OPTION`
type OptionDbView func(db *gorm.DB, scope global.ScopeVar) (Query *gorm.DB, Replace bool, CheckOption string, Error error)

// GetInitViews 返回所有需要初始化的视图结构体，nil 表示需要删除
func GetInitViews() map[string]OptionDbView {
	return map[string]OptionDbView{
		"not_exist": nil,
	}
}

// GetOrmTables 需要 orm 对象的表，如果已经在 GetInitTables 中定义了，不需要在这里定义
func GetOrmTables() []interface{} {
	return []interface{}{
		//&InsSupplier{},
		//&InsSupplierBrand{},
		//&InsWarehouse{},
		//&InsWarehouseStock{},
		//&InsWarehouseStockLog{},
		&InsDeskOpenLog{},
		&InsStoreTerminal{},

		&InsVipLevel{},
		&InsVipScoreLog{},
		&InsBook{},
		&InsBrand{},
		&InsDesk{},
		&InsDeskArea{},
		&InsDeskCategory{},
		&InsDeskCategoryMinConsumption{},
		&InsDeskMinConsumption{},
		&InsDeskPeriodMinConsumption{},
		&InsDeskStatusLog{},
		&InsMicroOrder{},
		&system.SysUser{},
		&system.SysUserAuthority{},
		&system.SysAuthority{},
		&InsProductCategory{},
		&InsDeposit{},
		&InsDepositLog{},
		&InsDepositVipReport{},
		&InsDepositRecord{},
		&InsDepositSmsRecord{},
		&InsProductSupplier{},
		&InsWarehouseInoutDetails{},
		&InsWarehouseInoutLog{},
		&InsWarehouseInoutReceipt{},
		&InsWarehouseInout{},
		&InsWarehouseInventory{},
		&InsWarehouseInventoryDetail{},
		&InsWarehouseInventoryUnique{},
		&InsWarehouseInoutType{},
		&InsWarehouseInoutDetailUnique{},
		&InsOrderPayDetail{},
		&InsOrderShoppingCart{},
		&InsOrderShoppingCartDetail{},
		&InsOrderBill{},
		&InsProductActivity{},
		&InsProductActivityDetail{},
		&InsProductActivityFormula{},
		&InsProductPackageItemTemplate{},
		&InsProductPackageItemTemplateDetails{},

		//订单
		&InsOrderInfoDetails{},
		&InsOrderInfoPackage{},
		&InsOrderDiscountDetail{},

		&InsPayment{},
		&InsPayConfig{},

		//会员卡充值规则
		&InsGiftRules{},
		&InsGiftRuleDetail{},
		&InsGiftRuleProduct{},
		&InsGiftRuleMember{},
		&InsRechargeRule{},
		//备品
		&InsShipment{},
		&InsInventoryProductFlow{},
		&InsInventoryMaterialFlow{},

		&InsMaterial{},
		&InsMaterialHistory{},
		&InsMaterialCategory{},
		&InsMaterialSupplier{},
		&InsBalanceLog{},
		&InsActivityContent{},
		&InsBusinessPayment{},
		&InsCustomPayment{},
		&InsTradePay{},
		&InsTradeRefunds{},
		&InsOrderReturn{},
		&InsOrderReturnDetails{},
		&InsRechargeCard{},
		&InsCostCard{},
		&InsCostCardDetail{},
		&InsTrade{},
		&InsTradeAccount{},
		&InsTradeCoupon{},
		&InsTradeCustom{},
		&InsTradeAccountDetails{},
		&InsTradeAccountConfig{},
		&InsWarehouseInoutApply{},
		&model.Organization{},
		&model.OrgUser{},
		&InsExcelTemplate{},
		&InsImportLog{},
		&InsVerifyCode{},
		&InsWarehouseInventoryDay{},
		&InsAudit{},
		&InsAuditConfig{},
		&InsAuditFlow{},
		&InsAuditFlowDetail{},
		&HyCheckout{},

		&InsVipCard{}, //会员卡

		//赠送记录
		&InsGiftLog{},
		&InsGiftLogDetail{},

		//赠送额度
		&InsGiftQuotaAssignUser{},
		&InsGiftQuotaAssignLogDetail{},
		&InsGiftQuotaApplyLog{},
		&InsGiftQuotaMonth{},

		//反结账
		&InsTradeReverseLog{},

		//销售验证码
		&InsSalerCode{},
		&InsSalerStore{},

		//报表
		&InsStoreRevenueReport{},
		&InsDepositReportData{},
		&InsDepositInoutReportDay{},
		&InsEmptyBottleReport{},

		//独立权限
		&InsUserAuthorityStore{},

		//备注管理
		&InsNotes{},
		&InsNotesDetails{},
	}
}
