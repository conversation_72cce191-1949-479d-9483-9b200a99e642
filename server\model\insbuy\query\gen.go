// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                                      = new(Query)
	BusinessCate                           *businessCate
	HyCheckout                             *hyCheckout
	InsActBookDeskConf                     *insActBookDeskConf
	InsActBookDeskConfBackup               *insActBookDeskConfBackup
	InsActBookDeskConfPackage              *insActBookDeskConfPackage
	InsActBookDeskRecord                   *insActBookDeskRecord
	InsActBookDeskTable                    *insActBookDeskTable
	InsActivityContent                     *insActivityContent
	InsAudit                               *insAudit
	InsAuditConfig                         *insAuditConfig
	InsAuditFlow                           *insAuditFlow
	InsAuditFlowDetail                     *insAuditFlowDetail
	InsBalanceBulk                         *insBalanceBulk
	InsBalanceBulkDetails                  *insBalanceBulkDetails
	InsBalanceLog                          *insBalanceLog
	InsBook                                *insBook
	InsBrand                               *insBrand
	InsBusinessConfig                      *insBusinessConfig
	InsBusinessPayment                     *insBusinessPayment
	InsConfHoliday                         *insConfHoliday
	InsConfigCenter                        *insConfigCenter
	InsConfigCenterDetail                  *insConfigCenterDetail
	InsContract                            *insContract
	InsContractComment                     *insContractComment
	InsContractFile                        *insContractFile
	InsContractSyncLog                     *insContractSyncLog
	InsContractTask                        *insContractTask
	InsContractTimeline                    *insContractTimeline
	InsCostCard                            *insCostCard
	InsCostCardDetail                      *insCostCardDetail
	InsCostPriceHistory                    *insCostPriceHistory
	InsCostPriceTotal                      *insCostPriceTotal
	InsCoupon                              *insCoupon
	InsCouponApplyScope                    *insCouponApplyScope
	InsCouponCash                          *insCouponCash
	InsCouponDiscount                      *insCouponDiscount
	InsCouponProduct                       *insCouponProduct
	InsCouponSendDetail                    *insCouponSendDetail
	InsCouponSendRule                      *insCouponSendRule
	InsCouponValidWeekDay                  *insCouponValidWeekDay
	InsCustomPayment                       *insCustomPayment
	InsDeposit                             *insDeposit
	InsDepositInoutReportDay               *insDepositInoutReportDay
	InsDepositLog                          *insDepositLog
	InsDepositRecord                       *insDepositRecord
	InsDepositReportData                   *insDepositReportData
	InsDepositSmsRecord                    *insDepositSmsRecord
	InsDepositVipReport                    *insDepositVipReport
	InsDesk                                *insDesk
	InsDeskArea                            *insDeskArea
	InsDeskCategory                        *insDeskCategory
	InsDeskCategoryMinConsumption          *insDeskCategoryMinConsumption
	InsDeskMinConsumption                  *insDeskMinConsumption
	InsDeskOpen                            *insDeskOpen
	InsDeskOpenLog                         *insDeskOpenLog
	InsDeskPeriodMinConsumption            *insDeskPeriodMinConsumption
	InsDeskPrintLog                        *insDeskPrintLog
	InsDeskStatus                          *insDeskStatus
	InsDeskStatusLog                       *insDeskStatusLog
	InsEmptyBottleReport                   *insEmptyBottleReport
	InsEventTrackingLogs                   *insEventTrackingLogs
	InsEventsLog                           *insEventsLog
	InsExcelTemplate                       *insExcelTemplate
	InsExtConf                             *insExtConf
	InsExtKezeeAddition                    *insExtKezeeAddition
	InsExtKezeeOpenDesk                    *insExtKezeeOpenDesk
	InsExtKezeeOrder                       *insExtKezeeOrder
	InsExtKezeeOrderItem                   *insExtKezeeOrderItem
	InsExtKezeeOrderPay                    *insExtKezeeOrderPay
	InsExtStoreSales                       *insExtStoreSales
	InsExtStoreTicket                      *insExtStoreTicket
	InsGiftLog                             *insGiftLog
	InsGiftLogDetail                       *insGiftLogDetail
	InsGiftQuotaApplyLog                   *insGiftQuotaApplyLog
	InsGiftQuotaAssignLogDetail            *insGiftQuotaAssignLogDetail
	InsGiftQuotaAssignUser                 *insGiftQuotaAssignUser
	InsGiftQuotaMonth                      *insGiftQuotaMonth
	InsGiftQuotaMonthNow                   *insGiftQuotaMonthNow
	InsGiftRuleDetail                      *insGiftRuleDetail
	InsGiftRuleMember                      *insGiftRuleMember
	InsGiftRuleProduct                     *insGiftRuleProduct
	InsGiftRules                           *insGiftRules
	InsImportLog                           *insImportLog
	InsInventoryMaterialFlow               *insInventoryMaterialFlow
	InsInventoryProductFlow                *insInventoryProductFlow
	InsLangMap                             *insLangMap
	InsLangTranslation                     *insLangTranslation
	InsMaterial                            *insMaterial
	InsMaterialCategory                    *insMaterialCategory
	InsMaterialHistory                     *insMaterialHistory
	InsMaterialStandard                    *insMaterialStandard
	InsMaterialStandardRel                 *insMaterialStandardRel
	InsMaterialSupplier                    *insMaterialSupplier
	InsMicroOrder                          *insMicroOrder
	InsNotes                               *insNotes
	InsNotesDetails                        *insNotesDetails
	InsOpenDeskSnapshot                    *insOpenDeskSnapshot
	InsOrderAdjustment                     *insOrderAdjustment
	InsOrderAdjustmentDetail               *insOrderAdjustmentDetail
	InsOrderBill                           *insOrderBill
	InsOrderDiscountDetail                 *insOrderDiscountDetail
	InsOrderInfo                           *insOrderInfo
	InsOrderInfoDetails                    *insOrderInfoDetails
	InsOrderInfoPackage                    *insOrderInfoPackage
	InsOrderPayDetail                      *insOrderPayDetail
	InsOrderReturn                         *insOrderReturn
	InsOrderReturnDetails                  *insOrderReturnDetails
	InsOrderShoppingCart                   *insOrderShoppingCart
	InsOrderShoppingCartDetail             *insOrderShoppingCartDetail
	InsPayConfig                           *insPayConfig
	InsPayment                             *insPayment
	InsProduct                             *insProduct
	InsProductActivity                     *insProductActivity
	InsProductActivityDetail               *insProductActivityDetail
	InsProductActivityFormula              *insProductActivityFormula
	InsProductCategory                     *insProductCategory
	InsProductDetails                      *insProductDetails
	InsProductPackage                      *insProductPackage
	InsProductPackageDetails               *insProductPackageDetails
	InsProductPackageItem                  *insProductPackageItem
	InsProductPackageItemDetails           *insProductPackageItemDetails
	InsProductPackageItemTemplate          *insProductPackageItemTemplate
	InsProductPackageItemTemplateBind      *insProductPackageItemTemplateBind
	InsProductPackageItemTemplateDetails   *insProductPackageItemTemplateDetails
	InsProductPromotion                    *insProductPromotion
	InsProductPromotionApplicable          *insProductPromotionApplicable
	InsProductPromotionCategory            *insProductPromotionCategory
	InsProductPromotionExclusion           *insProductPromotionExclusion
	InsProductPromotionProduct             *insProductPromotionProduct
	InsProductPromotionTime                *insProductPromotionTime
	InsProductSoldOut                      *insProductSoldOut
	InsProductStall                        *insProductStall
	InsProductStore                        *insProductStore
	InsProductSupplier                     *insProductSupplier
	InsQueueChannel                        *insQueueChannel
	InsQueueChannelResource                *insQueueChannelResource
	InsQueueItem                           *insQueueItem
	InsQueueItemEventHistory               *insQueueItemEventHistory
	InsRechargeCard                        *insRechargeCard
	InsRechargeRule                        *insRechargeRule
	InsReconciliationAdjustment            *insReconciliationAdjustment
	InsReconciliationDataSource            *insReconciliationDataSource
	InsReconciliationResult                *insReconciliationResult
	InsReconciliationRule                  *insReconciliationRule
	InsReconciliationTask                  *insReconciliationTask
	InsReport                              *insReport
	InsReportCategory                      *insReportCategory
	InsReportDataSalesValue                *insReportDataSalesValue
	InsReportField                         *insReportField
	InsReportIntermediateResult            *insReportIntermediateResult
	InsReportIntermediateResultDetails     *insReportIntermediateResultDetails
	InsReportRuleSalesShare                *insReportRuleSalesShare
	InsReportRuleSalesShareOrg             *insReportRuleSalesShareOrg
	InsReportRuleSalesShareProduct         *insReportRuleSalesShareProduct
	InsReportRuleSalesShareProductCategory *insReportRuleSalesShareProductCategory
	InsReportRuleSalesShareRecharge        *insReportRuleSalesShareRecharge
	InsReportRuleSalesShareStaff           *insReportRuleSalesShareStaff
	InsReportRuleSalesShareTimeFrame       *insReportRuleSalesShareTimeFrame
	InsReportSalesShare                    *insReportSalesShare
	InsReportStatisticalRule               *insReportStatisticalRule
	InsReportStatisticalRuleDetail         *insReportStatisticalRuleDetail
	InsSalerCode                           *insSalerCode
	InsSalerStore                          *insSalerStore
	InsScanFlow                            *insScanFlow
	InsServiceFee                          *insServiceFee
	InsServiceFeeApplicable                *insServiceFeeApplicable
	InsServiceFeeCategory                  *insServiceFeeCategory
	InsServiceFeeDesk                      *insServiceFeeDesk
	InsServiceFeeDeskCurrent               *insServiceFeeDeskCurrent
	InsServiceFeeExclusion                 *insServiceFeeExclusion
	InsServiceFeeProduct                   *insServiceFeeProduct
	InsServiceFeeTime                      *insServiceFeeTime
	InsShipment                            *insShipment
	InsSqlReport                           *insSqlReport
	InsSqlReportCate                       *insSqlReportCate
	InsSqlRule                             *insSqlRule
	InsSqlRuleHasReport                    *insSqlRuleHasReport
	InsSqlRuleHasUser                      *insSqlRuleHasUser
	InsStore                               *insStore
	InsStoreBusinessConfig                 *insStoreBusinessConfig
	InsStoreConsumeSnapshot                *insStoreConsumeSnapshot
	InsStoreManualOp                       *insStoreManualOp
	InsStoreRevenueReport                  *insStoreRevenueReport
	InsStoreTerminal                       *insStoreTerminal
	InsStoreTicketConfig                   *insStoreTicketConfig
	InsStoreWarehouseCostPrice             *insStoreWarehouseCostPrice
	InsSundriesMaterial                    *insSundriesMaterial
	InsSundriesMaterialHistory             *insSundriesMaterialHistory
	InsSundriesMaterialSupplier            *insSundriesMaterialSupplier
	InsSupplier                            *insSupplier
	InsSysCounter                          *insSysCounter
	InsSysDevPrinter                       *insSysDevPrinter
	InsSysDevPrinterConfig                 *insSysDevPrinterConfig
	InsSysPrintTemplate                    *insSysPrintTemplate
	InsSysStorePrintTemplate               *insSysStorePrintTemplate
	InsTicketOfflineRecord                 *insTicketOfflineRecord
	InsTrade                               *insTrade
	InsTradeAccount                        *insTradeAccount
	InsTradeAccountConfig                  *insTradeAccountConfig
	InsTradeAccountConfigStore             *insTradeAccountConfigStore
	InsTradeAccountDetails                 *insTradeAccountDetails
	InsTradeCoupon                         *insTradeCoupon
	InsTradeCustom                         *insTradeCustom
	InsTradePay                            *insTradePay
	InsTradeRefunds                        *insTradeRefunds
	InsTradeReverseLog                     *insTradeReverseLog
	InsTransferExecution                   *insTransferExecution
	InsTransferExecutionStep               *insTransferExecutionStep
	InsTransferProcess                     *insTransferProcess
	InsTransferProcessStep                 *insTransferProcessStep
	InsUserAuthorityStore                  *insUserAuthorityStore
	InsUserRegister                        *insUserRegister
	InsVerifyCode                          *insVerifyCode
	InsVipCard                             *insVipCard
	InsVipLevel                            *insVipLevel
	InsVipMember                           *insVipMember
	InsVipScoreLog                         *insVipScoreLog
	InsWarehouse                           *insWarehouse
	InsWarehouseAdmin                      *insWarehouseAdmin
	InsWarehouseCheckDay                   *insWarehouseCheckDay
	InsWarehouseCheckDayDetail             *insWarehouseCheckDayDetail
	InsWarehouseEmpties                    *insWarehouseEmpties
	InsWarehouseEmptiesDetail              *insWarehouseEmptiesDetail
	InsWarehouseHaiChang                   *insWarehouseHaiChang
	InsWarehouseHaiChangDetails            *insWarehouseHaiChangDetails
	InsWarehouseHaiChangImportLog          *insWarehouseHaiChangImportLog
	InsWarehouseInout                      *insWarehouseInout
	InsWarehouseInoutApply                 *insWarehouseInoutApply
	InsWarehouseInoutDetailUnique          *insWarehouseInoutDetailUnique
	InsWarehouseInoutDetails               *insWarehouseInoutDetails
	InsWarehouseInoutLog                   *insWarehouseInoutLog
	InsWarehouseInoutReceipt               *insWarehouseInoutReceipt
	InsWarehouseInoutType                  *insWarehouseInoutType
	InsWarehouseInventory                  *insWarehouseInventory
	InsWarehouseInventoryBatch             *insWarehouseInventoryBatch
	InsWarehouseInventoryBatchHistory      *insWarehouseInventoryBatchHistory
	InsWarehouseInventoryDay               *insWarehouseInventoryDay
	InsWarehouseInventoryDetail            *insWarehouseInventoryDetail
	InsWarehouseInventoryUnique            *insWarehouseInventoryUnique
	InsWarehouseSaleLog                    *insWarehouseSaleLog
	InsWxConfig                            *insWxConfig
	OrgUser                                *orgUser
	Organization                           *organization
	SysAuthority                           *sysAuthority
	SysAuthority2                          *sysAuthority2
	SysAuthority2Item                      *sysAuthority2Item
	SysAuthority2User                      *sysAuthority2User
	SysUser                                *sysUser
	SysUserAuthority                       *sysUserAuthority
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	BusinessCate = &Q.BusinessCate
	HyCheckout = &Q.HyCheckout
	InsActBookDeskConf = &Q.InsActBookDeskConf
	InsActBookDeskConfBackup = &Q.InsActBookDeskConfBackup
	InsActBookDeskConfPackage = &Q.InsActBookDeskConfPackage
	InsActBookDeskRecord = &Q.InsActBookDeskRecord
	InsActBookDeskTable = &Q.InsActBookDeskTable
	InsActivityContent = &Q.InsActivityContent
	InsAudit = &Q.InsAudit
	InsAuditConfig = &Q.InsAuditConfig
	InsAuditFlow = &Q.InsAuditFlow
	InsAuditFlowDetail = &Q.InsAuditFlowDetail
	InsBalanceBulk = &Q.InsBalanceBulk
	InsBalanceBulkDetails = &Q.InsBalanceBulkDetails
	InsBalanceLog = &Q.InsBalanceLog
	InsBook = &Q.InsBook
	InsBrand = &Q.InsBrand
	InsBusinessConfig = &Q.InsBusinessConfig
	InsBusinessPayment = &Q.InsBusinessPayment
	InsConfHoliday = &Q.InsConfHoliday
	InsConfigCenter = &Q.InsConfigCenter
	InsConfigCenterDetail = &Q.InsConfigCenterDetail
	InsContract = &Q.InsContract
	InsContractComment = &Q.InsContractComment
	InsContractFile = &Q.InsContractFile
	InsContractSyncLog = &Q.InsContractSyncLog
	InsContractTask = &Q.InsContractTask
	InsContractTimeline = &Q.InsContractTimeline
	InsCostCard = &Q.InsCostCard
	InsCostCardDetail = &Q.InsCostCardDetail
	InsCostPriceHistory = &Q.InsCostPriceHistory
	InsCostPriceTotal = &Q.InsCostPriceTotal
	InsCoupon = &Q.InsCoupon
	InsCouponApplyScope = &Q.InsCouponApplyScope
	InsCouponCash = &Q.InsCouponCash
	InsCouponDiscount = &Q.InsCouponDiscount
	InsCouponProduct = &Q.InsCouponProduct
	InsCouponSendDetail = &Q.InsCouponSendDetail
	InsCouponSendRule = &Q.InsCouponSendRule
	InsCouponValidWeekDay = &Q.InsCouponValidWeekDay
	InsCustomPayment = &Q.InsCustomPayment
	InsDeposit = &Q.InsDeposit
	InsDepositInoutReportDay = &Q.InsDepositInoutReportDay
	InsDepositLog = &Q.InsDepositLog
	InsDepositRecord = &Q.InsDepositRecord
	InsDepositReportData = &Q.InsDepositReportData
	InsDepositSmsRecord = &Q.InsDepositSmsRecord
	InsDepositVipReport = &Q.InsDepositVipReport
	InsDesk = &Q.InsDesk
	InsDeskArea = &Q.InsDeskArea
	InsDeskCategory = &Q.InsDeskCategory
	InsDeskCategoryMinConsumption = &Q.InsDeskCategoryMinConsumption
	InsDeskMinConsumption = &Q.InsDeskMinConsumption
	InsDeskOpen = &Q.InsDeskOpen
	InsDeskOpenLog = &Q.InsDeskOpenLog
	InsDeskPeriodMinConsumption = &Q.InsDeskPeriodMinConsumption
	InsDeskPrintLog = &Q.InsDeskPrintLog
	InsDeskStatus = &Q.InsDeskStatus
	InsDeskStatusLog = &Q.InsDeskStatusLog
	InsEmptyBottleReport = &Q.InsEmptyBottleReport
	InsEventTrackingLogs = &Q.InsEventTrackingLogs
	InsEventsLog = &Q.InsEventsLog
	InsExcelTemplate = &Q.InsExcelTemplate
	InsExtConf = &Q.InsExtConf
	InsExtKezeeAddition = &Q.InsExtKezeeAddition
	InsExtKezeeOpenDesk = &Q.InsExtKezeeOpenDesk
	InsExtKezeeOrder = &Q.InsExtKezeeOrder
	InsExtKezeeOrderItem = &Q.InsExtKezeeOrderItem
	InsExtKezeeOrderPay = &Q.InsExtKezeeOrderPay
	InsExtStoreSales = &Q.InsExtStoreSales
	InsExtStoreTicket = &Q.InsExtStoreTicket
	InsGiftLog = &Q.InsGiftLog
	InsGiftLogDetail = &Q.InsGiftLogDetail
	InsGiftQuotaApplyLog = &Q.InsGiftQuotaApplyLog
	InsGiftQuotaAssignLogDetail = &Q.InsGiftQuotaAssignLogDetail
	InsGiftQuotaAssignUser = &Q.InsGiftQuotaAssignUser
	InsGiftQuotaMonth = &Q.InsGiftQuotaMonth
	InsGiftQuotaMonthNow = &Q.InsGiftQuotaMonthNow
	InsGiftRuleDetail = &Q.InsGiftRuleDetail
	InsGiftRuleMember = &Q.InsGiftRuleMember
	InsGiftRuleProduct = &Q.InsGiftRuleProduct
	InsGiftRules = &Q.InsGiftRules
	InsImportLog = &Q.InsImportLog
	InsInventoryMaterialFlow = &Q.InsInventoryMaterialFlow
	InsInventoryProductFlow = &Q.InsInventoryProductFlow
	InsLangMap = &Q.InsLangMap
	InsLangTranslation = &Q.InsLangTranslation
	InsMaterial = &Q.InsMaterial
	InsMaterialCategory = &Q.InsMaterialCategory
	InsMaterialHistory = &Q.InsMaterialHistory
	InsMaterialStandard = &Q.InsMaterialStandard
	InsMaterialStandardRel = &Q.InsMaterialStandardRel
	InsMaterialSupplier = &Q.InsMaterialSupplier
	InsMicroOrder = &Q.InsMicroOrder
	InsNotes = &Q.InsNotes
	InsNotesDetails = &Q.InsNotesDetails
	InsOpenDeskSnapshot = &Q.InsOpenDeskSnapshot
	InsOrderAdjustment = &Q.InsOrderAdjustment
	InsOrderAdjustmentDetail = &Q.InsOrderAdjustmentDetail
	InsOrderBill = &Q.InsOrderBill
	InsOrderDiscountDetail = &Q.InsOrderDiscountDetail
	InsOrderInfo = &Q.InsOrderInfo
	InsOrderInfoDetails = &Q.InsOrderInfoDetails
	InsOrderInfoPackage = &Q.InsOrderInfoPackage
	InsOrderPayDetail = &Q.InsOrderPayDetail
	InsOrderReturn = &Q.InsOrderReturn
	InsOrderReturnDetails = &Q.InsOrderReturnDetails
	InsOrderShoppingCart = &Q.InsOrderShoppingCart
	InsOrderShoppingCartDetail = &Q.InsOrderShoppingCartDetail
	InsPayConfig = &Q.InsPayConfig
	InsPayment = &Q.InsPayment
	InsProduct = &Q.InsProduct
	InsProductActivity = &Q.InsProductActivity
	InsProductActivityDetail = &Q.InsProductActivityDetail
	InsProductActivityFormula = &Q.InsProductActivityFormula
	InsProductCategory = &Q.InsProductCategory
	InsProductDetails = &Q.InsProductDetails
	InsProductPackage = &Q.InsProductPackage
	InsProductPackageDetails = &Q.InsProductPackageDetails
	InsProductPackageItem = &Q.InsProductPackageItem
	InsProductPackageItemDetails = &Q.InsProductPackageItemDetails
	InsProductPackageItemTemplate = &Q.InsProductPackageItemTemplate
	InsProductPackageItemTemplateBind = &Q.InsProductPackageItemTemplateBind
	InsProductPackageItemTemplateDetails = &Q.InsProductPackageItemTemplateDetails
	InsProductPromotion = &Q.InsProductPromotion
	InsProductPromotionApplicable = &Q.InsProductPromotionApplicable
	InsProductPromotionCategory = &Q.InsProductPromotionCategory
	InsProductPromotionExclusion = &Q.InsProductPromotionExclusion
	InsProductPromotionProduct = &Q.InsProductPromotionProduct
	InsProductPromotionTime = &Q.InsProductPromotionTime
	InsProductSoldOut = &Q.InsProductSoldOut
	InsProductStall = &Q.InsProductStall
	InsProductStore = &Q.InsProductStore
	InsProductSupplier = &Q.InsProductSupplier
	InsQueueChannel = &Q.InsQueueChannel
	InsQueueChannelResource = &Q.InsQueueChannelResource
	InsQueueItem = &Q.InsQueueItem
	InsQueueItemEventHistory = &Q.InsQueueItemEventHistory
	InsRechargeCard = &Q.InsRechargeCard
	InsRechargeRule = &Q.InsRechargeRule
	InsReconciliationAdjustment = &Q.InsReconciliationAdjustment
	InsReconciliationDataSource = &Q.InsReconciliationDataSource
	InsReconciliationResult = &Q.InsReconciliationResult
	InsReconciliationRule = &Q.InsReconciliationRule
	InsReconciliationTask = &Q.InsReconciliationTask
	InsReport = &Q.InsReport
	InsReportCategory = &Q.InsReportCategory
	InsReportDataSalesValue = &Q.InsReportDataSalesValue
	InsReportField = &Q.InsReportField
	InsReportIntermediateResult = &Q.InsReportIntermediateResult
	InsReportIntermediateResultDetails = &Q.InsReportIntermediateResultDetails
	InsReportRuleSalesShare = &Q.InsReportRuleSalesShare
	InsReportRuleSalesShareOrg = &Q.InsReportRuleSalesShareOrg
	InsReportRuleSalesShareProduct = &Q.InsReportRuleSalesShareProduct
	InsReportRuleSalesShareProductCategory = &Q.InsReportRuleSalesShareProductCategory
	InsReportRuleSalesShareRecharge = &Q.InsReportRuleSalesShareRecharge
	InsReportRuleSalesShareStaff = &Q.InsReportRuleSalesShareStaff
	InsReportRuleSalesShareTimeFrame = &Q.InsReportRuleSalesShareTimeFrame
	InsReportSalesShare = &Q.InsReportSalesShare
	InsReportStatisticalRule = &Q.InsReportStatisticalRule
	InsReportStatisticalRuleDetail = &Q.InsReportStatisticalRuleDetail
	InsSalerCode = &Q.InsSalerCode
	InsSalerStore = &Q.InsSalerStore
	InsScanFlow = &Q.InsScanFlow
	InsServiceFee = &Q.InsServiceFee
	InsServiceFeeApplicable = &Q.InsServiceFeeApplicable
	InsServiceFeeCategory = &Q.InsServiceFeeCategory
	InsServiceFeeDesk = &Q.InsServiceFeeDesk
	InsServiceFeeDeskCurrent = &Q.InsServiceFeeDeskCurrent
	InsServiceFeeExclusion = &Q.InsServiceFeeExclusion
	InsServiceFeeProduct = &Q.InsServiceFeeProduct
	InsServiceFeeTime = &Q.InsServiceFeeTime
	InsShipment = &Q.InsShipment
	InsSqlReport = &Q.InsSqlReport
	InsSqlReportCate = &Q.InsSqlReportCate
	InsSqlRule = &Q.InsSqlRule
	InsSqlRuleHasReport = &Q.InsSqlRuleHasReport
	InsSqlRuleHasUser = &Q.InsSqlRuleHasUser
	InsStore = &Q.InsStore
	InsStoreBusinessConfig = &Q.InsStoreBusinessConfig
	InsStoreConsumeSnapshot = &Q.InsStoreConsumeSnapshot
	InsStoreManualOp = &Q.InsStoreManualOp
	InsStoreRevenueReport = &Q.InsStoreRevenueReport
	InsStoreTerminal = &Q.InsStoreTerminal
	InsStoreTicketConfig = &Q.InsStoreTicketConfig
	InsStoreWarehouseCostPrice = &Q.InsStoreWarehouseCostPrice
	InsSundriesMaterial = &Q.InsSundriesMaterial
	InsSundriesMaterialHistory = &Q.InsSundriesMaterialHistory
	InsSundriesMaterialSupplier = &Q.InsSundriesMaterialSupplier
	InsSupplier = &Q.InsSupplier
	InsSysCounter = &Q.InsSysCounter
	InsSysDevPrinter = &Q.InsSysDevPrinter
	InsSysDevPrinterConfig = &Q.InsSysDevPrinterConfig
	InsSysPrintTemplate = &Q.InsSysPrintTemplate
	InsSysStorePrintTemplate = &Q.InsSysStorePrintTemplate
	InsTicketOfflineRecord = &Q.InsTicketOfflineRecord
	InsTrade = &Q.InsTrade
	InsTradeAccount = &Q.InsTradeAccount
	InsTradeAccountConfig = &Q.InsTradeAccountConfig
	InsTradeAccountConfigStore = &Q.InsTradeAccountConfigStore
	InsTradeAccountDetails = &Q.InsTradeAccountDetails
	InsTradeCoupon = &Q.InsTradeCoupon
	InsTradeCustom = &Q.InsTradeCustom
	InsTradePay = &Q.InsTradePay
	InsTradeRefunds = &Q.InsTradeRefunds
	InsTradeReverseLog = &Q.InsTradeReverseLog
	InsTransferExecution = &Q.InsTransferExecution
	InsTransferExecutionStep = &Q.InsTransferExecutionStep
	InsTransferProcess = &Q.InsTransferProcess
	InsTransferProcessStep = &Q.InsTransferProcessStep
	InsUserAuthorityStore = &Q.InsUserAuthorityStore
	InsUserRegister = &Q.InsUserRegister
	InsVerifyCode = &Q.InsVerifyCode
	InsVipCard = &Q.InsVipCard
	InsVipLevel = &Q.InsVipLevel
	InsVipMember = &Q.InsVipMember
	InsVipScoreLog = &Q.InsVipScoreLog
	InsWarehouse = &Q.InsWarehouse
	InsWarehouseAdmin = &Q.InsWarehouseAdmin
	InsWarehouseCheckDay = &Q.InsWarehouseCheckDay
	InsWarehouseCheckDayDetail = &Q.InsWarehouseCheckDayDetail
	InsWarehouseEmpties = &Q.InsWarehouseEmpties
	InsWarehouseEmptiesDetail = &Q.InsWarehouseEmptiesDetail
	InsWarehouseHaiChang = &Q.InsWarehouseHaiChang
	InsWarehouseHaiChangDetails = &Q.InsWarehouseHaiChangDetails
	InsWarehouseHaiChangImportLog = &Q.InsWarehouseHaiChangImportLog
	InsWarehouseInout = &Q.InsWarehouseInout
	InsWarehouseInoutApply = &Q.InsWarehouseInoutApply
	InsWarehouseInoutDetailUnique = &Q.InsWarehouseInoutDetailUnique
	InsWarehouseInoutDetails = &Q.InsWarehouseInoutDetails
	InsWarehouseInoutLog = &Q.InsWarehouseInoutLog
	InsWarehouseInoutReceipt = &Q.InsWarehouseInoutReceipt
	InsWarehouseInoutType = &Q.InsWarehouseInoutType
	InsWarehouseInventory = &Q.InsWarehouseInventory
	InsWarehouseInventoryBatch = &Q.InsWarehouseInventoryBatch
	InsWarehouseInventoryBatchHistory = &Q.InsWarehouseInventoryBatchHistory
	InsWarehouseInventoryDay = &Q.InsWarehouseInventoryDay
	InsWarehouseInventoryDetail = &Q.InsWarehouseInventoryDetail
	InsWarehouseInventoryUnique = &Q.InsWarehouseInventoryUnique
	InsWarehouseSaleLog = &Q.InsWarehouseSaleLog
	InsWxConfig = &Q.InsWxConfig
	OrgUser = &Q.OrgUser
	Organization = &Q.Organization
	SysAuthority = &Q.SysAuthority
	SysAuthority2 = &Q.SysAuthority2
	SysAuthority2Item = &Q.SysAuthority2Item
	SysAuthority2User = &Q.SysAuthority2User
	SysUser = &Q.SysUser
	SysUserAuthority = &Q.SysUserAuthority
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                                     db,
		BusinessCate:                           newBusinessCate(db, opts...),
		HyCheckout:                             newHyCheckout(db, opts...),
		InsActBookDeskConf:                     newInsActBookDeskConf(db, opts...),
		InsActBookDeskConfBackup:               newInsActBookDeskConfBackup(db, opts...),
		InsActBookDeskConfPackage:              newInsActBookDeskConfPackage(db, opts...),
		InsActBookDeskRecord:                   newInsActBookDeskRecord(db, opts...),
		InsActBookDeskTable:                    newInsActBookDeskTable(db, opts...),
		InsActivityContent:                     newInsActivityContent(db, opts...),
		InsAudit:                               newInsAudit(db, opts...),
		InsAuditConfig:                         newInsAuditConfig(db, opts...),
		InsAuditFlow:                           newInsAuditFlow(db, opts...),
		InsAuditFlowDetail:                     newInsAuditFlowDetail(db, opts...),
		InsBalanceBulk:                         newInsBalanceBulk(db, opts...),
		InsBalanceBulkDetails:                  newInsBalanceBulkDetails(db, opts...),
		InsBalanceLog:                          newInsBalanceLog(db, opts...),
		InsBook:                                newInsBook(db, opts...),
		InsBrand:                               newInsBrand(db, opts...),
		InsBusinessConfig:                      newInsBusinessConfig(db, opts...),
		InsBusinessPayment:                     newInsBusinessPayment(db, opts...),
		InsConfHoliday:                         newInsConfHoliday(db, opts...),
		InsConfigCenter:                        newInsConfigCenter(db, opts...),
		InsConfigCenterDetail:                  newInsConfigCenterDetail(db, opts...),
		InsContract:                            newInsContract(db, opts...),
		InsContractComment:                     newInsContractComment(db, opts...),
		InsContractFile:                        newInsContractFile(db, opts...),
		InsContractSyncLog:                     newInsContractSyncLog(db, opts...),
		InsContractTask:                        newInsContractTask(db, opts...),
		InsContractTimeline:                    newInsContractTimeline(db, opts...),
		InsCostCard:                            newInsCostCard(db, opts...),
		InsCostCardDetail:                      newInsCostCardDetail(db, opts...),
		InsCostPriceHistory:                    newInsCostPriceHistory(db, opts...),
		InsCostPriceTotal:                      newInsCostPriceTotal(db, opts...),
		InsCoupon:                              newInsCoupon(db, opts...),
		InsCouponApplyScope:                    newInsCouponApplyScope(db, opts...),
		InsCouponCash:                          newInsCouponCash(db, opts...),
		InsCouponDiscount:                      newInsCouponDiscount(db, opts...),
		InsCouponProduct:                       newInsCouponProduct(db, opts...),
		InsCouponSendDetail:                    newInsCouponSendDetail(db, opts...),
		InsCouponSendRule:                      newInsCouponSendRule(db, opts...),
		InsCouponValidWeekDay:                  newInsCouponValidWeekDay(db, opts...),
		InsCustomPayment:                       newInsCustomPayment(db, opts...),
		InsDeposit:                             newInsDeposit(db, opts...),
		InsDepositInoutReportDay:               newInsDepositInoutReportDay(db, opts...),
		InsDepositLog:                          newInsDepositLog(db, opts...),
		InsDepositRecord:                       newInsDepositRecord(db, opts...),
		InsDepositReportData:                   newInsDepositReportData(db, opts...),
		InsDepositSmsRecord:                    newInsDepositSmsRecord(db, opts...),
		InsDepositVipReport:                    newInsDepositVipReport(db, opts...),
		InsDesk:                                newInsDesk(db, opts...),
		InsDeskArea:                            newInsDeskArea(db, opts...),
		InsDeskCategory:                        newInsDeskCategory(db, opts...),
		InsDeskCategoryMinConsumption:          newInsDeskCategoryMinConsumption(db, opts...),
		InsDeskMinConsumption:                  newInsDeskMinConsumption(db, opts...),
		InsDeskOpen:                            newInsDeskOpen(db, opts...),
		InsDeskOpenLog:                         newInsDeskOpenLog(db, opts...),
		InsDeskPeriodMinConsumption:            newInsDeskPeriodMinConsumption(db, opts...),
		InsDeskPrintLog:                        newInsDeskPrintLog(db, opts...),
		InsDeskStatus:                          newInsDeskStatus(db, opts...),
		InsDeskStatusLog:                       newInsDeskStatusLog(db, opts...),
		InsEmptyBottleReport:                   newInsEmptyBottleReport(db, opts...),
		InsEventTrackingLogs:                   newInsEventTrackingLogs(db, opts...),
		InsEventsLog:                           newInsEventsLog(db, opts...),
		InsExcelTemplate:                       newInsExcelTemplate(db, opts...),
		InsExtConf:                             newInsExtConf(db, opts...),
		InsExtKezeeAddition:                    newInsExtKezeeAddition(db, opts...),
		InsExtKezeeOpenDesk:                    newInsExtKezeeOpenDesk(db, opts...),
		InsExtKezeeOrder:                       newInsExtKezeeOrder(db, opts...),
		InsExtKezeeOrderItem:                   newInsExtKezeeOrderItem(db, opts...),
		InsExtKezeeOrderPay:                    newInsExtKezeeOrderPay(db, opts...),
		InsExtStoreSales:                       newInsExtStoreSales(db, opts...),
		InsExtStoreTicket:                      newInsExtStoreTicket(db, opts...),
		InsGiftLog:                             newInsGiftLog(db, opts...),
		InsGiftLogDetail:                       newInsGiftLogDetail(db, opts...),
		InsGiftQuotaApplyLog:                   newInsGiftQuotaApplyLog(db, opts...),
		InsGiftQuotaAssignLogDetail:            newInsGiftQuotaAssignLogDetail(db, opts...),
		InsGiftQuotaAssignUser:                 newInsGiftQuotaAssignUser(db, opts...),
		InsGiftQuotaMonth:                      newInsGiftQuotaMonth(db, opts...),
		InsGiftQuotaMonthNow:                   newInsGiftQuotaMonthNow(db, opts...),
		InsGiftRuleDetail:                      newInsGiftRuleDetail(db, opts...),
		InsGiftRuleMember:                      newInsGiftRuleMember(db, opts...),
		InsGiftRuleProduct:                     newInsGiftRuleProduct(db, opts...),
		InsGiftRules:                           newInsGiftRules(db, opts...),
		InsImportLog:                           newInsImportLog(db, opts...),
		InsInventoryMaterialFlow:               newInsInventoryMaterialFlow(db, opts...),
		InsInventoryProductFlow:                newInsInventoryProductFlow(db, opts...),
		InsLangMap:                             newInsLangMap(db, opts...),
		InsLangTranslation:                     newInsLangTranslation(db, opts...),
		InsMaterial:                            newInsMaterial(db, opts...),
		InsMaterialCategory:                    newInsMaterialCategory(db, opts...),
		InsMaterialHistory:                     newInsMaterialHistory(db, opts...),
		InsMaterialStandard:                    newInsMaterialStandard(db, opts...),
		InsMaterialStandardRel:                 newInsMaterialStandardRel(db, opts...),
		InsMaterialSupplier:                    newInsMaterialSupplier(db, opts...),
		InsMicroOrder:                          newInsMicroOrder(db, opts...),
		InsNotes:                               newInsNotes(db, opts...),
		InsNotesDetails:                        newInsNotesDetails(db, opts...),
		InsOpenDeskSnapshot:                    newInsOpenDeskSnapshot(db, opts...),
		InsOrderAdjustment:                     newInsOrderAdjustment(db, opts...),
		InsOrderAdjustmentDetail:               newInsOrderAdjustmentDetail(db, opts...),
		InsOrderBill:                           newInsOrderBill(db, opts...),
		InsOrderDiscountDetail:                 newInsOrderDiscountDetail(db, opts...),
		InsOrderInfo:                           newInsOrderInfo(db, opts...),
		InsOrderInfoDetails:                    newInsOrderInfoDetails(db, opts...),
		InsOrderInfoPackage:                    newInsOrderInfoPackage(db, opts...),
		InsOrderPayDetail:                      newInsOrderPayDetail(db, opts...),
		InsOrderReturn:                         newInsOrderReturn(db, opts...),
		InsOrderReturnDetails:                  newInsOrderReturnDetails(db, opts...),
		InsOrderShoppingCart:                   newInsOrderShoppingCart(db, opts...),
		InsOrderShoppingCartDetail:             newInsOrderShoppingCartDetail(db, opts...),
		InsPayConfig:                           newInsPayConfig(db, opts...),
		InsPayment:                             newInsPayment(db, opts...),
		InsProduct:                             newInsProduct(db, opts...),
		InsProductActivity:                     newInsProductActivity(db, opts...),
		InsProductActivityDetail:               newInsProductActivityDetail(db, opts...),
		InsProductActivityFormula:              newInsProductActivityFormula(db, opts...),
		InsProductCategory:                     newInsProductCategory(db, opts...),
		InsProductDetails:                      newInsProductDetails(db, opts...),
		InsProductPackage:                      newInsProductPackage(db, opts...),
		InsProductPackageDetails:               newInsProductPackageDetails(db, opts...),
		InsProductPackageItem:                  newInsProductPackageItem(db, opts...),
		InsProductPackageItemDetails:           newInsProductPackageItemDetails(db, opts...),
		InsProductPackageItemTemplate:          newInsProductPackageItemTemplate(db, opts...),
		InsProductPackageItemTemplateBind:      newInsProductPackageItemTemplateBind(db, opts...),
		InsProductPackageItemTemplateDetails:   newInsProductPackageItemTemplateDetails(db, opts...),
		InsProductPromotion:                    newInsProductPromotion(db, opts...),
		InsProductPromotionApplicable:          newInsProductPromotionApplicable(db, opts...),
		InsProductPromotionCategory:            newInsProductPromotionCategory(db, opts...),
		InsProductPromotionExclusion:           newInsProductPromotionExclusion(db, opts...),
		InsProductPromotionProduct:             newInsProductPromotionProduct(db, opts...),
		InsProductPromotionTime:                newInsProductPromotionTime(db, opts...),
		InsProductSoldOut:                      newInsProductSoldOut(db, opts...),
		InsProductStall:                        newInsProductStall(db, opts...),
		InsProductStore:                        newInsProductStore(db, opts...),
		InsProductSupplier:                     newInsProductSupplier(db, opts...),
		InsQueueChannel:                        newInsQueueChannel(db, opts...),
		InsQueueChannelResource:                newInsQueueChannelResource(db, opts...),
		InsQueueItem:                           newInsQueueItem(db, opts...),
		InsQueueItemEventHistory:               newInsQueueItemEventHistory(db, opts...),
		InsRechargeCard:                        newInsRechargeCard(db, opts...),
		InsRechargeRule:                        newInsRechargeRule(db, opts...),
		InsReconciliationAdjustment:            newInsReconciliationAdjustment(db, opts...),
		InsReconciliationDataSource:            newInsReconciliationDataSource(db, opts...),
		InsReconciliationResult:                newInsReconciliationResult(db, opts...),
		InsReconciliationRule:                  newInsReconciliationRule(db, opts...),
		InsReconciliationTask:                  newInsReconciliationTask(db, opts...),
		InsReport:                              newInsReport(db, opts...),
		InsReportCategory:                      newInsReportCategory(db, opts...),
		InsReportDataSalesValue:                newInsReportDataSalesValue(db, opts...),
		InsReportField:                         newInsReportField(db, opts...),
		InsReportIntermediateResult:            newInsReportIntermediateResult(db, opts...),
		InsReportIntermediateResultDetails:     newInsReportIntermediateResultDetails(db, opts...),
		InsReportRuleSalesShare:                newInsReportRuleSalesShare(db, opts...),
		InsReportRuleSalesShareOrg:             newInsReportRuleSalesShareOrg(db, opts...),
		InsReportRuleSalesShareProduct:         newInsReportRuleSalesShareProduct(db, opts...),
		InsReportRuleSalesShareProductCategory: newInsReportRuleSalesShareProductCategory(db, opts...),
		InsReportRuleSalesShareRecharge:        newInsReportRuleSalesShareRecharge(db, opts...),
		InsReportRuleSalesShareStaff:           newInsReportRuleSalesShareStaff(db, opts...),
		InsReportRuleSalesShareTimeFrame:       newInsReportRuleSalesShareTimeFrame(db, opts...),
		InsReportSalesShare:                    newInsReportSalesShare(db, opts...),
		InsReportStatisticalRule:               newInsReportStatisticalRule(db, opts...),
		InsReportStatisticalRuleDetail:         newInsReportStatisticalRuleDetail(db, opts...),
		InsSalerCode:                           newInsSalerCode(db, opts...),
		InsSalerStore:                          newInsSalerStore(db, opts...),
		InsScanFlow:                            newInsScanFlow(db, opts...),
		InsServiceFee:                          newInsServiceFee(db, opts...),
		InsServiceFeeApplicable:                newInsServiceFeeApplicable(db, opts...),
		InsServiceFeeCategory:                  newInsServiceFeeCategory(db, opts...),
		InsServiceFeeDesk:                      newInsServiceFeeDesk(db, opts...),
		InsServiceFeeDeskCurrent:               newInsServiceFeeDeskCurrent(db, opts...),
		InsServiceFeeExclusion:                 newInsServiceFeeExclusion(db, opts...),
		InsServiceFeeProduct:                   newInsServiceFeeProduct(db, opts...),
		InsServiceFeeTime:                      newInsServiceFeeTime(db, opts...),
		InsShipment:                            newInsShipment(db, opts...),
		InsSqlReport:                           newInsSqlReport(db, opts...),
		InsSqlReportCate:                       newInsSqlReportCate(db, opts...),
		InsSqlRule:                             newInsSqlRule(db, opts...),
		InsSqlRuleHasReport:                    newInsSqlRuleHasReport(db, opts...),
		InsSqlRuleHasUser:                      newInsSqlRuleHasUser(db, opts...),
		InsStore:                               newInsStore(db, opts...),
		InsStoreBusinessConfig:                 newInsStoreBusinessConfig(db, opts...),
		InsStoreConsumeSnapshot:                newInsStoreConsumeSnapshot(db, opts...),
		InsStoreManualOp:                       newInsStoreManualOp(db, opts...),
		InsStoreRevenueReport:                  newInsStoreRevenueReport(db, opts...),
		InsStoreTerminal:                       newInsStoreTerminal(db, opts...),
		InsStoreTicketConfig:                   newInsStoreTicketConfig(db, opts...),
		InsStoreWarehouseCostPrice:             newInsStoreWarehouseCostPrice(db, opts...),
		InsSundriesMaterial:                    newInsSundriesMaterial(db, opts...),
		InsSundriesMaterialHistory:             newInsSundriesMaterialHistory(db, opts...),
		InsSundriesMaterialSupplier:            newInsSundriesMaterialSupplier(db, opts...),
		InsSupplier:                            newInsSupplier(db, opts...),
		InsSysCounter:                          newInsSysCounter(db, opts...),
		InsSysDevPrinter:                       newInsSysDevPrinter(db, opts...),
		InsSysDevPrinterConfig:                 newInsSysDevPrinterConfig(db, opts...),
		InsSysPrintTemplate:                    newInsSysPrintTemplate(db, opts...),
		InsSysStorePrintTemplate:               newInsSysStorePrintTemplate(db, opts...),
		InsTicketOfflineRecord:                 newInsTicketOfflineRecord(db, opts...),
		InsTrade:                               newInsTrade(db, opts...),
		InsTradeAccount:                        newInsTradeAccount(db, opts...),
		InsTradeAccountConfig:                  newInsTradeAccountConfig(db, opts...),
		InsTradeAccountConfigStore:             newInsTradeAccountConfigStore(db, opts...),
		InsTradeAccountDetails:                 newInsTradeAccountDetails(db, opts...),
		InsTradeCoupon:                         newInsTradeCoupon(db, opts...),
		InsTradeCustom:                         newInsTradeCustom(db, opts...),
		InsTradePay:                            newInsTradePay(db, opts...),
		InsTradeRefunds:                        newInsTradeRefunds(db, opts...),
		InsTradeReverseLog:                     newInsTradeReverseLog(db, opts...),
		InsTransferExecution:                   newInsTransferExecution(db, opts...),
		InsTransferExecutionStep:               newInsTransferExecutionStep(db, opts...),
		InsTransferProcess:                     newInsTransferProcess(db, opts...),
		InsTransferProcessStep:                 newInsTransferProcessStep(db, opts...),
		InsUserAuthorityStore:                  newInsUserAuthorityStore(db, opts...),
		InsUserRegister:                        newInsUserRegister(db, opts...),
		InsVerifyCode:                          newInsVerifyCode(db, opts...),
		InsVipCard:                             newInsVipCard(db, opts...),
		InsVipLevel:                            newInsVipLevel(db, opts...),
		InsVipMember:                           newInsVipMember(db, opts...),
		InsVipScoreLog:                         newInsVipScoreLog(db, opts...),
		InsWarehouse:                           newInsWarehouse(db, opts...),
		InsWarehouseAdmin:                      newInsWarehouseAdmin(db, opts...),
		InsWarehouseCheckDay:                   newInsWarehouseCheckDay(db, opts...),
		InsWarehouseCheckDayDetail:             newInsWarehouseCheckDayDetail(db, opts...),
		InsWarehouseEmpties:                    newInsWarehouseEmpties(db, opts...),
		InsWarehouseEmptiesDetail:              newInsWarehouseEmptiesDetail(db, opts...),
		InsWarehouseHaiChang:                   newInsWarehouseHaiChang(db, opts...),
		InsWarehouseHaiChangDetails:            newInsWarehouseHaiChangDetails(db, opts...),
		InsWarehouseHaiChangImportLog:          newInsWarehouseHaiChangImportLog(db, opts...),
		InsWarehouseInout:                      newInsWarehouseInout(db, opts...),
		InsWarehouseInoutApply:                 newInsWarehouseInoutApply(db, opts...),
		InsWarehouseInoutDetailUnique:          newInsWarehouseInoutDetailUnique(db, opts...),
		InsWarehouseInoutDetails:               newInsWarehouseInoutDetails(db, opts...),
		InsWarehouseInoutLog:                   newInsWarehouseInoutLog(db, opts...),
		InsWarehouseInoutReceipt:               newInsWarehouseInoutReceipt(db, opts...),
		InsWarehouseInoutType:                  newInsWarehouseInoutType(db, opts...),
		InsWarehouseInventory:                  newInsWarehouseInventory(db, opts...),
		InsWarehouseInventoryBatch:             newInsWarehouseInventoryBatch(db, opts...),
		InsWarehouseInventoryBatchHistory:      newInsWarehouseInventoryBatchHistory(db, opts...),
		InsWarehouseInventoryDay:               newInsWarehouseInventoryDay(db, opts...),
		InsWarehouseInventoryDetail:            newInsWarehouseInventoryDetail(db, opts...),
		InsWarehouseInventoryUnique:            newInsWarehouseInventoryUnique(db, opts...),
		InsWarehouseSaleLog:                    newInsWarehouseSaleLog(db, opts...),
		InsWxConfig:                            newInsWxConfig(db, opts...),
		OrgUser:                                newOrgUser(db, opts...),
		Organization:                           newOrganization(db, opts...),
		SysAuthority:                           newSysAuthority(db, opts...),
		SysAuthority2:                          newSysAuthority2(db, opts...),
		SysAuthority2Item:                      newSysAuthority2Item(db, opts...),
		SysAuthority2User:                      newSysAuthority2User(db, opts...),
		SysUser:                                newSysUser(db, opts...),
		SysUserAuthority:                       newSysUserAuthority(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	BusinessCate                           businessCate
	HyCheckout                             hyCheckout
	InsActBookDeskConf                     insActBookDeskConf
	InsActBookDeskConfBackup               insActBookDeskConfBackup
	InsActBookDeskConfPackage              insActBookDeskConfPackage
	InsActBookDeskRecord                   insActBookDeskRecord
	InsActBookDeskTable                    insActBookDeskTable
	InsActivityContent                     insActivityContent
	InsAudit                               insAudit
	InsAuditConfig                         insAuditConfig
	InsAuditFlow                           insAuditFlow
	InsAuditFlowDetail                     insAuditFlowDetail
	InsBalanceBulk                         insBalanceBulk
	InsBalanceBulkDetails                  insBalanceBulkDetails
	InsBalanceLog                          insBalanceLog
	InsBook                                insBook
	InsBrand                               insBrand
	InsBusinessConfig                      insBusinessConfig
	InsBusinessPayment                     insBusinessPayment
	InsConfHoliday                         insConfHoliday
	InsConfigCenter                        insConfigCenter
	InsConfigCenterDetail                  insConfigCenterDetail
	InsContract                            insContract
	InsContractComment                     insContractComment
	InsContractFile                        insContractFile
	InsContractSyncLog                     insContractSyncLog
	InsContractTask                        insContractTask
	InsContractTimeline                    insContractTimeline
	InsCostCard                            insCostCard
	InsCostCardDetail                      insCostCardDetail
	InsCostPriceHistory                    insCostPriceHistory
	InsCostPriceTotal                      insCostPriceTotal
	InsCoupon                              insCoupon
	InsCouponApplyScope                    insCouponApplyScope
	InsCouponCash                          insCouponCash
	InsCouponDiscount                      insCouponDiscount
	InsCouponProduct                       insCouponProduct
	InsCouponSendDetail                    insCouponSendDetail
	InsCouponSendRule                      insCouponSendRule
	InsCouponValidWeekDay                  insCouponValidWeekDay
	InsCustomPayment                       insCustomPayment
	InsDeposit                             insDeposit
	InsDepositInoutReportDay               insDepositInoutReportDay
	InsDepositLog                          insDepositLog
	InsDepositRecord                       insDepositRecord
	InsDepositReportData                   insDepositReportData
	InsDepositSmsRecord                    insDepositSmsRecord
	InsDepositVipReport                    insDepositVipReport
	InsDesk                                insDesk
	InsDeskArea                            insDeskArea
	InsDeskCategory                        insDeskCategory
	InsDeskCategoryMinConsumption          insDeskCategoryMinConsumption
	InsDeskMinConsumption                  insDeskMinConsumption
	InsDeskOpen                            insDeskOpen
	InsDeskOpenLog                         insDeskOpenLog
	InsDeskPeriodMinConsumption            insDeskPeriodMinConsumption
	InsDeskPrintLog                        insDeskPrintLog
	InsDeskStatus                          insDeskStatus
	InsDeskStatusLog                       insDeskStatusLog
	InsEmptyBottleReport                   insEmptyBottleReport
	InsEventTrackingLogs                   insEventTrackingLogs
	InsEventsLog                           insEventsLog
	InsExcelTemplate                       insExcelTemplate
	InsExtConf                             insExtConf
	InsExtKezeeAddition                    insExtKezeeAddition
	InsExtKezeeOpenDesk                    insExtKezeeOpenDesk
	InsExtKezeeOrder                       insExtKezeeOrder
	InsExtKezeeOrderItem                   insExtKezeeOrderItem
	InsExtKezeeOrderPay                    insExtKezeeOrderPay
	InsExtStoreSales                       insExtStoreSales
	InsExtStoreTicket                      insExtStoreTicket
	InsGiftLog                             insGiftLog
	InsGiftLogDetail                       insGiftLogDetail
	InsGiftQuotaApplyLog                   insGiftQuotaApplyLog
	InsGiftQuotaAssignLogDetail            insGiftQuotaAssignLogDetail
	InsGiftQuotaAssignUser                 insGiftQuotaAssignUser
	InsGiftQuotaMonth                      insGiftQuotaMonth
	InsGiftQuotaMonthNow                   insGiftQuotaMonthNow
	InsGiftRuleDetail                      insGiftRuleDetail
	InsGiftRuleMember                      insGiftRuleMember
	InsGiftRuleProduct                     insGiftRuleProduct
	InsGiftRules                           insGiftRules
	InsImportLog                           insImportLog
	InsInventoryMaterialFlow               insInventoryMaterialFlow
	InsInventoryProductFlow                insInventoryProductFlow
	InsLangMap                             insLangMap
	InsLangTranslation                     insLangTranslation
	InsMaterial                            insMaterial
	InsMaterialCategory                    insMaterialCategory
	InsMaterialHistory                     insMaterialHistory
	InsMaterialStandard                    insMaterialStandard
	InsMaterialStandardRel                 insMaterialStandardRel
	InsMaterialSupplier                    insMaterialSupplier
	InsMicroOrder                          insMicroOrder
	InsNotes                               insNotes
	InsNotesDetails                        insNotesDetails
	InsOpenDeskSnapshot                    insOpenDeskSnapshot
	InsOrderAdjustment                     insOrderAdjustment
	InsOrderAdjustmentDetail               insOrderAdjustmentDetail
	InsOrderBill                           insOrderBill
	InsOrderDiscountDetail                 insOrderDiscountDetail
	InsOrderInfo                           insOrderInfo
	InsOrderInfoDetails                    insOrderInfoDetails
	InsOrderInfoPackage                    insOrderInfoPackage
	InsOrderPayDetail                      insOrderPayDetail
	InsOrderReturn                         insOrderReturn
	InsOrderReturnDetails                  insOrderReturnDetails
	InsOrderShoppingCart                   insOrderShoppingCart
	InsOrderShoppingCartDetail             insOrderShoppingCartDetail
	InsPayConfig                           insPayConfig
	InsPayment                             insPayment
	InsProduct                             insProduct
	InsProductActivity                     insProductActivity
	InsProductActivityDetail               insProductActivityDetail
	InsProductActivityFormula              insProductActivityFormula
	InsProductCategory                     insProductCategory
	InsProductDetails                      insProductDetails
	InsProductPackage                      insProductPackage
	InsProductPackageDetails               insProductPackageDetails
	InsProductPackageItem                  insProductPackageItem
	InsProductPackageItemDetails           insProductPackageItemDetails
	InsProductPackageItemTemplate          insProductPackageItemTemplate
	InsProductPackageItemTemplateBind      insProductPackageItemTemplateBind
	InsProductPackageItemTemplateDetails   insProductPackageItemTemplateDetails
	InsProductPromotion                    insProductPromotion
	InsProductPromotionApplicable          insProductPromotionApplicable
	InsProductPromotionCategory            insProductPromotionCategory
	InsProductPromotionExclusion           insProductPromotionExclusion
	InsProductPromotionProduct             insProductPromotionProduct
	InsProductPromotionTime                insProductPromotionTime
	InsProductSoldOut                      insProductSoldOut
	InsProductStall                        insProductStall
	InsProductStore                        insProductStore
	InsProductSupplier                     insProductSupplier
	InsQueueChannel                        insQueueChannel
	InsQueueChannelResource                insQueueChannelResource
	InsQueueItem                           insQueueItem
	InsQueueItemEventHistory               insQueueItemEventHistory
	InsRechargeCard                        insRechargeCard
	InsRechargeRule                        insRechargeRule
	InsReconciliationAdjustment            insReconciliationAdjustment
	InsReconciliationDataSource            insReconciliationDataSource
	InsReconciliationResult                insReconciliationResult
	InsReconciliationRule                  insReconciliationRule
	InsReconciliationTask                  insReconciliationTask
	InsReport                              insReport
	InsReportCategory                      insReportCategory
	InsReportDataSalesValue                insReportDataSalesValue
	InsReportField                         insReportField
	InsReportIntermediateResult            insReportIntermediateResult
	InsReportIntermediateResultDetails     insReportIntermediateResultDetails
	InsReportRuleSalesShare                insReportRuleSalesShare
	InsReportRuleSalesShareOrg             insReportRuleSalesShareOrg
	InsReportRuleSalesShareProduct         insReportRuleSalesShareProduct
	InsReportRuleSalesShareProductCategory insReportRuleSalesShareProductCategory
	InsReportRuleSalesShareRecharge        insReportRuleSalesShareRecharge
	InsReportRuleSalesShareStaff           insReportRuleSalesShareStaff
	InsReportRuleSalesShareTimeFrame       insReportRuleSalesShareTimeFrame
	InsReportSalesShare                    insReportSalesShare
	InsReportStatisticalRule               insReportStatisticalRule
	InsReportStatisticalRuleDetail         insReportStatisticalRuleDetail
	InsSalerCode                           insSalerCode
	InsSalerStore                          insSalerStore
	InsScanFlow                            insScanFlow
	InsServiceFee                          insServiceFee
	InsServiceFeeApplicable                insServiceFeeApplicable
	InsServiceFeeCategory                  insServiceFeeCategory
	InsServiceFeeDesk                      insServiceFeeDesk
	InsServiceFeeDeskCurrent               insServiceFeeDeskCurrent
	InsServiceFeeExclusion                 insServiceFeeExclusion
	InsServiceFeeProduct                   insServiceFeeProduct
	InsServiceFeeTime                      insServiceFeeTime
	InsShipment                            insShipment
	InsSqlReport                           insSqlReport
	InsSqlReportCate                       insSqlReportCate
	InsSqlRule                             insSqlRule
	InsSqlRuleHasReport                    insSqlRuleHasReport
	InsSqlRuleHasUser                      insSqlRuleHasUser
	InsStore                               insStore
	InsStoreBusinessConfig                 insStoreBusinessConfig
	InsStoreConsumeSnapshot                insStoreConsumeSnapshot
	InsStoreManualOp                       insStoreManualOp
	InsStoreRevenueReport                  insStoreRevenueReport
	InsStoreTerminal                       insStoreTerminal
	InsStoreTicketConfig                   insStoreTicketConfig
	InsStoreWarehouseCostPrice             insStoreWarehouseCostPrice
	InsSundriesMaterial                    insSundriesMaterial
	InsSundriesMaterialHistory             insSundriesMaterialHistory
	InsSundriesMaterialSupplier            insSundriesMaterialSupplier
	InsSupplier                            insSupplier
	InsSysCounter                          insSysCounter
	InsSysDevPrinter                       insSysDevPrinter
	InsSysDevPrinterConfig                 insSysDevPrinterConfig
	InsSysPrintTemplate                    insSysPrintTemplate
	InsSysStorePrintTemplate               insSysStorePrintTemplate
	InsTicketOfflineRecord                 insTicketOfflineRecord
	InsTrade                               insTrade
	InsTradeAccount                        insTradeAccount
	InsTradeAccountConfig                  insTradeAccountConfig
	InsTradeAccountConfigStore             insTradeAccountConfigStore
	InsTradeAccountDetails                 insTradeAccountDetails
	InsTradeCoupon                         insTradeCoupon
	InsTradeCustom                         insTradeCustom
	InsTradePay                            insTradePay
	InsTradeRefunds                        insTradeRefunds
	InsTradeReverseLog                     insTradeReverseLog
	InsTransferExecution                   insTransferExecution
	InsTransferExecutionStep               insTransferExecutionStep
	InsTransferProcess                     insTransferProcess
	InsTransferProcessStep                 insTransferProcessStep
	InsUserAuthorityStore                  insUserAuthorityStore
	InsUserRegister                        insUserRegister
	InsVerifyCode                          insVerifyCode
	InsVipCard                             insVipCard
	InsVipLevel                            insVipLevel
	InsVipMember                           insVipMember
	InsVipScoreLog                         insVipScoreLog
	InsWarehouse                           insWarehouse
	InsWarehouseAdmin                      insWarehouseAdmin
	InsWarehouseCheckDay                   insWarehouseCheckDay
	InsWarehouseCheckDayDetail             insWarehouseCheckDayDetail
	InsWarehouseEmpties                    insWarehouseEmpties
	InsWarehouseEmptiesDetail              insWarehouseEmptiesDetail
	InsWarehouseHaiChang                   insWarehouseHaiChang
	InsWarehouseHaiChangDetails            insWarehouseHaiChangDetails
	InsWarehouseHaiChangImportLog          insWarehouseHaiChangImportLog
	InsWarehouseInout                      insWarehouseInout
	InsWarehouseInoutApply                 insWarehouseInoutApply
	InsWarehouseInoutDetailUnique          insWarehouseInoutDetailUnique
	InsWarehouseInoutDetails               insWarehouseInoutDetails
	InsWarehouseInoutLog                   insWarehouseInoutLog
	InsWarehouseInoutReceipt               insWarehouseInoutReceipt
	InsWarehouseInoutType                  insWarehouseInoutType
	InsWarehouseInventory                  insWarehouseInventory
	InsWarehouseInventoryBatch             insWarehouseInventoryBatch
	InsWarehouseInventoryBatchHistory      insWarehouseInventoryBatchHistory
	InsWarehouseInventoryDay               insWarehouseInventoryDay
	InsWarehouseInventoryDetail            insWarehouseInventoryDetail
	InsWarehouseInventoryUnique            insWarehouseInventoryUnique
	InsWarehouseSaleLog                    insWarehouseSaleLog
	InsWxConfig                            insWxConfig
	OrgUser                                orgUser
	Organization                           organization
	SysAuthority                           sysAuthority
	SysAuthority2                          sysAuthority2
	SysAuthority2Item                      sysAuthority2Item
	SysAuthority2User                      sysAuthority2User
	SysUser                                sysUser
	SysUserAuthority                       sysUserAuthority
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                                     db,
		BusinessCate:                           q.BusinessCate.clone(db),
		HyCheckout:                             q.HyCheckout.clone(db),
		InsActBookDeskConf:                     q.InsActBookDeskConf.clone(db),
		InsActBookDeskConfBackup:               q.InsActBookDeskConfBackup.clone(db),
		InsActBookDeskConfPackage:              q.InsActBookDeskConfPackage.clone(db),
		InsActBookDeskRecord:                   q.InsActBookDeskRecord.clone(db),
		InsActBookDeskTable:                    q.InsActBookDeskTable.clone(db),
		InsActivityContent:                     q.InsActivityContent.clone(db),
		InsAudit:                               q.InsAudit.clone(db),
		InsAuditConfig:                         q.InsAuditConfig.clone(db),
		InsAuditFlow:                           q.InsAuditFlow.clone(db),
		InsAuditFlowDetail:                     q.InsAuditFlowDetail.clone(db),
		InsBalanceBulk:                         q.InsBalanceBulk.clone(db),
		InsBalanceBulkDetails:                  q.InsBalanceBulkDetails.clone(db),
		InsBalanceLog:                          q.InsBalanceLog.clone(db),
		InsBook:                                q.InsBook.clone(db),
		InsBrand:                               q.InsBrand.clone(db),
		InsBusinessConfig:                      q.InsBusinessConfig.clone(db),
		InsBusinessPayment:                     q.InsBusinessPayment.clone(db),
		InsConfHoliday:                         q.InsConfHoliday.clone(db),
		InsConfigCenter:                        q.InsConfigCenter.clone(db),
		InsConfigCenterDetail:                  q.InsConfigCenterDetail.clone(db),
		InsContract:                            q.InsContract.clone(db),
		InsContractComment:                     q.InsContractComment.clone(db),
		InsContractFile:                        q.InsContractFile.clone(db),
		InsContractSyncLog:                     q.InsContractSyncLog.clone(db),
		InsContractTask:                        q.InsContractTask.clone(db),
		InsContractTimeline:                    q.InsContractTimeline.clone(db),
		InsCostCard:                            q.InsCostCard.clone(db),
		InsCostCardDetail:                      q.InsCostCardDetail.clone(db),
		InsCostPriceHistory:                    q.InsCostPriceHistory.clone(db),
		InsCostPriceTotal:                      q.InsCostPriceTotal.clone(db),
		InsCoupon:                              q.InsCoupon.clone(db),
		InsCouponApplyScope:                    q.InsCouponApplyScope.clone(db),
		InsCouponCash:                          q.InsCouponCash.clone(db),
		InsCouponDiscount:                      q.InsCouponDiscount.clone(db),
		InsCouponProduct:                       q.InsCouponProduct.clone(db),
		InsCouponSendDetail:                    q.InsCouponSendDetail.clone(db),
		InsCouponSendRule:                      q.InsCouponSendRule.clone(db),
		InsCouponValidWeekDay:                  q.InsCouponValidWeekDay.clone(db),
		InsCustomPayment:                       q.InsCustomPayment.clone(db),
		InsDeposit:                             q.InsDeposit.clone(db),
		InsDepositInoutReportDay:               q.InsDepositInoutReportDay.clone(db),
		InsDepositLog:                          q.InsDepositLog.clone(db),
		InsDepositRecord:                       q.InsDepositRecord.clone(db),
		InsDepositReportData:                   q.InsDepositReportData.clone(db),
		InsDepositSmsRecord:                    q.InsDepositSmsRecord.clone(db),
		InsDepositVipReport:                    q.InsDepositVipReport.clone(db),
		InsDesk:                                q.InsDesk.clone(db),
		InsDeskArea:                            q.InsDeskArea.clone(db),
		InsDeskCategory:                        q.InsDeskCategory.clone(db),
		InsDeskCategoryMinConsumption:          q.InsDeskCategoryMinConsumption.clone(db),
		InsDeskMinConsumption:                  q.InsDeskMinConsumption.clone(db),
		InsDeskOpen:                            q.InsDeskOpen.clone(db),
		InsDeskOpenLog:                         q.InsDeskOpenLog.clone(db),
		InsDeskPeriodMinConsumption:            q.InsDeskPeriodMinConsumption.clone(db),
		InsDeskPrintLog:                        q.InsDeskPrintLog.clone(db),
		InsDeskStatus:                          q.InsDeskStatus.clone(db),
		InsDeskStatusLog:                       q.InsDeskStatusLog.clone(db),
		InsEmptyBottleReport:                   q.InsEmptyBottleReport.clone(db),
		InsEventTrackingLogs:                   q.InsEventTrackingLogs.clone(db),
		InsEventsLog:                           q.InsEventsLog.clone(db),
		InsExcelTemplate:                       q.InsExcelTemplate.clone(db),
		InsExtConf:                             q.InsExtConf.clone(db),
		InsExtKezeeAddition:                    q.InsExtKezeeAddition.clone(db),
		InsExtKezeeOpenDesk:                    q.InsExtKezeeOpenDesk.clone(db),
		InsExtKezeeOrder:                       q.InsExtKezeeOrder.clone(db),
		InsExtKezeeOrderItem:                   q.InsExtKezeeOrderItem.clone(db),
		InsExtKezeeOrderPay:                    q.InsExtKezeeOrderPay.clone(db),
		InsExtStoreSales:                       q.InsExtStoreSales.clone(db),
		InsExtStoreTicket:                      q.InsExtStoreTicket.clone(db),
		InsGiftLog:                             q.InsGiftLog.clone(db),
		InsGiftLogDetail:                       q.InsGiftLogDetail.clone(db),
		InsGiftQuotaApplyLog:                   q.InsGiftQuotaApplyLog.clone(db),
		InsGiftQuotaAssignLogDetail:            q.InsGiftQuotaAssignLogDetail.clone(db),
		InsGiftQuotaAssignUser:                 q.InsGiftQuotaAssignUser.clone(db),
		InsGiftQuotaMonth:                      q.InsGiftQuotaMonth.clone(db),
		InsGiftQuotaMonthNow:                   q.InsGiftQuotaMonthNow.clone(db),
		InsGiftRuleDetail:                      q.InsGiftRuleDetail.clone(db),
		InsGiftRuleMember:                      q.InsGiftRuleMember.clone(db),
		InsGiftRuleProduct:                     q.InsGiftRuleProduct.clone(db),
		InsGiftRules:                           q.InsGiftRules.clone(db),
		InsImportLog:                           q.InsImportLog.clone(db),
		InsInventoryMaterialFlow:               q.InsInventoryMaterialFlow.clone(db),
		InsInventoryProductFlow:                q.InsInventoryProductFlow.clone(db),
		InsLangMap:                             q.InsLangMap.clone(db),
		InsLangTranslation:                     q.InsLangTranslation.clone(db),
		InsMaterial:                            q.InsMaterial.clone(db),
		InsMaterialCategory:                    q.InsMaterialCategory.clone(db),
		InsMaterialHistory:                     q.InsMaterialHistory.clone(db),
		InsMaterialStandard:                    q.InsMaterialStandard.clone(db),
		InsMaterialStandardRel:                 q.InsMaterialStandardRel.clone(db),
		InsMaterialSupplier:                    q.InsMaterialSupplier.clone(db),
		InsMicroOrder:                          q.InsMicroOrder.clone(db),
		InsNotes:                               q.InsNotes.clone(db),
		InsNotesDetails:                        q.InsNotesDetails.clone(db),
		InsOpenDeskSnapshot:                    q.InsOpenDeskSnapshot.clone(db),
		InsOrderAdjustment:                     q.InsOrderAdjustment.clone(db),
		InsOrderAdjustmentDetail:               q.InsOrderAdjustmentDetail.clone(db),
		InsOrderBill:                           q.InsOrderBill.clone(db),
		InsOrderDiscountDetail:                 q.InsOrderDiscountDetail.clone(db),
		InsOrderInfo:                           q.InsOrderInfo.clone(db),
		InsOrderInfoDetails:                    q.InsOrderInfoDetails.clone(db),
		InsOrderInfoPackage:                    q.InsOrderInfoPackage.clone(db),
		InsOrderPayDetail:                      q.InsOrderPayDetail.clone(db),
		InsOrderReturn:                         q.InsOrderReturn.clone(db),
		InsOrderReturnDetails:                  q.InsOrderReturnDetails.clone(db),
		InsOrderShoppingCart:                   q.InsOrderShoppingCart.clone(db),
		InsOrderShoppingCartDetail:             q.InsOrderShoppingCartDetail.clone(db),
		InsPayConfig:                           q.InsPayConfig.clone(db),
		InsPayment:                             q.InsPayment.clone(db),
		InsProduct:                             q.InsProduct.clone(db),
		InsProductActivity:                     q.InsProductActivity.clone(db),
		InsProductActivityDetail:               q.InsProductActivityDetail.clone(db),
		InsProductActivityFormula:              q.InsProductActivityFormula.clone(db),
		InsProductCategory:                     q.InsProductCategory.clone(db),
		InsProductDetails:                      q.InsProductDetails.clone(db),
		InsProductPackage:                      q.InsProductPackage.clone(db),
		InsProductPackageDetails:               q.InsProductPackageDetails.clone(db),
		InsProductPackageItem:                  q.InsProductPackageItem.clone(db),
		InsProductPackageItemDetails:           q.InsProductPackageItemDetails.clone(db),
		InsProductPackageItemTemplate:          q.InsProductPackageItemTemplate.clone(db),
		InsProductPackageItemTemplateBind:      q.InsProductPackageItemTemplateBind.clone(db),
		InsProductPackageItemTemplateDetails:   q.InsProductPackageItemTemplateDetails.clone(db),
		InsProductPromotion:                    q.InsProductPromotion.clone(db),
		InsProductPromotionApplicable:          q.InsProductPromotionApplicable.clone(db),
		InsProductPromotionCategory:            q.InsProductPromotionCategory.clone(db),
		InsProductPromotionExclusion:           q.InsProductPromotionExclusion.clone(db),
		InsProductPromotionProduct:             q.InsProductPromotionProduct.clone(db),
		InsProductPromotionTime:                q.InsProductPromotionTime.clone(db),
		InsProductSoldOut:                      q.InsProductSoldOut.clone(db),
		InsProductStall:                        q.InsProductStall.clone(db),
		InsProductStore:                        q.InsProductStore.clone(db),
		InsProductSupplier:                     q.InsProductSupplier.clone(db),
		InsQueueChannel:                        q.InsQueueChannel.clone(db),
		InsQueueChannelResource:                q.InsQueueChannelResource.clone(db),
		InsQueueItem:                           q.InsQueueItem.clone(db),
		InsQueueItemEventHistory:               q.InsQueueItemEventHistory.clone(db),
		InsRechargeCard:                        q.InsRechargeCard.clone(db),
		InsRechargeRule:                        q.InsRechargeRule.clone(db),
		InsReconciliationAdjustment:            q.InsReconciliationAdjustment.clone(db),
		InsReconciliationDataSource:            q.InsReconciliationDataSource.clone(db),
		InsReconciliationResult:                q.InsReconciliationResult.clone(db),
		InsReconciliationRule:                  q.InsReconciliationRule.clone(db),
		InsReconciliationTask:                  q.InsReconciliationTask.clone(db),
		InsReport:                              q.InsReport.clone(db),
		InsReportCategory:                      q.InsReportCategory.clone(db),
		InsReportDataSalesValue:                q.InsReportDataSalesValue.clone(db),
		InsReportField:                         q.InsReportField.clone(db),
		InsReportIntermediateResult:            q.InsReportIntermediateResult.clone(db),
		InsReportIntermediateResultDetails:     q.InsReportIntermediateResultDetails.clone(db),
		InsReportRuleSalesShare:                q.InsReportRuleSalesShare.clone(db),
		InsReportRuleSalesShareOrg:             q.InsReportRuleSalesShareOrg.clone(db),
		InsReportRuleSalesShareProduct:         q.InsReportRuleSalesShareProduct.clone(db),
		InsReportRuleSalesShareProductCategory: q.InsReportRuleSalesShareProductCategory.clone(db),
		InsReportRuleSalesShareRecharge:        q.InsReportRuleSalesShareRecharge.clone(db),
		InsReportRuleSalesShareStaff:           q.InsReportRuleSalesShareStaff.clone(db),
		InsReportRuleSalesShareTimeFrame:       q.InsReportRuleSalesShareTimeFrame.clone(db),
		InsReportSalesShare:                    q.InsReportSalesShare.clone(db),
		InsReportStatisticalRule:               q.InsReportStatisticalRule.clone(db),
		InsReportStatisticalRuleDetail:         q.InsReportStatisticalRuleDetail.clone(db),
		InsSalerCode:                           q.InsSalerCode.clone(db),
		InsSalerStore:                          q.InsSalerStore.clone(db),
		InsScanFlow:                            q.InsScanFlow.clone(db),
		InsServiceFee:                          q.InsServiceFee.clone(db),
		InsServiceFeeApplicable:                q.InsServiceFeeApplicable.clone(db),
		InsServiceFeeCategory:                  q.InsServiceFeeCategory.clone(db),
		InsServiceFeeDesk:                      q.InsServiceFeeDesk.clone(db),
		InsServiceFeeDeskCurrent:               q.InsServiceFeeDeskCurrent.clone(db),
		InsServiceFeeExclusion:                 q.InsServiceFeeExclusion.clone(db),
		InsServiceFeeProduct:                   q.InsServiceFeeProduct.clone(db),
		InsServiceFeeTime:                      q.InsServiceFeeTime.clone(db),
		InsShipment:                            q.InsShipment.clone(db),
		InsSqlReport:                           q.InsSqlReport.clone(db),
		InsSqlReportCate:                       q.InsSqlReportCate.clone(db),
		InsSqlRule:                             q.InsSqlRule.clone(db),
		InsSqlRuleHasReport:                    q.InsSqlRuleHasReport.clone(db),
		InsSqlRuleHasUser:                      q.InsSqlRuleHasUser.clone(db),
		InsStore:                               q.InsStore.clone(db),
		InsStoreBusinessConfig:                 q.InsStoreBusinessConfig.clone(db),
		InsStoreConsumeSnapshot:                q.InsStoreConsumeSnapshot.clone(db),
		InsStoreManualOp:                       q.InsStoreManualOp.clone(db),
		InsStoreRevenueReport:                  q.InsStoreRevenueReport.clone(db),
		InsStoreTerminal:                       q.InsStoreTerminal.clone(db),
		InsStoreTicketConfig:                   q.InsStoreTicketConfig.clone(db),
		InsStoreWarehouseCostPrice:             q.InsStoreWarehouseCostPrice.clone(db),
		InsSundriesMaterial:                    q.InsSundriesMaterial.clone(db),
		InsSundriesMaterialHistory:             q.InsSundriesMaterialHistory.clone(db),
		InsSundriesMaterialSupplier:            q.InsSundriesMaterialSupplier.clone(db),
		InsSupplier:                            q.InsSupplier.clone(db),
		InsSysCounter:                          q.InsSysCounter.clone(db),
		InsSysDevPrinter:                       q.InsSysDevPrinter.clone(db),
		InsSysDevPrinterConfig:                 q.InsSysDevPrinterConfig.clone(db),
		InsSysPrintTemplate:                    q.InsSysPrintTemplate.clone(db),
		InsSysStorePrintTemplate:               q.InsSysStorePrintTemplate.clone(db),
		InsTicketOfflineRecord:                 q.InsTicketOfflineRecord.clone(db),
		InsTrade:                               q.InsTrade.clone(db),
		InsTradeAccount:                        q.InsTradeAccount.clone(db),
		InsTradeAccountConfig:                  q.InsTradeAccountConfig.clone(db),
		InsTradeAccountConfigStore:             q.InsTradeAccountConfigStore.clone(db),
		InsTradeAccountDetails:                 q.InsTradeAccountDetails.clone(db),
		InsTradeCoupon:                         q.InsTradeCoupon.clone(db),
		InsTradeCustom:                         q.InsTradeCustom.clone(db),
		InsTradePay:                            q.InsTradePay.clone(db),
		InsTradeRefunds:                        q.InsTradeRefunds.clone(db),
		InsTradeReverseLog:                     q.InsTradeReverseLog.clone(db),
		InsTransferExecution:                   q.InsTransferExecution.clone(db),
		InsTransferExecutionStep:               q.InsTransferExecutionStep.clone(db),
		InsTransferProcess:                     q.InsTransferProcess.clone(db),
		InsTransferProcessStep:                 q.InsTransferProcessStep.clone(db),
		InsUserAuthorityStore:                  q.InsUserAuthorityStore.clone(db),
		InsUserRegister:                        q.InsUserRegister.clone(db),
		InsVerifyCode:                          q.InsVerifyCode.clone(db),
		InsVipCard:                             q.InsVipCard.clone(db),
		InsVipLevel:                            q.InsVipLevel.clone(db),
		InsVipMember:                           q.InsVipMember.clone(db),
		InsVipScoreLog:                         q.InsVipScoreLog.clone(db),
		InsWarehouse:                           q.InsWarehouse.clone(db),
		InsWarehouseAdmin:                      q.InsWarehouseAdmin.clone(db),
		InsWarehouseCheckDay:                   q.InsWarehouseCheckDay.clone(db),
		InsWarehouseCheckDayDetail:             q.InsWarehouseCheckDayDetail.clone(db),
		InsWarehouseEmpties:                    q.InsWarehouseEmpties.clone(db),
		InsWarehouseEmptiesDetail:              q.InsWarehouseEmptiesDetail.clone(db),
		InsWarehouseHaiChang:                   q.InsWarehouseHaiChang.clone(db),
		InsWarehouseHaiChangDetails:            q.InsWarehouseHaiChangDetails.clone(db),
		InsWarehouseHaiChangImportLog:          q.InsWarehouseHaiChangImportLog.clone(db),
		InsWarehouseInout:                      q.InsWarehouseInout.clone(db),
		InsWarehouseInoutApply:                 q.InsWarehouseInoutApply.clone(db),
		InsWarehouseInoutDetailUnique:          q.InsWarehouseInoutDetailUnique.clone(db),
		InsWarehouseInoutDetails:               q.InsWarehouseInoutDetails.clone(db),
		InsWarehouseInoutLog:                   q.InsWarehouseInoutLog.clone(db),
		InsWarehouseInoutReceipt:               q.InsWarehouseInoutReceipt.clone(db),
		InsWarehouseInoutType:                  q.InsWarehouseInoutType.clone(db),
		InsWarehouseInventory:                  q.InsWarehouseInventory.clone(db),
		InsWarehouseInventoryBatch:             q.InsWarehouseInventoryBatch.clone(db),
		InsWarehouseInventoryBatchHistory:      q.InsWarehouseInventoryBatchHistory.clone(db),
		InsWarehouseInventoryDay:               q.InsWarehouseInventoryDay.clone(db),
		InsWarehouseInventoryDetail:            q.InsWarehouseInventoryDetail.clone(db),
		InsWarehouseInventoryUnique:            q.InsWarehouseInventoryUnique.clone(db),
		InsWarehouseSaleLog:                    q.InsWarehouseSaleLog.clone(db),
		InsWxConfig:                            q.InsWxConfig.clone(db),
		OrgUser:                                q.OrgUser.clone(db),
		Organization:                           q.Organization.clone(db),
		SysAuthority:                           q.SysAuthority.clone(db),
		SysAuthority2:                          q.SysAuthority2.clone(db),
		SysAuthority2Item:                      q.SysAuthority2Item.clone(db),
		SysAuthority2User:                      q.SysAuthority2User.clone(db),
		SysUser:                                q.SysUser.clone(db),
		SysUserAuthority:                       q.SysUserAuthority.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                                     db,
		BusinessCate:                           q.BusinessCate.replaceDB(db),
		HyCheckout:                             q.HyCheckout.replaceDB(db),
		InsActBookDeskConf:                     q.InsActBookDeskConf.replaceDB(db),
		InsActBookDeskConfBackup:               q.InsActBookDeskConfBackup.replaceDB(db),
		InsActBookDeskConfPackage:              q.InsActBookDeskConfPackage.replaceDB(db),
		InsActBookDeskRecord:                   q.InsActBookDeskRecord.replaceDB(db),
		InsActBookDeskTable:                    q.InsActBookDeskTable.replaceDB(db),
		InsActivityContent:                     q.InsActivityContent.replaceDB(db),
		InsAudit:                               q.InsAudit.replaceDB(db),
		InsAuditConfig:                         q.InsAuditConfig.replaceDB(db),
		InsAuditFlow:                           q.InsAuditFlow.replaceDB(db),
		InsAuditFlowDetail:                     q.InsAuditFlowDetail.replaceDB(db),
		InsBalanceBulk:                         q.InsBalanceBulk.replaceDB(db),
		InsBalanceBulkDetails:                  q.InsBalanceBulkDetails.replaceDB(db),
		InsBalanceLog:                          q.InsBalanceLog.replaceDB(db),
		InsBook:                                q.InsBook.replaceDB(db),
		InsBrand:                               q.InsBrand.replaceDB(db),
		InsBusinessConfig:                      q.InsBusinessConfig.replaceDB(db),
		InsBusinessPayment:                     q.InsBusinessPayment.replaceDB(db),
		InsConfHoliday:                         q.InsConfHoliday.replaceDB(db),
		InsConfigCenter:                        q.InsConfigCenter.replaceDB(db),
		InsConfigCenterDetail:                  q.InsConfigCenterDetail.replaceDB(db),
		InsContract:                            q.InsContract.replaceDB(db),
		InsContractComment:                     q.InsContractComment.replaceDB(db),
		InsContractFile:                        q.InsContractFile.replaceDB(db),
		InsContractSyncLog:                     q.InsContractSyncLog.replaceDB(db),
		InsContractTask:                        q.InsContractTask.replaceDB(db),
		InsContractTimeline:                    q.InsContractTimeline.replaceDB(db),
		InsCostCard:                            q.InsCostCard.replaceDB(db),
		InsCostCardDetail:                      q.InsCostCardDetail.replaceDB(db),
		InsCostPriceHistory:                    q.InsCostPriceHistory.replaceDB(db),
		InsCostPriceTotal:                      q.InsCostPriceTotal.replaceDB(db),
		InsCoupon:                              q.InsCoupon.replaceDB(db),
		InsCouponApplyScope:                    q.InsCouponApplyScope.replaceDB(db),
		InsCouponCash:                          q.InsCouponCash.replaceDB(db),
		InsCouponDiscount:                      q.InsCouponDiscount.replaceDB(db),
		InsCouponProduct:                       q.InsCouponProduct.replaceDB(db),
		InsCouponSendDetail:                    q.InsCouponSendDetail.replaceDB(db),
		InsCouponSendRule:                      q.InsCouponSendRule.replaceDB(db),
		InsCouponValidWeekDay:                  q.InsCouponValidWeekDay.replaceDB(db),
		InsCustomPayment:                       q.InsCustomPayment.replaceDB(db),
		InsDeposit:                             q.InsDeposit.replaceDB(db),
		InsDepositInoutReportDay:               q.InsDepositInoutReportDay.replaceDB(db),
		InsDepositLog:                          q.InsDepositLog.replaceDB(db),
		InsDepositRecord:                       q.InsDepositRecord.replaceDB(db),
		InsDepositReportData:                   q.InsDepositReportData.replaceDB(db),
		InsDepositSmsRecord:                    q.InsDepositSmsRecord.replaceDB(db),
		InsDepositVipReport:                    q.InsDepositVipReport.replaceDB(db),
		InsDesk:                                q.InsDesk.replaceDB(db),
		InsDeskArea:                            q.InsDeskArea.replaceDB(db),
		InsDeskCategory:                        q.InsDeskCategory.replaceDB(db),
		InsDeskCategoryMinConsumption:          q.InsDeskCategoryMinConsumption.replaceDB(db),
		InsDeskMinConsumption:                  q.InsDeskMinConsumption.replaceDB(db),
		InsDeskOpen:                            q.InsDeskOpen.replaceDB(db),
		InsDeskOpenLog:                         q.InsDeskOpenLog.replaceDB(db),
		InsDeskPeriodMinConsumption:            q.InsDeskPeriodMinConsumption.replaceDB(db),
		InsDeskPrintLog:                        q.InsDeskPrintLog.replaceDB(db),
		InsDeskStatus:                          q.InsDeskStatus.replaceDB(db),
		InsDeskStatusLog:                       q.InsDeskStatusLog.replaceDB(db),
		InsEmptyBottleReport:                   q.InsEmptyBottleReport.replaceDB(db),
		InsEventTrackingLogs:                   q.InsEventTrackingLogs.replaceDB(db),
		InsEventsLog:                           q.InsEventsLog.replaceDB(db),
		InsExcelTemplate:                       q.InsExcelTemplate.replaceDB(db),
		InsExtConf:                             q.InsExtConf.replaceDB(db),
		InsExtKezeeAddition:                    q.InsExtKezeeAddition.replaceDB(db),
		InsExtKezeeOpenDesk:                    q.InsExtKezeeOpenDesk.replaceDB(db),
		InsExtKezeeOrder:                       q.InsExtKezeeOrder.replaceDB(db),
		InsExtKezeeOrderItem:                   q.InsExtKezeeOrderItem.replaceDB(db),
		InsExtKezeeOrderPay:                    q.InsExtKezeeOrderPay.replaceDB(db),
		InsExtStoreSales:                       q.InsExtStoreSales.replaceDB(db),
		InsExtStoreTicket:                      q.InsExtStoreTicket.replaceDB(db),
		InsGiftLog:                             q.InsGiftLog.replaceDB(db),
		InsGiftLogDetail:                       q.InsGiftLogDetail.replaceDB(db),
		InsGiftQuotaApplyLog:                   q.InsGiftQuotaApplyLog.replaceDB(db),
		InsGiftQuotaAssignLogDetail:            q.InsGiftQuotaAssignLogDetail.replaceDB(db),
		InsGiftQuotaAssignUser:                 q.InsGiftQuotaAssignUser.replaceDB(db),
		InsGiftQuotaMonth:                      q.InsGiftQuotaMonth.replaceDB(db),
		InsGiftQuotaMonthNow:                   q.InsGiftQuotaMonthNow.replaceDB(db),
		InsGiftRuleDetail:                      q.InsGiftRuleDetail.replaceDB(db),
		InsGiftRuleMember:                      q.InsGiftRuleMember.replaceDB(db),
		InsGiftRuleProduct:                     q.InsGiftRuleProduct.replaceDB(db),
		InsGiftRules:                           q.InsGiftRules.replaceDB(db),
		InsImportLog:                           q.InsImportLog.replaceDB(db),
		InsInventoryMaterialFlow:               q.InsInventoryMaterialFlow.replaceDB(db),
		InsInventoryProductFlow:                q.InsInventoryProductFlow.replaceDB(db),
		InsLangMap:                             q.InsLangMap.replaceDB(db),
		InsLangTranslation:                     q.InsLangTranslation.replaceDB(db),
		InsMaterial:                            q.InsMaterial.replaceDB(db),
		InsMaterialCategory:                    q.InsMaterialCategory.replaceDB(db),
		InsMaterialHistory:                     q.InsMaterialHistory.replaceDB(db),
		InsMaterialStandard:                    q.InsMaterialStandard.replaceDB(db),
		InsMaterialStandardRel:                 q.InsMaterialStandardRel.replaceDB(db),
		InsMaterialSupplier:                    q.InsMaterialSupplier.replaceDB(db),
		InsMicroOrder:                          q.InsMicroOrder.replaceDB(db),
		InsNotes:                               q.InsNotes.replaceDB(db),
		InsNotesDetails:                        q.InsNotesDetails.replaceDB(db),
		InsOpenDeskSnapshot:                    q.InsOpenDeskSnapshot.replaceDB(db),
		InsOrderAdjustment:                     q.InsOrderAdjustment.replaceDB(db),
		InsOrderAdjustmentDetail:               q.InsOrderAdjustmentDetail.replaceDB(db),
		InsOrderBill:                           q.InsOrderBill.replaceDB(db),
		InsOrderDiscountDetail:                 q.InsOrderDiscountDetail.replaceDB(db),
		InsOrderInfo:                           q.InsOrderInfo.replaceDB(db),
		InsOrderInfoDetails:                    q.InsOrderInfoDetails.replaceDB(db),
		InsOrderInfoPackage:                    q.InsOrderInfoPackage.replaceDB(db),
		InsOrderPayDetail:                      q.InsOrderPayDetail.replaceDB(db),
		InsOrderReturn:                         q.InsOrderReturn.replaceDB(db),
		InsOrderReturnDetails:                  q.InsOrderReturnDetails.replaceDB(db),
		InsOrderShoppingCart:                   q.InsOrderShoppingCart.replaceDB(db),
		InsOrderShoppingCartDetail:             q.InsOrderShoppingCartDetail.replaceDB(db),
		InsPayConfig:                           q.InsPayConfig.replaceDB(db),
		InsPayment:                             q.InsPayment.replaceDB(db),
		InsProduct:                             q.InsProduct.replaceDB(db),
		InsProductActivity:                     q.InsProductActivity.replaceDB(db),
		InsProductActivityDetail:               q.InsProductActivityDetail.replaceDB(db),
		InsProductActivityFormula:              q.InsProductActivityFormula.replaceDB(db),
		InsProductCategory:                     q.InsProductCategory.replaceDB(db),
		InsProductDetails:                      q.InsProductDetails.replaceDB(db),
		InsProductPackage:                      q.InsProductPackage.replaceDB(db),
		InsProductPackageDetails:               q.InsProductPackageDetails.replaceDB(db),
		InsProductPackageItem:                  q.InsProductPackageItem.replaceDB(db),
		InsProductPackageItemDetails:           q.InsProductPackageItemDetails.replaceDB(db),
		InsProductPackageItemTemplate:          q.InsProductPackageItemTemplate.replaceDB(db),
		InsProductPackageItemTemplateBind:      q.InsProductPackageItemTemplateBind.replaceDB(db),
		InsProductPackageItemTemplateDetails:   q.InsProductPackageItemTemplateDetails.replaceDB(db),
		InsProductPromotion:                    q.InsProductPromotion.replaceDB(db),
		InsProductPromotionApplicable:          q.InsProductPromotionApplicable.replaceDB(db),
		InsProductPromotionCategory:            q.InsProductPromotionCategory.replaceDB(db),
		InsProductPromotionExclusion:           q.InsProductPromotionExclusion.replaceDB(db),
		InsProductPromotionProduct:             q.InsProductPromotionProduct.replaceDB(db),
		InsProductPromotionTime:                q.InsProductPromotionTime.replaceDB(db),
		InsProductSoldOut:                      q.InsProductSoldOut.replaceDB(db),
		InsProductStall:                        q.InsProductStall.replaceDB(db),
		InsProductStore:                        q.InsProductStore.replaceDB(db),
		InsProductSupplier:                     q.InsProductSupplier.replaceDB(db),
		InsQueueChannel:                        q.InsQueueChannel.replaceDB(db),
		InsQueueChannelResource:                q.InsQueueChannelResource.replaceDB(db),
		InsQueueItem:                           q.InsQueueItem.replaceDB(db),
		InsQueueItemEventHistory:               q.InsQueueItemEventHistory.replaceDB(db),
		InsRechargeCard:                        q.InsRechargeCard.replaceDB(db),
		InsRechargeRule:                        q.InsRechargeRule.replaceDB(db),
		InsReconciliationAdjustment:            q.InsReconciliationAdjustment.replaceDB(db),
		InsReconciliationDataSource:            q.InsReconciliationDataSource.replaceDB(db),
		InsReconciliationResult:                q.InsReconciliationResult.replaceDB(db),
		InsReconciliationRule:                  q.InsReconciliationRule.replaceDB(db),
		InsReconciliationTask:                  q.InsReconciliationTask.replaceDB(db),
		InsReport:                              q.InsReport.replaceDB(db),
		InsReportCategory:                      q.InsReportCategory.replaceDB(db),
		InsReportDataSalesValue:                q.InsReportDataSalesValue.replaceDB(db),
		InsReportField:                         q.InsReportField.replaceDB(db),
		InsReportIntermediateResult:            q.InsReportIntermediateResult.replaceDB(db),
		InsReportIntermediateResultDetails:     q.InsReportIntermediateResultDetails.replaceDB(db),
		InsReportRuleSalesShare:                q.InsReportRuleSalesShare.replaceDB(db),
		InsReportRuleSalesShareOrg:             q.InsReportRuleSalesShareOrg.replaceDB(db),
		InsReportRuleSalesShareProduct:         q.InsReportRuleSalesShareProduct.replaceDB(db),
		InsReportRuleSalesShareProductCategory: q.InsReportRuleSalesShareProductCategory.replaceDB(db),
		InsReportRuleSalesShareRecharge:        q.InsReportRuleSalesShareRecharge.replaceDB(db),
		InsReportRuleSalesShareStaff:           q.InsReportRuleSalesShareStaff.replaceDB(db),
		InsReportRuleSalesShareTimeFrame:       q.InsReportRuleSalesShareTimeFrame.replaceDB(db),
		InsReportSalesShare:                    q.InsReportSalesShare.replaceDB(db),
		InsReportStatisticalRule:               q.InsReportStatisticalRule.replaceDB(db),
		InsReportStatisticalRuleDetail:         q.InsReportStatisticalRuleDetail.replaceDB(db),
		InsSalerCode:                           q.InsSalerCode.replaceDB(db),
		InsSalerStore:                          q.InsSalerStore.replaceDB(db),
		InsScanFlow:                            q.InsScanFlow.replaceDB(db),
		InsServiceFee:                          q.InsServiceFee.replaceDB(db),
		InsServiceFeeApplicable:                q.InsServiceFeeApplicable.replaceDB(db),
		InsServiceFeeCategory:                  q.InsServiceFeeCategory.replaceDB(db),
		InsServiceFeeDesk:                      q.InsServiceFeeDesk.replaceDB(db),
		InsServiceFeeDeskCurrent:               q.InsServiceFeeDeskCurrent.replaceDB(db),
		InsServiceFeeExclusion:                 q.InsServiceFeeExclusion.replaceDB(db),
		InsServiceFeeProduct:                   q.InsServiceFeeProduct.replaceDB(db),
		InsServiceFeeTime:                      q.InsServiceFeeTime.replaceDB(db),
		InsShipment:                            q.InsShipment.replaceDB(db),
		InsSqlReport:                           q.InsSqlReport.replaceDB(db),
		InsSqlReportCate:                       q.InsSqlReportCate.replaceDB(db),
		InsSqlRule:                             q.InsSqlRule.replaceDB(db),
		InsSqlRuleHasReport:                    q.InsSqlRuleHasReport.replaceDB(db),
		InsSqlRuleHasUser:                      q.InsSqlRuleHasUser.replaceDB(db),
		InsStore:                               q.InsStore.replaceDB(db),
		InsStoreBusinessConfig:                 q.InsStoreBusinessConfig.replaceDB(db),
		InsStoreConsumeSnapshot:                q.InsStoreConsumeSnapshot.replaceDB(db),
		InsStoreManualOp:                       q.InsStoreManualOp.replaceDB(db),
		InsStoreRevenueReport:                  q.InsStoreRevenueReport.replaceDB(db),
		InsStoreTerminal:                       q.InsStoreTerminal.replaceDB(db),
		InsStoreTicketConfig:                   q.InsStoreTicketConfig.replaceDB(db),
		InsStoreWarehouseCostPrice:             q.InsStoreWarehouseCostPrice.replaceDB(db),
		InsSundriesMaterial:                    q.InsSundriesMaterial.replaceDB(db),
		InsSundriesMaterialHistory:             q.InsSundriesMaterialHistory.replaceDB(db),
		InsSundriesMaterialSupplier:            q.InsSundriesMaterialSupplier.replaceDB(db),
		InsSupplier:                            q.InsSupplier.replaceDB(db),
		InsSysCounter:                          q.InsSysCounter.replaceDB(db),
		InsSysDevPrinter:                       q.InsSysDevPrinter.replaceDB(db),
		InsSysDevPrinterConfig:                 q.InsSysDevPrinterConfig.replaceDB(db),
		InsSysPrintTemplate:                    q.InsSysPrintTemplate.replaceDB(db),
		InsSysStorePrintTemplate:               q.InsSysStorePrintTemplate.replaceDB(db),
		InsTicketOfflineRecord:                 q.InsTicketOfflineRecord.replaceDB(db),
		InsTrade:                               q.InsTrade.replaceDB(db),
		InsTradeAccount:                        q.InsTradeAccount.replaceDB(db),
		InsTradeAccountConfig:                  q.InsTradeAccountConfig.replaceDB(db),
		InsTradeAccountConfigStore:             q.InsTradeAccountConfigStore.replaceDB(db),
		InsTradeAccountDetails:                 q.InsTradeAccountDetails.replaceDB(db),
		InsTradeCoupon:                         q.InsTradeCoupon.replaceDB(db),
		InsTradeCustom:                         q.InsTradeCustom.replaceDB(db),
		InsTradePay:                            q.InsTradePay.replaceDB(db),
		InsTradeRefunds:                        q.InsTradeRefunds.replaceDB(db),
		InsTradeReverseLog:                     q.InsTradeReverseLog.replaceDB(db),
		InsTransferExecution:                   q.InsTransferExecution.replaceDB(db),
		InsTransferExecutionStep:               q.InsTransferExecutionStep.replaceDB(db),
		InsTransferProcess:                     q.InsTransferProcess.replaceDB(db),
		InsTransferProcessStep:                 q.InsTransferProcessStep.replaceDB(db),
		InsUserAuthorityStore:                  q.InsUserAuthorityStore.replaceDB(db),
		InsUserRegister:                        q.InsUserRegister.replaceDB(db),
		InsVerifyCode:                          q.InsVerifyCode.replaceDB(db),
		InsVipCard:                             q.InsVipCard.replaceDB(db),
		InsVipLevel:                            q.InsVipLevel.replaceDB(db),
		InsVipMember:                           q.InsVipMember.replaceDB(db),
		InsVipScoreLog:                         q.InsVipScoreLog.replaceDB(db),
		InsWarehouse:                           q.InsWarehouse.replaceDB(db),
		InsWarehouseAdmin:                      q.InsWarehouseAdmin.replaceDB(db),
		InsWarehouseCheckDay:                   q.InsWarehouseCheckDay.replaceDB(db),
		InsWarehouseCheckDayDetail:             q.InsWarehouseCheckDayDetail.replaceDB(db),
		InsWarehouseEmpties:                    q.InsWarehouseEmpties.replaceDB(db),
		InsWarehouseEmptiesDetail:              q.InsWarehouseEmptiesDetail.replaceDB(db),
		InsWarehouseHaiChang:                   q.InsWarehouseHaiChang.replaceDB(db),
		InsWarehouseHaiChangDetails:            q.InsWarehouseHaiChangDetails.replaceDB(db),
		InsWarehouseHaiChangImportLog:          q.InsWarehouseHaiChangImportLog.replaceDB(db),
		InsWarehouseInout:                      q.InsWarehouseInout.replaceDB(db),
		InsWarehouseInoutApply:                 q.InsWarehouseInoutApply.replaceDB(db),
		InsWarehouseInoutDetailUnique:          q.InsWarehouseInoutDetailUnique.replaceDB(db),
		InsWarehouseInoutDetails:               q.InsWarehouseInoutDetails.replaceDB(db),
		InsWarehouseInoutLog:                   q.InsWarehouseInoutLog.replaceDB(db),
		InsWarehouseInoutReceipt:               q.InsWarehouseInoutReceipt.replaceDB(db),
		InsWarehouseInoutType:                  q.InsWarehouseInoutType.replaceDB(db),
		InsWarehouseInventory:                  q.InsWarehouseInventory.replaceDB(db),
		InsWarehouseInventoryBatch:             q.InsWarehouseInventoryBatch.replaceDB(db),
		InsWarehouseInventoryBatchHistory:      q.InsWarehouseInventoryBatchHistory.replaceDB(db),
		InsWarehouseInventoryDay:               q.InsWarehouseInventoryDay.replaceDB(db),
		InsWarehouseInventoryDetail:            q.InsWarehouseInventoryDetail.replaceDB(db),
		InsWarehouseInventoryUnique:            q.InsWarehouseInventoryUnique.replaceDB(db),
		InsWarehouseSaleLog:                    q.InsWarehouseSaleLog.replaceDB(db),
		InsWxConfig:                            q.InsWxConfig.replaceDB(db),
		OrgUser:                                q.OrgUser.replaceDB(db),
		Organization:                           q.Organization.replaceDB(db),
		SysAuthority:                           q.SysAuthority.replaceDB(db),
		SysAuthority2:                          q.SysAuthority2.replaceDB(db),
		SysAuthority2Item:                      q.SysAuthority2Item.replaceDB(db),
		SysAuthority2User:                      q.SysAuthority2User.replaceDB(db),
		SysUser:                                q.SysUser.replaceDB(db),
		SysUserAuthority:                       q.SysUserAuthority.replaceDB(db),
	}
}

type queryCtx struct {
	BusinessCate                           *businessCateDo
	HyCheckout                             *hyCheckoutDo
	InsActBookDeskConf                     *insActBookDeskConfDo
	InsActBookDeskConfBackup               *insActBookDeskConfBackupDo
	InsActBookDeskConfPackage              *insActBookDeskConfPackageDo
	InsActBookDeskRecord                   *insActBookDeskRecordDo
	InsActBookDeskTable                    *insActBookDeskTableDo
	InsActivityContent                     *insActivityContentDo
	InsAudit                               *insAuditDo
	InsAuditConfig                         *insAuditConfigDo
	InsAuditFlow                           *insAuditFlowDo
	InsAuditFlowDetail                     *insAuditFlowDetailDo
	InsBalanceBulk                         *insBalanceBulkDo
	InsBalanceBulkDetails                  *insBalanceBulkDetailsDo
	InsBalanceLog                          *insBalanceLogDo
	InsBook                                *insBookDo
	InsBrand                               *insBrandDo
	InsBusinessConfig                      *insBusinessConfigDo
	InsBusinessPayment                     *insBusinessPaymentDo
	InsConfHoliday                         *insConfHolidayDo
	InsConfigCenter                        *insConfigCenterDo
	InsConfigCenterDetail                  *insConfigCenterDetailDo
	InsContract                            *insContractDo
	InsContractComment                     *insContractCommentDo
	InsContractFile                        *insContractFileDo
	InsContractSyncLog                     *insContractSyncLogDo
	InsContractTask                        *insContractTaskDo
	InsContractTimeline                    *insContractTimelineDo
	InsCostCard                            *insCostCardDo
	InsCostCardDetail                      *insCostCardDetailDo
	InsCostPriceHistory                    *insCostPriceHistoryDo
	InsCostPriceTotal                      *insCostPriceTotalDo
	InsCoupon                              *insCouponDo
	InsCouponApplyScope                    *insCouponApplyScopeDo
	InsCouponCash                          *insCouponCashDo
	InsCouponDiscount                      *insCouponDiscountDo
	InsCouponProduct                       *insCouponProductDo
	InsCouponSendDetail                    *insCouponSendDetailDo
	InsCouponSendRule                      *insCouponSendRuleDo
	InsCouponValidWeekDay                  *insCouponValidWeekDayDo
	InsCustomPayment                       *insCustomPaymentDo
	InsDeposit                             *insDepositDo
	InsDepositInoutReportDay               *insDepositInoutReportDayDo
	InsDepositLog                          *insDepositLogDo
	InsDepositRecord                       *insDepositRecordDo
	InsDepositReportData                   *insDepositReportDataDo
	InsDepositSmsRecord                    *insDepositSmsRecordDo
	InsDepositVipReport                    *insDepositVipReportDo
	InsDesk                                *insDeskDo
	InsDeskArea                            *insDeskAreaDo
	InsDeskCategory                        *insDeskCategoryDo
	InsDeskCategoryMinConsumption          *insDeskCategoryMinConsumptionDo
	InsDeskMinConsumption                  *insDeskMinConsumptionDo
	InsDeskOpen                            *insDeskOpenDo
	InsDeskOpenLog                         *insDeskOpenLogDo
	InsDeskPeriodMinConsumption            *insDeskPeriodMinConsumptionDo
	InsDeskPrintLog                        *insDeskPrintLogDo
	InsDeskStatus                          *insDeskStatusDo
	InsDeskStatusLog                       *insDeskStatusLogDo
	InsEmptyBottleReport                   *insEmptyBottleReportDo
	InsEventTrackingLogs                   *insEventTrackingLogsDo
	InsEventsLog                           *insEventsLogDo
	InsExcelTemplate                       *insExcelTemplateDo
	InsExtConf                             *insExtConfDo
	InsExtKezeeAddition                    *insExtKezeeAdditionDo
	InsExtKezeeOpenDesk                    *insExtKezeeOpenDeskDo
	InsExtKezeeOrder                       *insExtKezeeOrderDo
	InsExtKezeeOrderItem                   *insExtKezeeOrderItemDo
	InsExtKezeeOrderPay                    *insExtKezeeOrderPayDo
	InsExtStoreSales                       *insExtStoreSalesDo
	InsExtStoreTicket                      *insExtStoreTicketDo
	InsGiftLog                             *insGiftLogDo
	InsGiftLogDetail                       *insGiftLogDetailDo
	InsGiftQuotaApplyLog                   *insGiftQuotaApplyLogDo
	InsGiftQuotaAssignLogDetail            *insGiftQuotaAssignLogDetailDo
	InsGiftQuotaAssignUser                 *insGiftQuotaAssignUserDo
	InsGiftQuotaMonth                      *insGiftQuotaMonthDo
	InsGiftQuotaMonthNow                   *insGiftQuotaMonthNowDo
	InsGiftRuleDetail                      *insGiftRuleDetailDo
	InsGiftRuleMember                      *insGiftRuleMemberDo
	InsGiftRuleProduct                     *insGiftRuleProductDo
	InsGiftRules                           *insGiftRulesDo
	InsImportLog                           *insImportLogDo
	InsInventoryMaterialFlow               *insInventoryMaterialFlowDo
	InsInventoryProductFlow                *insInventoryProductFlowDo
	InsLangMap                             *insLangMapDo
	InsLangTranslation                     *insLangTranslationDo
	InsMaterial                            *insMaterialDo
	InsMaterialCategory                    *insMaterialCategoryDo
	InsMaterialHistory                     *insMaterialHistoryDo
	InsMaterialStandard                    *insMaterialStandardDo
	InsMaterialStandardRel                 *insMaterialStandardRelDo
	InsMaterialSupplier                    *insMaterialSupplierDo
	InsMicroOrder                          *insMicroOrderDo
	InsNotes                               *insNotesDo
	InsNotesDetails                        *insNotesDetailsDo
	InsOpenDeskSnapshot                    *insOpenDeskSnapshotDo
	InsOrderAdjustment                     *insOrderAdjustmentDo
	InsOrderAdjustmentDetail               *insOrderAdjustmentDetailDo
	InsOrderBill                           *insOrderBillDo
	InsOrderDiscountDetail                 *insOrderDiscountDetailDo
	InsOrderInfo                           *insOrderInfoDo
	InsOrderInfoDetails                    *insOrderInfoDetailsDo
	InsOrderInfoPackage                    *insOrderInfoPackageDo
	InsOrderPayDetail                      *insOrderPayDetailDo
	InsOrderReturn                         *insOrderReturnDo
	InsOrderReturnDetails                  *insOrderReturnDetailsDo
	InsOrderShoppingCart                   *insOrderShoppingCartDo
	InsOrderShoppingCartDetail             *insOrderShoppingCartDetailDo
	InsPayConfig                           *insPayConfigDo
	InsPayment                             *insPaymentDo
	InsProduct                             *insProductDo
	InsProductActivity                     *insProductActivityDo
	InsProductActivityDetail               *insProductActivityDetailDo
	InsProductActivityFormula              *insProductActivityFormulaDo
	InsProductCategory                     *insProductCategoryDo
	InsProductDetails                      *insProductDetailsDo
	InsProductPackage                      *insProductPackageDo
	InsProductPackageDetails               *insProductPackageDetailsDo
	InsProductPackageItem                  *insProductPackageItemDo
	InsProductPackageItemDetails           *insProductPackageItemDetailsDo
	InsProductPackageItemTemplate          *insProductPackageItemTemplateDo
	InsProductPackageItemTemplateBind      *insProductPackageItemTemplateBindDo
	InsProductPackageItemTemplateDetails   *insProductPackageItemTemplateDetailsDo
	InsProductPromotion                    *insProductPromotionDo
	InsProductPromotionApplicable          *insProductPromotionApplicableDo
	InsProductPromotionCategory            *insProductPromotionCategoryDo
	InsProductPromotionExclusion           *insProductPromotionExclusionDo
	InsProductPromotionProduct             *insProductPromotionProductDo
	InsProductPromotionTime                *insProductPromotionTimeDo
	InsProductSoldOut                      *insProductSoldOutDo
	InsProductStall                        *insProductStallDo
	InsProductStore                        *insProductStoreDo
	InsProductSupplier                     *insProductSupplierDo
	InsQueueChannel                        *insQueueChannelDo
	InsQueueChannelResource                *insQueueChannelResourceDo
	InsQueueItem                           *insQueueItemDo
	InsQueueItemEventHistory               *insQueueItemEventHistoryDo
	InsRechargeCard                        *insRechargeCardDo
	InsRechargeRule                        *insRechargeRuleDo
	InsReconciliationAdjustment            *insReconciliationAdjustmentDo
	InsReconciliationDataSource            *insReconciliationDataSourceDo
	InsReconciliationResult                *insReconciliationResultDo
	InsReconciliationRule                  *insReconciliationRuleDo
	InsReconciliationTask                  *insReconciliationTaskDo
	InsReport                              *insReportDo
	InsReportCategory                      *insReportCategoryDo
	InsReportDataSalesValue                *insReportDataSalesValueDo
	InsReportField                         *insReportFieldDo
	InsReportIntermediateResult            *insReportIntermediateResultDo
	InsReportIntermediateResultDetails     *insReportIntermediateResultDetailsDo
	InsReportRuleSalesShare                *insReportRuleSalesShareDo
	InsReportRuleSalesShareOrg             *insReportRuleSalesShareOrgDo
	InsReportRuleSalesShareProduct         *insReportRuleSalesShareProductDo
	InsReportRuleSalesShareProductCategory *insReportRuleSalesShareProductCategoryDo
	InsReportRuleSalesShareRecharge        *insReportRuleSalesShareRechargeDo
	InsReportRuleSalesShareStaff           *insReportRuleSalesShareStaffDo
	InsReportRuleSalesShareTimeFrame       *insReportRuleSalesShareTimeFrameDo
	InsReportSalesShare                    *insReportSalesShareDo
	InsReportStatisticalRule               *insReportStatisticalRuleDo
	InsReportStatisticalRuleDetail         *insReportStatisticalRuleDetailDo
	InsSalerCode                           *insSalerCodeDo
	InsSalerStore                          *insSalerStoreDo
	InsScanFlow                            *insScanFlowDo
	InsServiceFee                          *insServiceFeeDo
	InsServiceFeeApplicable                *insServiceFeeApplicableDo
	InsServiceFeeCategory                  *insServiceFeeCategoryDo
	InsServiceFeeDesk                      *insServiceFeeDeskDo
	InsServiceFeeDeskCurrent               *insServiceFeeDeskCurrentDo
	InsServiceFeeExclusion                 *insServiceFeeExclusionDo
	InsServiceFeeProduct                   *insServiceFeeProductDo
	InsServiceFeeTime                      *insServiceFeeTimeDo
	InsShipment                            *insShipmentDo
	InsSqlReport                           *insSqlReportDo
	InsSqlReportCate                       *insSqlReportCateDo
	InsSqlRule                             *insSqlRuleDo
	InsSqlRuleHasReport                    *insSqlRuleHasReportDo
	InsSqlRuleHasUser                      *insSqlRuleHasUserDo
	InsStore                               *insStoreDo
	InsStoreBusinessConfig                 *insStoreBusinessConfigDo
	InsStoreConsumeSnapshot                *insStoreConsumeSnapshotDo
	InsStoreManualOp                       *insStoreManualOpDo
	InsStoreRevenueReport                  *insStoreRevenueReportDo
	InsStoreTerminal                       *insStoreTerminalDo
	InsStoreTicketConfig                   *insStoreTicketConfigDo
	InsStoreWarehouseCostPrice             *insStoreWarehouseCostPriceDo
	InsSundriesMaterial                    *insSundriesMaterialDo
	InsSundriesMaterialHistory             *insSundriesMaterialHistoryDo
	InsSundriesMaterialSupplier            *insSundriesMaterialSupplierDo
	InsSupplier                            *insSupplierDo
	InsSysCounter                          *insSysCounterDo
	InsSysDevPrinter                       *insSysDevPrinterDo
	InsSysDevPrinterConfig                 *insSysDevPrinterConfigDo
	InsSysPrintTemplate                    *insSysPrintTemplateDo
	InsSysStorePrintTemplate               *insSysStorePrintTemplateDo
	InsTicketOfflineRecord                 *insTicketOfflineRecordDo
	InsTrade                               *insTradeDo
	InsTradeAccount                        *insTradeAccountDo
	InsTradeAccountConfig                  *insTradeAccountConfigDo
	InsTradeAccountConfigStore             *insTradeAccountConfigStoreDo
	InsTradeAccountDetails                 *insTradeAccountDetailsDo
	InsTradeCoupon                         *insTradeCouponDo
	InsTradeCustom                         *insTradeCustomDo
	InsTradePay                            *insTradePayDo
	InsTradeRefunds                        *insTradeRefundsDo
	InsTradeReverseLog                     *insTradeReverseLogDo
	InsTransferExecution                   *insTransferExecutionDo
	InsTransferExecutionStep               *insTransferExecutionStepDo
	InsTransferProcess                     *insTransferProcessDo
	InsTransferProcessStep                 *insTransferProcessStepDo
	InsUserAuthorityStore                  *insUserAuthorityStoreDo
	InsUserRegister                        *insUserRegisterDo
	InsVerifyCode                          *insVerifyCodeDo
	InsVipCard                             *insVipCardDo
	InsVipLevel                            *insVipLevelDo
	InsVipMember                           *insVipMemberDo
	InsVipScoreLog                         *insVipScoreLogDo
	InsWarehouse                           *insWarehouseDo
	InsWarehouseAdmin                      *insWarehouseAdminDo
	InsWarehouseCheckDay                   *insWarehouseCheckDayDo
	InsWarehouseCheckDayDetail             *insWarehouseCheckDayDetailDo
	InsWarehouseEmpties                    *insWarehouseEmptiesDo
	InsWarehouseEmptiesDetail              *insWarehouseEmptiesDetailDo
	InsWarehouseHaiChang                   *insWarehouseHaiChangDo
	InsWarehouseHaiChangDetails            *insWarehouseHaiChangDetailsDo
	InsWarehouseHaiChangImportLog          *insWarehouseHaiChangImportLogDo
	InsWarehouseInout                      *insWarehouseInoutDo
	InsWarehouseInoutApply                 *insWarehouseInoutApplyDo
	InsWarehouseInoutDetailUnique          *insWarehouseInoutDetailUniqueDo
	InsWarehouseInoutDetails               *insWarehouseInoutDetailsDo
	InsWarehouseInoutLog                   *insWarehouseInoutLogDo
	InsWarehouseInoutReceipt               *insWarehouseInoutReceiptDo
	InsWarehouseInoutType                  *insWarehouseInoutTypeDo
	InsWarehouseInventory                  *insWarehouseInventoryDo
	InsWarehouseInventoryBatch             *insWarehouseInventoryBatchDo
	InsWarehouseInventoryBatchHistory      *insWarehouseInventoryBatchHistoryDo
	InsWarehouseInventoryDay               *insWarehouseInventoryDayDo
	InsWarehouseInventoryDetail            *insWarehouseInventoryDetailDo
	InsWarehouseInventoryUnique            *insWarehouseInventoryUniqueDo
	InsWarehouseSaleLog                    *insWarehouseSaleLogDo
	InsWxConfig                            *insWxConfigDo
	OrgUser                                *orgUserDo
	Organization                           *organizationDo
	SysAuthority                           *sysAuthorityDo
	SysAuthority2                          *sysAuthority2Do
	SysAuthority2Item                      *sysAuthority2ItemDo
	SysAuthority2User                      *sysAuthority2UserDo
	SysUser                                *sysUserDo
	SysUserAuthority                       *sysUserAuthorityDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		BusinessCate:                           q.BusinessCate.WithContext(ctx),
		HyCheckout:                             q.HyCheckout.WithContext(ctx),
		InsActBookDeskConf:                     q.InsActBookDeskConf.WithContext(ctx),
		InsActBookDeskConfBackup:               q.InsActBookDeskConfBackup.WithContext(ctx),
		InsActBookDeskConfPackage:              q.InsActBookDeskConfPackage.WithContext(ctx),
		InsActBookDeskRecord:                   q.InsActBookDeskRecord.WithContext(ctx),
		InsActBookDeskTable:                    q.InsActBookDeskTable.WithContext(ctx),
		InsActivityContent:                     q.InsActivityContent.WithContext(ctx),
		InsAudit:                               q.InsAudit.WithContext(ctx),
		InsAuditConfig:                         q.InsAuditConfig.WithContext(ctx),
		InsAuditFlow:                           q.InsAuditFlow.WithContext(ctx),
		InsAuditFlowDetail:                     q.InsAuditFlowDetail.WithContext(ctx),
		InsBalanceBulk:                         q.InsBalanceBulk.WithContext(ctx),
		InsBalanceBulkDetails:                  q.InsBalanceBulkDetails.WithContext(ctx),
		InsBalanceLog:                          q.InsBalanceLog.WithContext(ctx),
		InsBook:                                q.InsBook.WithContext(ctx),
		InsBrand:                               q.InsBrand.WithContext(ctx),
		InsBusinessConfig:                      q.InsBusinessConfig.WithContext(ctx),
		InsBusinessPayment:                     q.InsBusinessPayment.WithContext(ctx),
		InsConfHoliday:                         q.InsConfHoliday.WithContext(ctx),
		InsConfigCenter:                        q.InsConfigCenter.WithContext(ctx),
		InsConfigCenterDetail:                  q.InsConfigCenterDetail.WithContext(ctx),
		InsContract:                            q.InsContract.WithContext(ctx),
		InsContractComment:                     q.InsContractComment.WithContext(ctx),
		InsContractFile:                        q.InsContractFile.WithContext(ctx),
		InsContractSyncLog:                     q.InsContractSyncLog.WithContext(ctx),
		InsContractTask:                        q.InsContractTask.WithContext(ctx),
		InsContractTimeline:                    q.InsContractTimeline.WithContext(ctx),
		InsCostCard:                            q.InsCostCard.WithContext(ctx),
		InsCostCardDetail:                      q.InsCostCardDetail.WithContext(ctx),
		InsCostPriceHistory:                    q.InsCostPriceHistory.WithContext(ctx),
		InsCostPriceTotal:                      q.InsCostPriceTotal.WithContext(ctx),
		InsCoupon:                              q.InsCoupon.WithContext(ctx),
		InsCouponApplyScope:                    q.InsCouponApplyScope.WithContext(ctx),
		InsCouponCash:                          q.InsCouponCash.WithContext(ctx),
		InsCouponDiscount:                      q.InsCouponDiscount.WithContext(ctx),
		InsCouponProduct:                       q.InsCouponProduct.WithContext(ctx),
		InsCouponSendDetail:                    q.InsCouponSendDetail.WithContext(ctx),
		InsCouponSendRule:                      q.InsCouponSendRule.WithContext(ctx),
		InsCouponValidWeekDay:                  q.InsCouponValidWeekDay.WithContext(ctx),
		InsCustomPayment:                       q.InsCustomPayment.WithContext(ctx),
		InsDeposit:                             q.InsDeposit.WithContext(ctx),
		InsDepositInoutReportDay:               q.InsDepositInoutReportDay.WithContext(ctx),
		InsDepositLog:                          q.InsDepositLog.WithContext(ctx),
		InsDepositRecord:                       q.InsDepositRecord.WithContext(ctx),
		InsDepositReportData:                   q.InsDepositReportData.WithContext(ctx),
		InsDepositSmsRecord:                    q.InsDepositSmsRecord.WithContext(ctx),
		InsDepositVipReport:                    q.InsDepositVipReport.WithContext(ctx),
		InsDesk:                                q.InsDesk.WithContext(ctx),
		InsDeskArea:                            q.InsDeskArea.WithContext(ctx),
		InsDeskCategory:                        q.InsDeskCategory.WithContext(ctx),
		InsDeskCategoryMinConsumption:          q.InsDeskCategoryMinConsumption.WithContext(ctx),
		InsDeskMinConsumption:                  q.InsDeskMinConsumption.WithContext(ctx),
		InsDeskOpen:                            q.InsDeskOpen.WithContext(ctx),
		InsDeskOpenLog:                         q.InsDeskOpenLog.WithContext(ctx),
		InsDeskPeriodMinConsumption:            q.InsDeskPeriodMinConsumption.WithContext(ctx),
		InsDeskPrintLog:                        q.InsDeskPrintLog.WithContext(ctx),
		InsDeskStatus:                          q.InsDeskStatus.WithContext(ctx),
		InsDeskStatusLog:                       q.InsDeskStatusLog.WithContext(ctx),
		InsEmptyBottleReport:                   q.InsEmptyBottleReport.WithContext(ctx),
		InsEventTrackingLogs:                   q.InsEventTrackingLogs.WithContext(ctx),
		InsEventsLog:                           q.InsEventsLog.WithContext(ctx),
		InsExcelTemplate:                       q.InsExcelTemplate.WithContext(ctx),
		InsExtConf:                             q.InsExtConf.WithContext(ctx),
		InsExtKezeeAddition:                    q.InsExtKezeeAddition.WithContext(ctx),
		InsExtKezeeOpenDesk:                    q.InsExtKezeeOpenDesk.WithContext(ctx),
		InsExtKezeeOrder:                       q.InsExtKezeeOrder.WithContext(ctx),
		InsExtKezeeOrderItem:                   q.InsExtKezeeOrderItem.WithContext(ctx),
		InsExtKezeeOrderPay:                    q.InsExtKezeeOrderPay.WithContext(ctx),
		InsExtStoreSales:                       q.InsExtStoreSales.WithContext(ctx),
		InsExtStoreTicket:                      q.InsExtStoreTicket.WithContext(ctx),
		InsGiftLog:                             q.InsGiftLog.WithContext(ctx),
		InsGiftLogDetail:                       q.InsGiftLogDetail.WithContext(ctx),
		InsGiftQuotaApplyLog:                   q.InsGiftQuotaApplyLog.WithContext(ctx),
		InsGiftQuotaAssignLogDetail:            q.InsGiftQuotaAssignLogDetail.WithContext(ctx),
		InsGiftQuotaAssignUser:                 q.InsGiftQuotaAssignUser.WithContext(ctx),
		InsGiftQuotaMonth:                      q.InsGiftQuotaMonth.WithContext(ctx),
		InsGiftQuotaMonthNow:                   q.InsGiftQuotaMonthNow.WithContext(ctx),
		InsGiftRuleDetail:                      q.InsGiftRuleDetail.WithContext(ctx),
		InsGiftRuleMember:                      q.InsGiftRuleMember.WithContext(ctx),
		InsGiftRuleProduct:                     q.InsGiftRuleProduct.WithContext(ctx),
		InsGiftRules:                           q.InsGiftRules.WithContext(ctx),
		InsImportLog:                           q.InsImportLog.WithContext(ctx),
		InsInventoryMaterialFlow:               q.InsInventoryMaterialFlow.WithContext(ctx),
		InsInventoryProductFlow:                q.InsInventoryProductFlow.WithContext(ctx),
		InsLangMap:                             q.InsLangMap.WithContext(ctx),
		InsLangTranslation:                     q.InsLangTranslation.WithContext(ctx),
		InsMaterial:                            q.InsMaterial.WithContext(ctx),
		InsMaterialCategory:                    q.InsMaterialCategory.WithContext(ctx),
		InsMaterialHistory:                     q.InsMaterialHistory.WithContext(ctx),
		InsMaterialStandard:                    q.InsMaterialStandard.WithContext(ctx),
		InsMaterialStandardRel:                 q.InsMaterialStandardRel.WithContext(ctx),
		InsMaterialSupplier:                    q.InsMaterialSupplier.WithContext(ctx),
		InsMicroOrder:                          q.InsMicroOrder.WithContext(ctx),
		InsNotes:                               q.InsNotes.WithContext(ctx),
		InsNotesDetails:                        q.InsNotesDetails.WithContext(ctx),
		InsOpenDeskSnapshot:                    q.InsOpenDeskSnapshot.WithContext(ctx),
		InsOrderAdjustment:                     q.InsOrderAdjustment.WithContext(ctx),
		InsOrderAdjustmentDetail:               q.InsOrderAdjustmentDetail.WithContext(ctx),
		InsOrderBill:                           q.InsOrderBill.WithContext(ctx),
		InsOrderDiscountDetail:                 q.InsOrderDiscountDetail.WithContext(ctx),
		InsOrderInfo:                           q.InsOrderInfo.WithContext(ctx),
		InsOrderInfoDetails:                    q.InsOrderInfoDetails.WithContext(ctx),
		InsOrderInfoPackage:                    q.InsOrderInfoPackage.WithContext(ctx),
		InsOrderPayDetail:                      q.InsOrderPayDetail.WithContext(ctx),
		InsOrderReturn:                         q.InsOrderReturn.WithContext(ctx),
		InsOrderReturnDetails:                  q.InsOrderReturnDetails.WithContext(ctx),
		InsOrderShoppingCart:                   q.InsOrderShoppingCart.WithContext(ctx),
		InsOrderShoppingCartDetail:             q.InsOrderShoppingCartDetail.WithContext(ctx),
		InsPayConfig:                           q.InsPayConfig.WithContext(ctx),
		InsPayment:                             q.InsPayment.WithContext(ctx),
		InsProduct:                             q.InsProduct.WithContext(ctx),
		InsProductActivity:                     q.InsProductActivity.WithContext(ctx),
		InsProductActivityDetail:               q.InsProductActivityDetail.WithContext(ctx),
		InsProductActivityFormula:              q.InsProductActivityFormula.WithContext(ctx),
		InsProductCategory:                     q.InsProductCategory.WithContext(ctx),
		InsProductDetails:                      q.InsProductDetails.WithContext(ctx),
		InsProductPackage:                      q.InsProductPackage.WithContext(ctx),
		InsProductPackageDetails:               q.InsProductPackageDetails.WithContext(ctx),
		InsProductPackageItem:                  q.InsProductPackageItem.WithContext(ctx),
		InsProductPackageItemDetails:           q.InsProductPackageItemDetails.WithContext(ctx),
		InsProductPackageItemTemplate:          q.InsProductPackageItemTemplate.WithContext(ctx),
		InsProductPackageItemTemplateBind:      q.InsProductPackageItemTemplateBind.WithContext(ctx),
		InsProductPackageItemTemplateDetails:   q.InsProductPackageItemTemplateDetails.WithContext(ctx),
		InsProductPromotion:                    q.InsProductPromotion.WithContext(ctx),
		InsProductPromotionApplicable:          q.InsProductPromotionApplicable.WithContext(ctx),
		InsProductPromotionCategory:            q.InsProductPromotionCategory.WithContext(ctx),
		InsProductPromotionExclusion:           q.InsProductPromotionExclusion.WithContext(ctx),
		InsProductPromotionProduct:             q.InsProductPromotionProduct.WithContext(ctx),
		InsProductPromotionTime:                q.InsProductPromotionTime.WithContext(ctx),
		InsProductSoldOut:                      q.InsProductSoldOut.WithContext(ctx),
		InsProductStall:                        q.InsProductStall.WithContext(ctx),
		InsProductStore:                        q.InsProductStore.WithContext(ctx),
		InsProductSupplier:                     q.InsProductSupplier.WithContext(ctx),
		InsQueueChannel:                        q.InsQueueChannel.WithContext(ctx),
		InsQueueChannelResource:                q.InsQueueChannelResource.WithContext(ctx),
		InsQueueItem:                           q.InsQueueItem.WithContext(ctx),
		InsQueueItemEventHistory:               q.InsQueueItemEventHistory.WithContext(ctx),
		InsRechargeCard:                        q.InsRechargeCard.WithContext(ctx),
		InsRechargeRule:                        q.InsRechargeRule.WithContext(ctx),
		InsReconciliationAdjustment:            q.InsReconciliationAdjustment.WithContext(ctx),
		InsReconciliationDataSource:            q.InsReconciliationDataSource.WithContext(ctx),
		InsReconciliationResult:                q.InsReconciliationResult.WithContext(ctx),
		InsReconciliationRule:                  q.InsReconciliationRule.WithContext(ctx),
		InsReconciliationTask:                  q.InsReconciliationTask.WithContext(ctx),
		InsReport:                              q.InsReport.WithContext(ctx),
		InsReportCategory:                      q.InsReportCategory.WithContext(ctx),
		InsReportDataSalesValue:                q.InsReportDataSalesValue.WithContext(ctx),
		InsReportField:                         q.InsReportField.WithContext(ctx),
		InsReportIntermediateResult:            q.InsReportIntermediateResult.WithContext(ctx),
		InsReportIntermediateResultDetails:     q.InsReportIntermediateResultDetails.WithContext(ctx),
		InsReportRuleSalesShare:                q.InsReportRuleSalesShare.WithContext(ctx),
		InsReportRuleSalesShareOrg:             q.InsReportRuleSalesShareOrg.WithContext(ctx),
		InsReportRuleSalesShareProduct:         q.InsReportRuleSalesShareProduct.WithContext(ctx),
		InsReportRuleSalesShareProductCategory: q.InsReportRuleSalesShareProductCategory.WithContext(ctx),
		InsReportRuleSalesShareRecharge:        q.InsReportRuleSalesShareRecharge.WithContext(ctx),
		InsReportRuleSalesShareStaff:           q.InsReportRuleSalesShareStaff.WithContext(ctx),
		InsReportRuleSalesShareTimeFrame:       q.InsReportRuleSalesShareTimeFrame.WithContext(ctx),
		InsReportSalesShare:                    q.InsReportSalesShare.WithContext(ctx),
		InsReportStatisticalRule:               q.InsReportStatisticalRule.WithContext(ctx),
		InsReportStatisticalRuleDetail:         q.InsReportStatisticalRuleDetail.WithContext(ctx),
		InsSalerCode:                           q.InsSalerCode.WithContext(ctx),
		InsSalerStore:                          q.InsSalerStore.WithContext(ctx),
		InsScanFlow:                            q.InsScanFlow.WithContext(ctx),
		InsServiceFee:                          q.InsServiceFee.WithContext(ctx),
		InsServiceFeeApplicable:                q.InsServiceFeeApplicable.WithContext(ctx),
		InsServiceFeeCategory:                  q.InsServiceFeeCategory.WithContext(ctx),
		InsServiceFeeDesk:                      q.InsServiceFeeDesk.WithContext(ctx),
		InsServiceFeeDeskCurrent:               q.InsServiceFeeDeskCurrent.WithContext(ctx),
		InsServiceFeeExclusion:                 q.InsServiceFeeExclusion.WithContext(ctx),
		InsServiceFeeProduct:                   q.InsServiceFeeProduct.WithContext(ctx),
		InsServiceFeeTime:                      q.InsServiceFeeTime.WithContext(ctx),
		InsShipment:                            q.InsShipment.WithContext(ctx),
		InsSqlReport:                           q.InsSqlReport.WithContext(ctx),
		InsSqlReportCate:                       q.InsSqlReportCate.WithContext(ctx),
		InsSqlRule:                             q.InsSqlRule.WithContext(ctx),
		InsSqlRuleHasReport:                    q.InsSqlRuleHasReport.WithContext(ctx),
		InsSqlRuleHasUser:                      q.InsSqlRuleHasUser.WithContext(ctx),
		InsStore:                               q.InsStore.WithContext(ctx),
		InsStoreBusinessConfig:                 q.InsStoreBusinessConfig.WithContext(ctx),
		InsStoreConsumeSnapshot:                q.InsStoreConsumeSnapshot.WithContext(ctx),
		InsStoreManualOp:                       q.InsStoreManualOp.WithContext(ctx),
		InsStoreRevenueReport:                  q.InsStoreRevenueReport.WithContext(ctx),
		InsStoreTerminal:                       q.InsStoreTerminal.WithContext(ctx),
		InsStoreTicketConfig:                   q.InsStoreTicketConfig.WithContext(ctx),
		InsStoreWarehouseCostPrice:             q.InsStoreWarehouseCostPrice.WithContext(ctx),
		InsSundriesMaterial:                    q.InsSundriesMaterial.WithContext(ctx),
		InsSundriesMaterialHistory:             q.InsSundriesMaterialHistory.WithContext(ctx),
		InsSundriesMaterialSupplier:            q.InsSundriesMaterialSupplier.WithContext(ctx),
		InsSupplier:                            q.InsSupplier.WithContext(ctx),
		InsSysCounter:                          q.InsSysCounter.WithContext(ctx),
		InsSysDevPrinter:                       q.InsSysDevPrinter.WithContext(ctx),
		InsSysDevPrinterConfig:                 q.InsSysDevPrinterConfig.WithContext(ctx),
		InsSysPrintTemplate:                    q.InsSysPrintTemplate.WithContext(ctx),
		InsSysStorePrintTemplate:               q.InsSysStorePrintTemplate.WithContext(ctx),
		InsTicketOfflineRecord:                 q.InsTicketOfflineRecord.WithContext(ctx),
		InsTrade:                               q.InsTrade.WithContext(ctx),
		InsTradeAccount:                        q.InsTradeAccount.WithContext(ctx),
		InsTradeAccountConfig:                  q.InsTradeAccountConfig.WithContext(ctx),
		InsTradeAccountConfigStore:             q.InsTradeAccountConfigStore.WithContext(ctx),
		InsTradeAccountDetails:                 q.InsTradeAccountDetails.WithContext(ctx),
		InsTradeCoupon:                         q.InsTradeCoupon.WithContext(ctx),
		InsTradeCustom:                         q.InsTradeCustom.WithContext(ctx),
		InsTradePay:                            q.InsTradePay.WithContext(ctx),
		InsTradeRefunds:                        q.InsTradeRefunds.WithContext(ctx),
		InsTradeReverseLog:                     q.InsTradeReverseLog.WithContext(ctx),
		InsTransferExecution:                   q.InsTransferExecution.WithContext(ctx),
		InsTransferExecutionStep:               q.InsTransferExecutionStep.WithContext(ctx),
		InsTransferProcess:                     q.InsTransferProcess.WithContext(ctx),
		InsTransferProcessStep:                 q.InsTransferProcessStep.WithContext(ctx),
		InsUserAuthorityStore:                  q.InsUserAuthorityStore.WithContext(ctx),
		InsUserRegister:                        q.InsUserRegister.WithContext(ctx),
		InsVerifyCode:                          q.InsVerifyCode.WithContext(ctx),
		InsVipCard:                             q.InsVipCard.WithContext(ctx),
		InsVipLevel:                            q.InsVipLevel.WithContext(ctx),
		InsVipMember:                           q.InsVipMember.WithContext(ctx),
		InsVipScoreLog:                         q.InsVipScoreLog.WithContext(ctx),
		InsWarehouse:                           q.InsWarehouse.WithContext(ctx),
		InsWarehouseAdmin:                      q.InsWarehouseAdmin.WithContext(ctx),
		InsWarehouseCheckDay:                   q.InsWarehouseCheckDay.WithContext(ctx),
		InsWarehouseCheckDayDetail:             q.InsWarehouseCheckDayDetail.WithContext(ctx),
		InsWarehouseEmpties:                    q.InsWarehouseEmpties.WithContext(ctx),
		InsWarehouseEmptiesDetail:              q.InsWarehouseEmptiesDetail.WithContext(ctx),
		InsWarehouseHaiChang:                   q.InsWarehouseHaiChang.WithContext(ctx),
		InsWarehouseHaiChangDetails:            q.InsWarehouseHaiChangDetails.WithContext(ctx),
		InsWarehouseHaiChangImportLog:          q.InsWarehouseHaiChangImportLog.WithContext(ctx),
		InsWarehouseInout:                      q.InsWarehouseInout.WithContext(ctx),
		InsWarehouseInoutApply:                 q.InsWarehouseInoutApply.WithContext(ctx),
		InsWarehouseInoutDetailUnique:          q.InsWarehouseInoutDetailUnique.WithContext(ctx),
		InsWarehouseInoutDetails:               q.InsWarehouseInoutDetails.WithContext(ctx),
		InsWarehouseInoutLog:                   q.InsWarehouseInoutLog.WithContext(ctx),
		InsWarehouseInoutReceipt:               q.InsWarehouseInoutReceipt.WithContext(ctx),
		InsWarehouseInoutType:                  q.InsWarehouseInoutType.WithContext(ctx),
		InsWarehouseInventory:                  q.InsWarehouseInventory.WithContext(ctx),
		InsWarehouseInventoryBatch:             q.InsWarehouseInventoryBatch.WithContext(ctx),
		InsWarehouseInventoryBatchHistory:      q.InsWarehouseInventoryBatchHistory.WithContext(ctx),
		InsWarehouseInventoryDay:               q.InsWarehouseInventoryDay.WithContext(ctx),
		InsWarehouseInventoryDetail:            q.InsWarehouseInventoryDetail.WithContext(ctx),
		InsWarehouseInventoryUnique:            q.InsWarehouseInventoryUnique.WithContext(ctx),
		InsWarehouseSaleLog:                    q.InsWarehouseSaleLog.WithContext(ctx),
		InsWxConfig:                            q.InsWxConfig.WithContext(ctx),
		OrgUser:                                q.OrgUser.WithContext(ctx),
		Organization:                           q.Organization.WithContext(ctx),
		SysAuthority:                           q.SysAuthority.WithContext(ctx),
		SysAuthority2:                          q.SysAuthority2.WithContext(ctx),
		SysAuthority2Item:                      q.SysAuthority2Item.WithContext(ctx),
		SysAuthority2User:                      q.SysAuthority2User.WithContext(ctx),
		SysUser:                                q.SysUser.WithContext(ctx),
		SysUserAuthority:                       q.SysUserAuthority.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
