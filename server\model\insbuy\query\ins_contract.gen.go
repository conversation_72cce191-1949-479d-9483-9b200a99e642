// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsContract(db *gorm.DB, opts ...gen.DOOption) insContract {
	_insContract := insContract{}

	_insContract.insContractDo.UseDB(db, opts...)
	_insContract.insContractDo.UseModel(&insbuy.InsContract{})

	tableName := _insContract.insContractDo.TableName()
	_insContract.ALL = field.NewAsterisk(tableName)
	_insContract.ID = field.NewUint(tableName, "id")
	_insContract.CreatedAt = field.NewTime(tableName, "created_at")
	_insContract.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insContract.DeletedAt = field.NewField(tableName, "deleted_at")
	_insContract.ApprovalCode = field.NewString(tableName, "approval_code")
	_insContract.ApprovalName = field.NewString(tableName, "approval_name")
	_insContract.InstanceCode = field.NewString(tableName, "instance_code")
	_insContract.SerialNumber = field.NewString(tableName, "serial_number")
	_insContract.Uuid = field.NewString(tableName, "uuid")
	_insContract.Status = field.NewString(tableName, "status")
	_insContract.Reverted = field.NewBool(tableName, "reverted")
	_insContract.StartTime = field.NewTime(tableName, "start_time")
	_insContract.EndTime = field.NewTime(tableName, "end_time")
	_insContract.UserId = field.NewString(tableName, "user_id")
	_insContract.OpenId = field.NewString(tableName, "open_id")
	_insContract.DepartmentId = field.NewString(tableName, "department_id")
	_insContract.Form = field.NewField(tableName, "form")
	_insContract.ContractType = field.NewString(tableName, "contract_type")
	_insContract.ContractTitle = field.NewString(tableName, "contract_title")
	_insContract.ContractAmount = field.NewFloat64(tableName, "contract_amount")
	_insContract.Currency = field.NewString(tableName, "currency")
	_insContract.Comments = insContractHasManyComments{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Comments", "insbuy.InsContractComment"),
	}

	_insContract.Tasks = insContractHasManyTasks{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Tasks", "insbuy.InsContractTask"),
	}

	_insContract.Timeline = insContractHasManyTimeline{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Timeline", "insbuy.InsContractTimeline"),
	}

	_insContract.Files = insContractHasManyFiles{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Files", "insbuy.InsContractFile"),
	}

	_insContract.fillFieldMap()

	return _insContract
}

type insContract struct {
	insContractDo

	ALL            field.Asterisk
	ID             field.Uint
	CreatedAt      field.Time
	UpdatedAt      field.Time
	DeletedAt      field.Field
	ApprovalCode   field.String
	ApprovalName   field.String
	InstanceCode   field.String
	SerialNumber   field.String
	Uuid           field.String
	Status         field.String
	Reverted       field.Bool
	StartTime      field.Time
	EndTime        field.Time
	UserId         field.String
	OpenId         field.String
	DepartmentId   field.String
	Form           field.Field
	ContractType   field.String
	ContractTitle  field.String
	ContractAmount field.Float64
	Currency       field.String
	Comments       insContractHasManyComments

	Tasks insContractHasManyTasks

	Timeline insContractHasManyTimeline

	Files insContractHasManyFiles

	fieldMap map[string]field.Expr
}

func (i insContract) Table(newTableName string) *insContract {
	i.insContractDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insContract) As(alias string) *insContract {
	i.insContractDo.DO = *(i.insContractDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insContract) updateTableName(table string) *insContract {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ApprovalCode = field.NewString(table, "approval_code")
	i.ApprovalName = field.NewString(table, "approval_name")
	i.InstanceCode = field.NewString(table, "instance_code")
	i.SerialNumber = field.NewString(table, "serial_number")
	i.Uuid = field.NewString(table, "uuid")
	i.Status = field.NewString(table, "status")
	i.Reverted = field.NewBool(table, "reverted")
	i.StartTime = field.NewTime(table, "start_time")
	i.EndTime = field.NewTime(table, "end_time")
	i.UserId = field.NewString(table, "user_id")
	i.OpenId = field.NewString(table, "open_id")
	i.DepartmentId = field.NewString(table, "department_id")
	i.Form = field.NewField(table, "form")
	i.ContractType = field.NewString(table, "contract_type")
	i.ContractTitle = field.NewString(table, "contract_title")
	i.ContractAmount = field.NewFloat64(table, "contract_amount")
	i.Currency = field.NewString(table, "currency")

	i.fillFieldMap()

	return i
}

func (i *insContract) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insContract) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 25)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["approval_code"] = i.ApprovalCode
	i.fieldMap["approval_name"] = i.ApprovalName
	i.fieldMap["instance_code"] = i.InstanceCode
	i.fieldMap["serial_number"] = i.SerialNumber
	i.fieldMap["uuid"] = i.Uuid
	i.fieldMap["status"] = i.Status
	i.fieldMap["reverted"] = i.Reverted
	i.fieldMap["start_time"] = i.StartTime
	i.fieldMap["end_time"] = i.EndTime
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["open_id"] = i.OpenId
	i.fieldMap["department_id"] = i.DepartmentId
	i.fieldMap["form"] = i.Form
	i.fieldMap["contract_type"] = i.ContractType
	i.fieldMap["contract_title"] = i.ContractTitle
	i.fieldMap["contract_amount"] = i.ContractAmount
	i.fieldMap["currency"] = i.Currency

}

func (i insContract) clone(db *gorm.DB) insContract {
	i.insContractDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insContract) replaceDB(db *gorm.DB) insContract {
	i.insContractDo.ReplaceDB(db)
	return i
}

type insContractHasManyComments struct {
	db *gorm.DB

	field.RelationField
}

func (a insContractHasManyComments) Where(conds ...field.Expr) *insContractHasManyComments {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insContractHasManyComments) WithContext(ctx context.Context) *insContractHasManyComments {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insContractHasManyComments) Session(session *gorm.Session) *insContractHasManyComments {
	a.db = a.db.Session(session)
	return &a
}

func (a insContractHasManyComments) Model(m *insbuy.InsContract) *insContractHasManyCommentsTx {
	return &insContractHasManyCommentsTx{a.db.Model(m).Association(a.Name())}
}

type insContractHasManyCommentsTx struct{ tx *gorm.Association }

func (a insContractHasManyCommentsTx) Find() (result []*insbuy.InsContractComment, err error) {
	return result, a.tx.Find(&result)
}

func (a insContractHasManyCommentsTx) Append(values ...*insbuy.InsContractComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insContractHasManyCommentsTx) Replace(values ...*insbuy.InsContractComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insContractHasManyCommentsTx) Delete(values ...*insbuy.InsContractComment) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insContractHasManyCommentsTx) Clear() error {
	return a.tx.Clear()
}

func (a insContractHasManyCommentsTx) Count() int64 {
	return a.tx.Count()
}

type insContractHasManyTasks struct {
	db *gorm.DB

	field.RelationField
}

func (a insContractHasManyTasks) Where(conds ...field.Expr) *insContractHasManyTasks {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insContractHasManyTasks) WithContext(ctx context.Context) *insContractHasManyTasks {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insContractHasManyTasks) Session(session *gorm.Session) *insContractHasManyTasks {
	a.db = a.db.Session(session)
	return &a
}

func (a insContractHasManyTasks) Model(m *insbuy.InsContract) *insContractHasManyTasksTx {
	return &insContractHasManyTasksTx{a.db.Model(m).Association(a.Name())}
}

type insContractHasManyTasksTx struct{ tx *gorm.Association }

func (a insContractHasManyTasksTx) Find() (result []*insbuy.InsContractTask, err error) {
	return result, a.tx.Find(&result)
}

func (a insContractHasManyTasksTx) Append(values ...*insbuy.InsContractTask) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insContractHasManyTasksTx) Replace(values ...*insbuy.InsContractTask) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insContractHasManyTasksTx) Delete(values ...*insbuy.InsContractTask) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insContractHasManyTasksTx) Clear() error {
	return a.tx.Clear()
}

func (a insContractHasManyTasksTx) Count() int64 {
	return a.tx.Count()
}

type insContractHasManyTimeline struct {
	db *gorm.DB

	field.RelationField
}

func (a insContractHasManyTimeline) Where(conds ...field.Expr) *insContractHasManyTimeline {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insContractHasManyTimeline) WithContext(ctx context.Context) *insContractHasManyTimeline {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insContractHasManyTimeline) Session(session *gorm.Session) *insContractHasManyTimeline {
	a.db = a.db.Session(session)
	return &a
}

func (a insContractHasManyTimeline) Model(m *insbuy.InsContract) *insContractHasManyTimelineTx {
	return &insContractHasManyTimelineTx{a.db.Model(m).Association(a.Name())}
}

type insContractHasManyTimelineTx struct{ tx *gorm.Association }

func (a insContractHasManyTimelineTx) Find() (result []*insbuy.InsContractTimeline, err error) {
	return result, a.tx.Find(&result)
}

func (a insContractHasManyTimelineTx) Append(values ...*insbuy.InsContractTimeline) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insContractHasManyTimelineTx) Replace(values ...*insbuy.InsContractTimeline) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insContractHasManyTimelineTx) Delete(values ...*insbuy.InsContractTimeline) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insContractHasManyTimelineTx) Clear() error {
	return a.tx.Clear()
}

func (a insContractHasManyTimelineTx) Count() int64 {
	return a.tx.Count()
}

type insContractHasManyFiles struct {
	db *gorm.DB

	field.RelationField
}

func (a insContractHasManyFiles) Where(conds ...field.Expr) *insContractHasManyFiles {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a insContractHasManyFiles) WithContext(ctx context.Context) *insContractHasManyFiles {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a insContractHasManyFiles) Session(session *gorm.Session) *insContractHasManyFiles {
	a.db = a.db.Session(session)
	return &a
}

func (a insContractHasManyFiles) Model(m *insbuy.InsContract) *insContractHasManyFilesTx {
	return &insContractHasManyFilesTx{a.db.Model(m).Association(a.Name())}
}

type insContractHasManyFilesTx struct{ tx *gorm.Association }

func (a insContractHasManyFilesTx) Find() (result []*insbuy.InsContractFile, err error) {
	return result, a.tx.Find(&result)
}

func (a insContractHasManyFilesTx) Append(values ...*insbuy.InsContractFile) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a insContractHasManyFilesTx) Replace(values ...*insbuy.InsContractFile) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a insContractHasManyFilesTx) Delete(values ...*insbuy.InsContractFile) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a insContractHasManyFilesTx) Clear() error {
	return a.tx.Clear()
}

func (a insContractHasManyFilesTx) Count() int64 {
	return a.tx.Count()
}

type insContractDo struct{ gen.DO }

func (i insContractDo) Debug() *insContractDo {
	return i.withDO(i.DO.Debug())
}

func (i insContractDo) WithContext(ctx context.Context) *insContractDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insContractDo) ReadDB() *insContractDo {
	return i.Clauses(dbresolver.Read)
}

func (i insContractDo) WriteDB() *insContractDo {
	return i.Clauses(dbresolver.Write)
}

func (i insContractDo) Session(config *gorm.Session) *insContractDo {
	return i.withDO(i.DO.Session(config))
}

func (i insContractDo) Clauses(conds ...clause.Expression) *insContractDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insContractDo) Returning(value interface{}, columns ...string) *insContractDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insContractDo) Not(conds ...gen.Condition) *insContractDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insContractDo) Or(conds ...gen.Condition) *insContractDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insContractDo) Select(conds ...field.Expr) *insContractDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insContractDo) Where(conds ...gen.Condition) *insContractDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insContractDo) Order(conds ...field.Expr) *insContractDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insContractDo) Distinct(cols ...field.Expr) *insContractDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insContractDo) Omit(cols ...field.Expr) *insContractDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insContractDo) Join(table schema.Tabler, on ...field.Expr) *insContractDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insContractDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insContractDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insContractDo) RightJoin(table schema.Tabler, on ...field.Expr) *insContractDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insContractDo) Group(cols ...field.Expr) *insContractDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insContractDo) Having(conds ...gen.Condition) *insContractDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insContractDo) Limit(limit int) *insContractDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insContractDo) Offset(offset int) *insContractDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insContractDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insContractDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insContractDo) Unscoped() *insContractDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insContractDo) Create(values ...*insbuy.InsContract) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insContractDo) CreateInBatches(values []*insbuy.InsContract, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insContractDo) Save(values ...*insbuy.InsContract) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insContractDo) First() (*insbuy.InsContract, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContract), nil
	}
}

func (i insContractDo) Take() (*insbuy.InsContract, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContract), nil
	}
}

func (i insContractDo) Last() (*insbuy.InsContract, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContract), nil
	}
}

func (i insContractDo) Find() ([]*insbuy.InsContract, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsContract), err
}

func (i insContractDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsContract, err error) {
	buf := make([]*insbuy.InsContract, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insContractDo) FindInBatches(result *[]*insbuy.InsContract, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insContractDo) Attrs(attrs ...field.AssignExpr) *insContractDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insContractDo) Assign(attrs ...field.AssignExpr) *insContractDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insContractDo) Joins(fields ...field.RelationField) *insContractDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insContractDo) Preload(fields ...field.RelationField) *insContractDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insContractDo) FirstOrInit() (*insbuy.InsContract, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContract), nil
	}
}

func (i insContractDo) FirstOrCreate() (*insbuy.InsContract, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContract), nil
	}
}

func (i insContractDo) FindByPage(offset int, limit int) (result []*insbuy.InsContract, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insContractDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insContractDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insContractDo) Delete(models ...*insbuy.InsContract) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insContractDo) withDO(do gen.Dao) *insContractDo {
	i.DO = *do.(*gen.DO)
	return i
}
