// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsContractComment(db *gorm.DB, opts ...gen.DOOption) insContractComment {
	_insContractComment := insContractComment{}

	_insContractComment.insContractCommentDo.UseDB(db, opts...)
	_insContractComment.insContractCommentDo.UseModel(&insbuy.InsContractComment{})

	tableName := _insContractComment.insContractCommentDo.TableName()
	_insContractComment.ALL = field.NewAsterisk(tableName)
	_insContractComment.ID = field.NewUint(tableName, "id")
	_insContractComment.CreatedAt = field.NewTime(tableName, "created_at")
	_insContractComment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insContractComment.DeletedAt = field.NewField(tableName, "deleted_at")
	_insContractComment.ContractId = field.NewUint(tableName, "contract_id")
	_insContractComment.CommentId = field.NewString(tableName, "comment_id")
	_insContractComment.Comment = field.NewString(tableName, "comment")
	_insContractComment.CreateTime = field.NewString(tableName, "create_time")
	_insContractComment.OpenId = field.NewString(tableName, "open_id")
	_insContractComment.UserId = field.NewString(tableName, "user_id")

	_insContractComment.fillFieldMap()

	return _insContractComment
}

type insContractComment struct {
	insContractCommentDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	ContractId field.Uint
	CommentId  field.String
	Comment    field.String
	CreateTime field.String
	OpenId     field.String
	UserId     field.String

	fieldMap map[string]field.Expr
}

func (i insContractComment) Table(newTableName string) *insContractComment {
	i.insContractCommentDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insContractComment) As(alias string) *insContractComment {
	i.insContractCommentDo.DO = *(i.insContractCommentDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insContractComment) updateTableName(table string) *insContractComment {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ContractId = field.NewUint(table, "contract_id")
	i.CommentId = field.NewString(table, "comment_id")
	i.Comment = field.NewString(table, "comment")
	i.CreateTime = field.NewString(table, "create_time")
	i.OpenId = field.NewString(table, "open_id")
	i.UserId = field.NewString(table, "user_id")

	i.fillFieldMap()

	return i
}

func (i *insContractComment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insContractComment) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["contract_id"] = i.ContractId
	i.fieldMap["comment_id"] = i.CommentId
	i.fieldMap["comment"] = i.Comment
	i.fieldMap["create_time"] = i.CreateTime
	i.fieldMap["open_id"] = i.OpenId
	i.fieldMap["user_id"] = i.UserId
}

func (i insContractComment) clone(db *gorm.DB) insContractComment {
	i.insContractCommentDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insContractComment) replaceDB(db *gorm.DB) insContractComment {
	i.insContractCommentDo.ReplaceDB(db)
	return i
}

type insContractCommentDo struct{ gen.DO }

func (i insContractCommentDo) Debug() *insContractCommentDo {
	return i.withDO(i.DO.Debug())
}

func (i insContractCommentDo) WithContext(ctx context.Context) *insContractCommentDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insContractCommentDo) ReadDB() *insContractCommentDo {
	return i.Clauses(dbresolver.Read)
}

func (i insContractCommentDo) WriteDB() *insContractCommentDo {
	return i.Clauses(dbresolver.Write)
}

func (i insContractCommentDo) Session(config *gorm.Session) *insContractCommentDo {
	return i.withDO(i.DO.Session(config))
}

func (i insContractCommentDo) Clauses(conds ...clause.Expression) *insContractCommentDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insContractCommentDo) Returning(value interface{}, columns ...string) *insContractCommentDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insContractCommentDo) Not(conds ...gen.Condition) *insContractCommentDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insContractCommentDo) Or(conds ...gen.Condition) *insContractCommentDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insContractCommentDo) Select(conds ...field.Expr) *insContractCommentDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insContractCommentDo) Where(conds ...gen.Condition) *insContractCommentDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insContractCommentDo) Order(conds ...field.Expr) *insContractCommentDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insContractCommentDo) Distinct(cols ...field.Expr) *insContractCommentDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insContractCommentDo) Omit(cols ...field.Expr) *insContractCommentDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insContractCommentDo) Join(table schema.Tabler, on ...field.Expr) *insContractCommentDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insContractCommentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insContractCommentDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insContractCommentDo) RightJoin(table schema.Tabler, on ...field.Expr) *insContractCommentDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insContractCommentDo) Group(cols ...field.Expr) *insContractCommentDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insContractCommentDo) Having(conds ...gen.Condition) *insContractCommentDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insContractCommentDo) Limit(limit int) *insContractCommentDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insContractCommentDo) Offset(offset int) *insContractCommentDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insContractCommentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insContractCommentDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insContractCommentDo) Unscoped() *insContractCommentDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insContractCommentDo) Create(values ...*insbuy.InsContractComment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insContractCommentDo) CreateInBatches(values []*insbuy.InsContractComment, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insContractCommentDo) Save(values ...*insbuy.InsContractComment) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insContractCommentDo) First() (*insbuy.InsContractComment, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractComment), nil
	}
}

func (i insContractCommentDo) Take() (*insbuy.InsContractComment, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractComment), nil
	}
}

func (i insContractCommentDo) Last() (*insbuy.InsContractComment, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractComment), nil
	}
}

func (i insContractCommentDo) Find() ([]*insbuy.InsContractComment, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsContractComment), err
}

func (i insContractCommentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsContractComment, err error) {
	buf := make([]*insbuy.InsContractComment, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insContractCommentDo) FindInBatches(result *[]*insbuy.InsContractComment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insContractCommentDo) Attrs(attrs ...field.AssignExpr) *insContractCommentDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insContractCommentDo) Assign(attrs ...field.AssignExpr) *insContractCommentDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insContractCommentDo) Joins(fields ...field.RelationField) *insContractCommentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insContractCommentDo) Preload(fields ...field.RelationField) *insContractCommentDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insContractCommentDo) FirstOrInit() (*insbuy.InsContractComment, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractComment), nil
	}
}

func (i insContractCommentDo) FirstOrCreate() (*insbuy.InsContractComment, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractComment), nil
	}
}

func (i insContractCommentDo) FindByPage(offset int, limit int) (result []*insbuy.InsContractComment, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insContractCommentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insContractCommentDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insContractCommentDo) Delete(models ...*insbuy.InsContractComment) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insContractCommentDo) withDO(do gen.Dao) *insContractCommentDo {
	i.DO = *do.(*gen.DO)
	return i
}
