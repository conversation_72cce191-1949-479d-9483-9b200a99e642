// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsContractFile(db *gorm.DB, opts ...gen.DOOption) insContractFile {
	_insContractFile := insContractFile{}

	_insContractFile.insContractFileDo.UseDB(db, opts...)
	_insContractFile.insContractFileDo.UseModel(&insbuy.InsContractFile{})

	tableName := _insContractFile.insContractFileDo.TableName()
	_insContractFile.ALL = field.NewAsterisk(tableName)
	_insContractFile.ID = field.NewUint(tableName, "id")
	_insContractFile.CreatedAt = field.NewTime(tableName, "created_at")
	_insContractFile.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insContractFile.DeletedAt = field.NewField(tableName, "deleted_at")
	_insContractFile.ContractId = field.NewUint(tableName, "contract_id")
	_insContractFile.CommentId = field.NewUint(tableName, "comment_id")
	_insContractFile.FileSize = field.NewInt(tableName, "file_size")
	_insContractFile.Title = field.NewString(tableName, "title")
	_insContractFile.Type = field.NewString(tableName, "type")
	_insContractFile.Url = field.NewString(tableName, "url")

	_insContractFile.fillFieldMap()

	return _insContractFile
}

type insContractFile struct {
	insContractFileDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	ContractId field.Uint
	CommentId  field.Uint
	FileSize   field.Int
	Title      field.String
	Type       field.String
	Url        field.String

	fieldMap map[string]field.Expr
}

func (i insContractFile) Table(newTableName string) *insContractFile {
	i.insContractFileDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insContractFile) As(alias string) *insContractFile {
	i.insContractFileDo.DO = *(i.insContractFileDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insContractFile) updateTableName(table string) *insContractFile {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ContractId = field.NewUint(table, "contract_id")
	i.CommentId = field.NewUint(table, "comment_id")
	i.FileSize = field.NewInt(table, "file_size")
	i.Title = field.NewString(table, "title")
	i.Type = field.NewString(table, "type")
	i.Url = field.NewString(table, "url")

	i.fillFieldMap()

	return i
}

func (i *insContractFile) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insContractFile) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 10)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["contract_id"] = i.ContractId
	i.fieldMap["comment_id"] = i.CommentId
	i.fieldMap["file_size"] = i.FileSize
	i.fieldMap["title"] = i.Title
	i.fieldMap["type"] = i.Type
	i.fieldMap["url"] = i.Url
}

func (i insContractFile) clone(db *gorm.DB) insContractFile {
	i.insContractFileDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insContractFile) replaceDB(db *gorm.DB) insContractFile {
	i.insContractFileDo.ReplaceDB(db)
	return i
}

type insContractFileDo struct{ gen.DO }

func (i insContractFileDo) Debug() *insContractFileDo {
	return i.withDO(i.DO.Debug())
}

func (i insContractFileDo) WithContext(ctx context.Context) *insContractFileDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insContractFileDo) ReadDB() *insContractFileDo {
	return i.Clauses(dbresolver.Read)
}

func (i insContractFileDo) WriteDB() *insContractFileDo {
	return i.Clauses(dbresolver.Write)
}

func (i insContractFileDo) Session(config *gorm.Session) *insContractFileDo {
	return i.withDO(i.DO.Session(config))
}

func (i insContractFileDo) Clauses(conds ...clause.Expression) *insContractFileDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insContractFileDo) Returning(value interface{}, columns ...string) *insContractFileDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insContractFileDo) Not(conds ...gen.Condition) *insContractFileDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insContractFileDo) Or(conds ...gen.Condition) *insContractFileDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insContractFileDo) Select(conds ...field.Expr) *insContractFileDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insContractFileDo) Where(conds ...gen.Condition) *insContractFileDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insContractFileDo) Order(conds ...field.Expr) *insContractFileDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insContractFileDo) Distinct(cols ...field.Expr) *insContractFileDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insContractFileDo) Omit(cols ...field.Expr) *insContractFileDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insContractFileDo) Join(table schema.Tabler, on ...field.Expr) *insContractFileDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insContractFileDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insContractFileDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insContractFileDo) RightJoin(table schema.Tabler, on ...field.Expr) *insContractFileDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insContractFileDo) Group(cols ...field.Expr) *insContractFileDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insContractFileDo) Having(conds ...gen.Condition) *insContractFileDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insContractFileDo) Limit(limit int) *insContractFileDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insContractFileDo) Offset(offset int) *insContractFileDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insContractFileDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insContractFileDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insContractFileDo) Unscoped() *insContractFileDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insContractFileDo) Create(values ...*insbuy.InsContractFile) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insContractFileDo) CreateInBatches(values []*insbuy.InsContractFile, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insContractFileDo) Save(values ...*insbuy.InsContractFile) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insContractFileDo) First() (*insbuy.InsContractFile, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractFile), nil
	}
}

func (i insContractFileDo) Take() (*insbuy.InsContractFile, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractFile), nil
	}
}

func (i insContractFileDo) Last() (*insbuy.InsContractFile, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractFile), nil
	}
}

func (i insContractFileDo) Find() ([]*insbuy.InsContractFile, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsContractFile), err
}

func (i insContractFileDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsContractFile, err error) {
	buf := make([]*insbuy.InsContractFile, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insContractFileDo) FindInBatches(result *[]*insbuy.InsContractFile, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insContractFileDo) Attrs(attrs ...field.AssignExpr) *insContractFileDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insContractFileDo) Assign(attrs ...field.AssignExpr) *insContractFileDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insContractFileDo) Joins(fields ...field.RelationField) *insContractFileDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insContractFileDo) Preload(fields ...field.RelationField) *insContractFileDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insContractFileDo) FirstOrInit() (*insbuy.InsContractFile, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractFile), nil
	}
}

func (i insContractFileDo) FirstOrCreate() (*insbuy.InsContractFile, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractFile), nil
	}
}

func (i insContractFileDo) FindByPage(offset int, limit int) (result []*insbuy.InsContractFile, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insContractFileDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insContractFileDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insContractFileDo) Delete(models ...*insbuy.InsContractFile) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insContractFileDo) withDO(do gen.Dao) *insContractFileDo {
	i.DO = *do.(*gen.DO)
	return i
}
