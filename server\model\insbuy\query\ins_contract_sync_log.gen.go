// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsContractSyncLog(db *gorm.DB, opts ...gen.DOOption) insContractSyncLog {
	_insContractSyncLog := insContractSyncLog{}

	_insContractSyncLog.insContractSyncLogDo.UseDB(db, opts...)
	_insContractSyncLog.insContractSyncLogDo.UseModel(&insbuy.InsContractSyncLog{})

	tableName := _insContractSyncLog.insContractSyncLogDo.TableName()
	_insContractSyncLog.ALL = field.NewAsterisk(tableName)
	_insContractSyncLog.ID = field.NewUint(tableName, "id")
	_insContractSyncLog.CreatedAt = field.NewTime(tableName, "created_at")
	_insContractSyncLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insContractSyncLog.DeletedAt = field.NewField(tableName, "deleted_at")
	_insContractSyncLog.ApprovalCode = field.NewString(tableName, "approval_code")
	_insContractSyncLog.SyncType = field.NewString(tableName, "sync_type")
	_insContractSyncLog.Status = field.NewString(tableName, "status")
	_insContractSyncLog.StartTime = field.NewTime(tableName, "start_time")
	_insContractSyncLog.EndTime = field.NewTime(tableName, "end_time")
	_insContractSyncLog.RecordCount = field.NewInt(tableName, "record_count")
	_insContractSyncLog.ErrorMsg = field.NewString(tableName, "error_msg")
	_insContractSyncLog.PageToken = field.NewString(tableName, "page_token")

	_insContractSyncLog.fillFieldMap()

	return _insContractSyncLog
}

type insContractSyncLog struct {
	insContractSyncLogDo

	ALL          field.Asterisk
	ID           field.Uint
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field
	ApprovalCode field.String
	SyncType     field.String
	Status       field.String
	StartTime    field.Time
	EndTime      field.Time
	RecordCount  field.Int
	ErrorMsg     field.String
	PageToken    field.String

	fieldMap map[string]field.Expr
}

func (i insContractSyncLog) Table(newTableName string) *insContractSyncLog {
	i.insContractSyncLogDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insContractSyncLog) As(alias string) *insContractSyncLog {
	i.insContractSyncLogDo.DO = *(i.insContractSyncLogDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insContractSyncLog) updateTableName(table string) *insContractSyncLog {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ApprovalCode = field.NewString(table, "approval_code")
	i.SyncType = field.NewString(table, "sync_type")
	i.Status = field.NewString(table, "status")
	i.StartTime = field.NewTime(table, "start_time")
	i.EndTime = field.NewTime(table, "end_time")
	i.RecordCount = field.NewInt(table, "record_count")
	i.ErrorMsg = field.NewString(table, "error_msg")
	i.PageToken = field.NewString(table, "page_token")

	i.fillFieldMap()

	return i
}

func (i *insContractSyncLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insContractSyncLog) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 12)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["approval_code"] = i.ApprovalCode
	i.fieldMap["sync_type"] = i.SyncType
	i.fieldMap["status"] = i.Status
	i.fieldMap["start_time"] = i.StartTime
	i.fieldMap["end_time"] = i.EndTime
	i.fieldMap["record_count"] = i.RecordCount
	i.fieldMap["error_msg"] = i.ErrorMsg
	i.fieldMap["page_token"] = i.PageToken
}

func (i insContractSyncLog) clone(db *gorm.DB) insContractSyncLog {
	i.insContractSyncLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insContractSyncLog) replaceDB(db *gorm.DB) insContractSyncLog {
	i.insContractSyncLogDo.ReplaceDB(db)
	return i
}

type insContractSyncLogDo struct{ gen.DO }

func (i insContractSyncLogDo) Debug() *insContractSyncLogDo {
	return i.withDO(i.DO.Debug())
}

func (i insContractSyncLogDo) WithContext(ctx context.Context) *insContractSyncLogDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insContractSyncLogDo) ReadDB() *insContractSyncLogDo {
	return i.Clauses(dbresolver.Read)
}

func (i insContractSyncLogDo) WriteDB() *insContractSyncLogDo {
	return i.Clauses(dbresolver.Write)
}

func (i insContractSyncLogDo) Session(config *gorm.Session) *insContractSyncLogDo {
	return i.withDO(i.DO.Session(config))
}

func (i insContractSyncLogDo) Clauses(conds ...clause.Expression) *insContractSyncLogDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insContractSyncLogDo) Returning(value interface{}, columns ...string) *insContractSyncLogDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insContractSyncLogDo) Not(conds ...gen.Condition) *insContractSyncLogDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insContractSyncLogDo) Or(conds ...gen.Condition) *insContractSyncLogDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insContractSyncLogDo) Select(conds ...field.Expr) *insContractSyncLogDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insContractSyncLogDo) Where(conds ...gen.Condition) *insContractSyncLogDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insContractSyncLogDo) Order(conds ...field.Expr) *insContractSyncLogDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insContractSyncLogDo) Distinct(cols ...field.Expr) *insContractSyncLogDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insContractSyncLogDo) Omit(cols ...field.Expr) *insContractSyncLogDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insContractSyncLogDo) Join(table schema.Tabler, on ...field.Expr) *insContractSyncLogDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insContractSyncLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insContractSyncLogDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insContractSyncLogDo) RightJoin(table schema.Tabler, on ...field.Expr) *insContractSyncLogDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insContractSyncLogDo) Group(cols ...field.Expr) *insContractSyncLogDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insContractSyncLogDo) Having(conds ...gen.Condition) *insContractSyncLogDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insContractSyncLogDo) Limit(limit int) *insContractSyncLogDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insContractSyncLogDo) Offset(offset int) *insContractSyncLogDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insContractSyncLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insContractSyncLogDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insContractSyncLogDo) Unscoped() *insContractSyncLogDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insContractSyncLogDo) Create(values ...*insbuy.InsContractSyncLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insContractSyncLogDo) CreateInBatches(values []*insbuy.InsContractSyncLog, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insContractSyncLogDo) Save(values ...*insbuy.InsContractSyncLog) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insContractSyncLogDo) First() (*insbuy.InsContractSyncLog, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractSyncLog), nil
	}
}

func (i insContractSyncLogDo) Take() (*insbuy.InsContractSyncLog, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractSyncLog), nil
	}
}

func (i insContractSyncLogDo) Last() (*insbuy.InsContractSyncLog, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractSyncLog), nil
	}
}

func (i insContractSyncLogDo) Find() ([]*insbuy.InsContractSyncLog, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsContractSyncLog), err
}

func (i insContractSyncLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsContractSyncLog, err error) {
	buf := make([]*insbuy.InsContractSyncLog, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insContractSyncLogDo) FindInBatches(result *[]*insbuy.InsContractSyncLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insContractSyncLogDo) Attrs(attrs ...field.AssignExpr) *insContractSyncLogDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insContractSyncLogDo) Assign(attrs ...field.AssignExpr) *insContractSyncLogDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insContractSyncLogDo) Joins(fields ...field.RelationField) *insContractSyncLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insContractSyncLogDo) Preload(fields ...field.RelationField) *insContractSyncLogDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insContractSyncLogDo) FirstOrInit() (*insbuy.InsContractSyncLog, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractSyncLog), nil
	}
}

func (i insContractSyncLogDo) FirstOrCreate() (*insbuy.InsContractSyncLog, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractSyncLog), nil
	}
}

func (i insContractSyncLogDo) FindByPage(offset int, limit int) (result []*insbuy.InsContractSyncLog, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insContractSyncLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insContractSyncLogDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insContractSyncLogDo) Delete(models ...*insbuy.InsContractSyncLog) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insContractSyncLogDo) withDO(do gen.Dao) *insContractSyncLogDo {
	i.DO = *do.(*gen.DO)
	return i
}
