// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsContractTask(db *gorm.DB, opts ...gen.DOOption) insContractTask {
	_insContractTask := insContractTask{}

	_insContractTask.insContractTaskDo.UseDB(db, opts...)
	_insContractTask.insContractTaskDo.UseModel(&insbuy.InsContractTask{})

	tableName := _insContractTask.insContractTaskDo.TableName()
	_insContractTask.ALL = field.NewAsterisk(tableName)
	_insContractTask.ID = field.NewUint(tableName, "id")
	_insContractTask.CreatedAt = field.NewTime(tableName, "created_at")
	_insContractTask.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insContractTask.DeletedAt = field.NewField(tableName, "deleted_at")
	_insContractTask.ContractId = field.NewUint(tableName, "contract_id")
	_insContractTask.TaskId = field.NewString(tableName, "task_id")
	_insContractTask.NodeId = field.NewString(tableName, "node_id")
	_insContractTask.NodeName = field.NewString(tableName, "node_name")
	_insContractTask.Type = field.NewString(tableName, "type")
	_insContractTask.Status = field.NewString(tableName, "status")
	_insContractTask.StartTime = field.NewString(tableName, "start_time")
	_insContractTask.EndTime = field.NewString(tableName, "end_time")
	_insContractTask.OpenId = field.NewString(tableName, "open_id")
	_insContractTask.UserId = field.NewString(tableName, "user_id")

	_insContractTask.fillFieldMap()

	return _insContractTask
}

type insContractTask struct {
	insContractTaskDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	ContractId field.Uint
	TaskId     field.String
	NodeId     field.String
	NodeName   field.String
	Type       field.String
	Status     field.String
	StartTime  field.String
	EndTime    field.String
	OpenId     field.String
	UserId     field.String

	fieldMap map[string]field.Expr
}

func (i insContractTask) Table(newTableName string) *insContractTask {
	i.insContractTaskDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insContractTask) As(alias string) *insContractTask {
	i.insContractTaskDo.DO = *(i.insContractTaskDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insContractTask) updateTableName(table string) *insContractTask {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ContractId = field.NewUint(table, "contract_id")
	i.TaskId = field.NewString(table, "task_id")
	i.NodeId = field.NewString(table, "node_id")
	i.NodeName = field.NewString(table, "node_name")
	i.Type = field.NewString(table, "type")
	i.Status = field.NewString(table, "status")
	i.StartTime = field.NewString(table, "start_time")
	i.EndTime = field.NewString(table, "end_time")
	i.OpenId = field.NewString(table, "open_id")
	i.UserId = field.NewString(table, "user_id")

	i.fillFieldMap()

	return i
}

func (i *insContractTask) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insContractTask) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 14)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["contract_id"] = i.ContractId
	i.fieldMap["task_id"] = i.TaskId
	i.fieldMap["node_id"] = i.NodeId
	i.fieldMap["node_name"] = i.NodeName
	i.fieldMap["type"] = i.Type
	i.fieldMap["status"] = i.Status
	i.fieldMap["start_time"] = i.StartTime
	i.fieldMap["end_time"] = i.EndTime
	i.fieldMap["open_id"] = i.OpenId
	i.fieldMap["user_id"] = i.UserId
}

func (i insContractTask) clone(db *gorm.DB) insContractTask {
	i.insContractTaskDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insContractTask) replaceDB(db *gorm.DB) insContractTask {
	i.insContractTaskDo.ReplaceDB(db)
	return i
}

type insContractTaskDo struct{ gen.DO }

func (i insContractTaskDo) Debug() *insContractTaskDo {
	return i.withDO(i.DO.Debug())
}

func (i insContractTaskDo) WithContext(ctx context.Context) *insContractTaskDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insContractTaskDo) ReadDB() *insContractTaskDo {
	return i.Clauses(dbresolver.Read)
}

func (i insContractTaskDo) WriteDB() *insContractTaskDo {
	return i.Clauses(dbresolver.Write)
}

func (i insContractTaskDo) Session(config *gorm.Session) *insContractTaskDo {
	return i.withDO(i.DO.Session(config))
}

func (i insContractTaskDo) Clauses(conds ...clause.Expression) *insContractTaskDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insContractTaskDo) Returning(value interface{}, columns ...string) *insContractTaskDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insContractTaskDo) Not(conds ...gen.Condition) *insContractTaskDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insContractTaskDo) Or(conds ...gen.Condition) *insContractTaskDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insContractTaskDo) Select(conds ...field.Expr) *insContractTaskDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insContractTaskDo) Where(conds ...gen.Condition) *insContractTaskDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insContractTaskDo) Order(conds ...field.Expr) *insContractTaskDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insContractTaskDo) Distinct(cols ...field.Expr) *insContractTaskDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insContractTaskDo) Omit(cols ...field.Expr) *insContractTaskDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insContractTaskDo) Join(table schema.Tabler, on ...field.Expr) *insContractTaskDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insContractTaskDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insContractTaskDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insContractTaskDo) RightJoin(table schema.Tabler, on ...field.Expr) *insContractTaskDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insContractTaskDo) Group(cols ...field.Expr) *insContractTaskDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insContractTaskDo) Having(conds ...gen.Condition) *insContractTaskDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insContractTaskDo) Limit(limit int) *insContractTaskDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insContractTaskDo) Offset(offset int) *insContractTaskDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insContractTaskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insContractTaskDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insContractTaskDo) Unscoped() *insContractTaskDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insContractTaskDo) Create(values ...*insbuy.InsContractTask) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insContractTaskDo) CreateInBatches(values []*insbuy.InsContractTask, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insContractTaskDo) Save(values ...*insbuy.InsContractTask) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insContractTaskDo) First() (*insbuy.InsContractTask, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTask), nil
	}
}

func (i insContractTaskDo) Take() (*insbuy.InsContractTask, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTask), nil
	}
}

func (i insContractTaskDo) Last() (*insbuy.InsContractTask, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTask), nil
	}
}

func (i insContractTaskDo) Find() ([]*insbuy.InsContractTask, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsContractTask), err
}

func (i insContractTaskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsContractTask, err error) {
	buf := make([]*insbuy.InsContractTask, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insContractTaskDo) FindInBatches(result *[]*insbuy.InsContractTask, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insContractTaskDo) Attrs(attrs ...field.AssignExpr) *insContractTaskDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insContractTaskDo) Assign(attrs ...field.AssignExpr) *insContractTaskDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insContractTaskDo) Joins(fields ...field.RelationField) *insContractTaskDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insContractTaskDo) Preload(fields ...field.RelationField) *insContractTaskDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insContractTaskDo) FirstOrInit() (*insbuy.InsContractTask, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTask), nil
	}
}

func (i insContractTaskDo) FirstOrCreate() (*insbuy.InsContractTask, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTask), nil
	}
}

func (i insContractTaskDo) FindByPage(offset int, limit int) (result []*insbuy.InsContractTask, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insContractTaskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insContractTaskDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insContractTaskDo) Delete(models ...*insbuy.InsContractTask) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insContractTaskDo) withDO(do gen.Dao) *insContractTaskDo {
	i.DO = *do.(*gen.DO)
	return i
}
