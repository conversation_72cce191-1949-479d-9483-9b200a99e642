// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"
)

func newInsContractTimeline(db *gorm.DB, opts ...gen.DOOption) insContractTimeline {
	_insContractTimeline := insContractTimeline{}

	_insContractTimeline.insContractTimelineDo.UseDB(db, opts...)
	_insContractTimeline.insContractTimelineDo.UseModel(&insbuy.InsContractTimeline{})

	tableName := _insContractTimeline.insContractTimelineDo.TableName()
	_insContractTimeline.ALL = field.NewAsterisk(tableName)
	_insContractTimeline.ID = field.NewUint(tableName, "id")
	_insContractTimeline.CreatedAt = field.NewTime(tableName, "created_at")
	_insContractTimeline.UpdatedAt = field.NewTime(tableName, "updated_at")
	_insContractTimeline.DeletedAt = field.NewField(tableName, "deleted_at")
	_insContractTimeline.ContractId = field.NewUint(tableName, "contract_id")
	_insContractTimeline.CreateTime = field.NewString(tableName, "create_time")
	_insContractTimeline.Ext = field.NewString(tableName, "ext")
	_insContractTimeline.NodeKey = field.NewString(tableName, "node_key")
	_insContractTimeline.OpenId = field.NewString(tableName, "open_id")
	_insContractTimeline.Type = field.NewString(tableName, "type")
	_insContractTimeline.UserId = field.NewString(tableName, "user_id")
	_insContractTimeline.TaskId = field.NewString(tableName, "task_id")
	_insContractTimeline.CcUserList = field.NewField(tableName, "cc_user_list")
	_insContractTimeline.OpenIdList = field.NewField(tableName, "open_id_list")
	_insContractTimeline.UserIdList = field.NewField(tableName, "user_id_list")

	_insContractTimeline.fillFieldMap()

	return _insContractTimeline
}

type insContractTimeline struct {
	insContractTimelineDo

	ALL        field.Asterisk
	ID         field.Uint
	CreatedAt  field.Time
	UpdatedAt  field.Time
	DeletedAt  field.Field
	ContractId field.Uint
	CreateTime field.String
	Ext        field.String
	NodeKey    field.String
	OpenId     field.String
	Type       field.String
	UserId     field.String
	TaskId     field.String
	CcUserList field.Field
	OpenIdList field.Field
	UserIdList field.Field

	fieldMap map[string]field.Expr
}

func (i insContractTimeline) Table(newTableName string) *insContractTimeline {
	i.insContractTimelineDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i insContractTimeline) As(alias string) *insContractTimeline {
	i.insContractTimelineDo.DO = *(i.insContractTimelineDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *insContractTimeline) updateTableName(table string) *insContractTimeline {
	i.ALL = field.NewAsterisk(table)
	i.ID = field.NewUint(table, "id")
	i.CreatedAt = field.NewTime(table, "created_at")
	i.UpdatedAt = field.NewTime(table, "updated_at")
	i.DeletedAt = field.NewField(table, "deleted_at")
	i.ContractId = field.NewUint(table, "contract_id")
	i.CreateTime = field.NewString(table, "create_time")
	i.Ext = field.NewString(table, "ext")
	i.NodeKey = field.NewString(table, "node_key")
	i.OpenId = field.NewString(table, "open_id")
	i.Type = field.NewString(table, "type")
	i.UserId = field.NewString(table, "user_id")
	i.TaskId = field.NewString(table, "task_id")
	i.CcUserList = field.NewField(table, "cc_user_list")
	i.OpenIdList = field.NewField(table, "open_id_list")
	i.UserIdList = field.NewField(table, "user_id_list")

	i.fillFieldMap()

	return i
}

func (i *insContractTimeline) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *insContractTimeline) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 15)
	i.fieldMap["id"] = i.ID
	i.fieldMap["created_at"] = i.CreatedAt
	i.fieldMap["updated_at"] = i.UpdatedAt
	i.fieldMap["deleted_at"] = i.DeletedAt
	i.fieldMap["contract_id"] = i.ContractId
	i.fieldMap["create_time"] = i.CreateTime
	i.fieldMap["ext"] = i.Ext
	i.fieldMap["node_key"] = i.NodeKey
	i.fieldMap["open_id"] = i.OpenId
	i.fieldMap["type"] = i.Type
	i.fieldMap["user_id"] = i.UserId
	i.fieldMap["task_id"] = i.TaskId
	i.fieldMap["cc_user_list"] = i.CcUserList
	i.fieldMap["open_id_list"] = i.OpenIdList
	i.fieldMap["user_id_list"] = i.UserIdList
}

func (i insContractTimeline) clone(db *gorm.DB) insContractTimeline {
	i.insContractTimelineDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i insContractTimeline) replaceDB(db *gorm.DB) insContractTimeline {
	i.insContractTimelineDo.ReplaceDB(db)
	return i
}

type insContractTimelineDo struct{ gen.DO }

func (i insContractTimelineDo) Debug() *insContractTimelineDo {
	return i.withDO(i.DO.Debug())
}

func (i insContractTimelineDo) WithContext(ctx context.Context) *insContractTimelineDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i insContractTimelineDo) ReadDB() *insContractTimelineDo {
	return i.Clauses(dbresolver.Read)
}

func (i insContractTimelineDo) WriteDB() *insContractTimelineDo {
	return i.Clauses(dbresolver.Write)
}

func (i insContractTimelineDo) Session(config *gorm.Session) *insContractTimelineDo {
	return i.withDO(i.DO.Session(config))
}

func (i insContractTimelineDo) Clauses(conds ...clause.Expression) *insContractTimelineDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i insContractTimelineDo) Returning(value interface{}, columns ...string) *insContractTimelineDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i insContractTimelineDo) Not(conds ...gen.Condition) *insContractTimelineDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i insContractTimelineDo) Or(conds ...gen.Condition) *insContractTimelineDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i insContractTimelineDo) Select(conds ...field.Expr) *insContractTimelineDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i insContractTimelineDo) Where(conds ...gen.Condition) *insContractTimelineDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i insContractTimelineDo) Order(conds ...field.Expr) *insContractTimelineDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i insContractTimelineDo) Distinct(cols ...field.Expr) *insContractTimelineDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i insContractTimelineDo) Omit(cols ...field.Expr) *insContractTimelineDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i insContractTimelineDo) Join(table schema.Tabler, on ...field.Expr) *insContractTimelineDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i insContractTimelineDo) LeftJoin(table schema.Tabler, on ...field.Expr) *insContractTimelineDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i insContractTimelineDo) RightJoin(table schema.Tabler, on ...field.Expr) *insContractTimelineDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i insContractTimelineDo) Group(cols ...field.Expr) *insContractTimelineDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i insContractTimelineDo) Having(conds ...gen.Condition) *insContractTimelineDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i insContractTimelineDo) Limit(limit int) *insContractTimelineDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i insContractTimelineDo) Offset(offset int) *insContractTimelineDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i insContractTimelineDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *insContractTimelineDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i insContractTimelineDo) Unscoped() *insContractTimelineDo {
	return i.withDO(i.DO.Unscoped())
}

func (i insContractTimelineDo) Create(values ...*insbuy.InsContractTimeline) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i insContractTimelineDo) CreateInBatches(values []*insbuy.InsContractTimeline, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i insContractTimelineDo) Save(values ...*insbuy.InsContractTimeline) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i insContractTimelineDo) First() (*insbuy.InsContractTimeline, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTimeline), nil
	}
}

func (i insContractTimelineDo) Take() (*insbuy.InsContractTimeline, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTimeline), nil
	}
}

func (i insContractTimelineDo) Last() (*insbuy.InsContractTimeline, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTimeline), nil
	}
}

func (i insContractTimelineDo) Find() ([]*insbuy.InsContractTimeline, error) {
	result, err := i.DO.Find()
	return result.([]*insbuy.InsContractTimeline), err
}

func (i insContractTimelineDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*insbuy.InsContractTimeline, err error) {
	buf := make([]*insbuy.InsContractTimeline, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i insContractTimelineDo) FindInBatches(result *[]*insbuy.InsContractTimeline, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i insContractTimelineDo) Attrs(attrs ...field.AssignExpr) *insContractTimelineDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i insContractTimelineDo) Assign(attrs ...field.AssignExpr) *insContractTimelineDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i insContractTimelineDo) Joins(fields ...field.RelationField) *insContractTimelineDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i insContractTimelineDo) Preload(fields ...field.RelationField) *insContractTimelineDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i insContractTimelineDo) FirstOrInit() (*insbuy.InsContractTimeline, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTimeline), nil
	}
}

func (i insContractTimelineDo) FirstOrCreate() (*insbuy.InsContractTimeline, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*insbuy.InsContractTimeline), nil
	}
}

func (i insContractTimelineDo) FindByPage(offset int, limit int) (result []*insbuy.InsContractTimeline, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i insContractTimelineDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i insContractTimelineDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i insContractTimelineDo) Delete(models ...*insbuy.InsContractTimeline) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *insContractTimelineDo) withDO(do gen.Dao) *insContractTimelineDo {
	i.DO = *do.(*gen.DO)
	return i
}
