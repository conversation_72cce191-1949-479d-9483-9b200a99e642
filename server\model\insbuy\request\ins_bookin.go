package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/notesmodel"
	"time"
)

// TODO: 未完成

// InsBookInAreaList 指定分店，列出店内所有区域
type InsBookInAreaList struct {
	WithCtx
	WithAuthUser
	WithStoreId
}

// InsBookInDeskStatusList 指定分店，列出店内所有「桌台可用状态」
type InsBookInDeskStatusList struct {
	WithCtx
	WithAuthUser
	WithStoreId
}

// InsBookInDeskList 指定分店、区域（可选）、桌台状态（可选），列出所有桌台，包括桌台状态等信息，列出店内所有「桌台」
type InsBookInDeskList struct {
	WithCtx
	WithAuthUser
	WithStoreId
	AreaId     uint   `json:"areaId" form:"areaId"`         // 区域ID
	Status     []uint `json:"status" form:"status"`         // 桌台状态
	Style      *int   `json:"style" form:"style"`           // 桌台样式
	SalesmanId uint   `json:"salesmanId" form:"salesmanId"` //销售id
}

type OpenDeskHistoryListReq struct {
	WithCtx
	WithAuthUser
	WithStoreId
	WithGin

	AreaId      uint       `json:"areaId" form:"areaId"`         // 区域ID
	Status      []uint     `json:"status" form:"status"`         // 桌台状态
	Style       *int       `json:"style" form:"style"`           // 桌台样式
	SalesmanId  uint       `json:"salesmanId" form:"salesmanId"` //销售id
	DeskId      uint       `json:"deskId" form:"deskId"`         //桌台id
	StartTime   *time.Time `json:"startTime" form:"startTime"`   //开始时间
	EndTime     *time.Time `json:"endTime" form:"endTime"`       //结束时间
	BusinessDay *time.Time `json:"businessDay" form:"businessDay"`
	SalerId     uint       `json:"salerId" form:"salerId"`   //销售id-后台查询使用
	SalerOrg    uint       `json:"salerOrg" form:"salerOrg"` //销售组织-后台查询使用
	request.PageReq
	request.ExportReq
}

// InsBookInAddTempDesk 添加「临时桌台」
type InsBookInAddTempDesk struct {
	DeskName string `json:"deskName" form:"deskName"` // 桌台名称
	DeskNO   string `json:"deskNo" form:"deskNo"`     // 桌台编号，可自定义
}

type InsBookInOpenDesk struct {
	WithStoreId
	WithCtx
	WithAuthUser

	OpenDesk
}

type OpenDesk struct {
	// 人数
	PeopleNum uint `json:"peopleNum" form:"peopleNum"`
	// 会员
	MemberId uint `json:"memberId" form:"memberId"`
	// 桌台id
	DeskId uint `json:"deskId" form:"deskId"`
	// 销售人员
	SalesmanId uint `json:"salesmanId" form:"salesmanId"`
	// 最低消费金额
	MinAmount float64 `json:"minAmount" form:"minAmount"`
	// 备注
	Remark string `json:"remark" form:"remark"`
	// 备注列表
	RemarkExt notesmodel.Notes `form:"remarkExt" json:"remarkExt"` //开台备注
	// 临时会员名称
	TempMemberName  string     `json:"tempMemberName" form:"tempMemberName"`
	BookType        int        `json:"bookType" form:"bookType"`               //预约类型  0=无预约 1=普通预约 2=压台预约
	BookArrivalTime *time.Time `json:"bookArrivalTime" form:"bookArrivalTime"` //预约到店时间
	BookAmount      float64    `json:"bookAmount" form:"bookAmount"`           //定金
	OpenDeskId      uint       `json:"openDeskId" form:"openDeskId"`           //开台id
	CustomerSource  int        `json:"customerSource" form:"customerSource"`   //客户来源 1=餐厅 2通票 3点评 4集团销售 5扫码点单
	Force           *int       `json:"force" form:"force"`                     //是否强制开台 0=否 1=是
}

type TryOpenDeskParams struct {
	UserId   uint
	StoreId  uint
	SStoreId string
	OpenDesk
}

type InsBookInOpenDeskDetail struct {
	OpenDeskId uint `json:"openDeskId" form:"openDeskId"` //开台编号
}

type InsBookInCloseDeskReq struct {
	OpenDeskId uint `json:"openDeskId" form:"openDeskId"` //开台编号
	CancelDesk int  `json:"cancelDesk" form:"cancelDesk"` //是否撤销桌台 0=否 1=是
	RealDeskId uint `json:"realDeskId" form:"realDeskId"` //真实台号

	WithStoreId
	WithAuthUser
}

type InsBookInCloseAllDeskResp struct {
	WithStoreId
	WithAuthUser
}

type CancelBookReq struct {
}

type BookOpenDeskReq struct {
	WithStoreId
	WithAuthUser

	OpenDeskId uint `json:"openDeskId" form:"openDeskId"` //开台id
}

// OpenLogParams 开台状态参数
type OpenLogParams struct {
	OpenDeskId  uint
	StoreId     uint
	FormDeskId  uint //旧台号
	DeskId      uint
	OldStatusId int
	NewStatusId int
	OperatorId  uint
	Remark      string
	Event       insbuy.DeskAction
}

type OpenDeskSnapshotReq struct {
	WithAuthUser
	StoreId   uint   `json:"storeId" form:"storeId"`
	StartDate string `json:"startDate" form:"startDate"`
	EndDate   string `json:"endDate" form:"endDate"`
}
