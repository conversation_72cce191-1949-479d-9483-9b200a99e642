package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// ContractListRequest 合同列表查询请求
type ContractListRequest struct {
	request.PageInfo

	// 查询条件
	ApprovalCode  string     `json:"approval_code" form:"approval_code"`     // 审批定义Code
	Status        string     `json:"status" form:"status"`                   // 审批状态
	ContractType  string     `json:"contract_type" form:"contract_type"`     // 合同类型
	UserId        string     `json:"user_id" form:"user_id"`                 // 发起人UserId
	DepartmentId  string     `json:"department_id" form:"department_id"`     // 部门ID
	StartTimeFrom *time.Time `json:"start_time_from" form:"start_time_from"` // 开始时间范围-起
	StartTimeTo   *time.Time `json:"start_time_to" form:"start_time_to"`     // 开始时间范围-止
	EndTimeFrom   *time.Time `json:"end_time_from" form:"end_time_from"`     // 结束时间范围-起
	EndTimeTo     *time.Time `json:"end_time_to" form:"end_time_to"`         // 结束时间范围-止
	AmountFrom    *float64   `json:"amount_from" form:"amount_from"`         // 金额范围-起
	AmountTo      *float64   `json:"amount_to" form:"amount_to"`             // 金额范围-止
	ContractTitle string     `json:"contract_title" form:"contract_title"`   // 合同标题（模糊查询）
	InstanceCode  string     `json:"instance_code" form:"instance_code"`     // 实例代码
	SerialNumber  string     `json:"serial_number" form:"serial_number"`     // 序列号

	// 排序
	OrderBy   string `json:"order_by" form:"order_by"`     // 排序字段
	OrderType string `json:"order_type" form:"order_type"` // 排序方式 asc/desc
}

// ContractDetailRequest 合同详情查询请求
type ContractDetailRequest struct {
	ID           uint   `json:"id" form:"id"`                       // 主键ID
	InstanceCode string `json:"instance_code" form:"instance_code"` // 实例代码
	Uuid         string `json:"uuid" form:"uuid"`                   // UUID
}

// ContractSyncRequest 合同同步请求
type ContractSyncRequest struct {
	ApprovalCode string     `json:"approval_code" binding:"required"` // 审批定义Code
	StartTime    *time.Time `json:"start_time"`                       // 开始时间
	EndTime      *time.Time `json:"end_time"`                         // 结束时间
	ForceSync    bool       `json:"force_sync"`                       // 是否强制同步
}

// ContractBatchSyncRequest 批量合同同步请求
type ContractBatchSyncRequest struct {
	ApprovalCodes []string   `json:"approval_codes" binding:"required"` // 审批定义Code列表
	StartTime     *time.Time `json:"start_time"`                        // 开始时间
	EndTime       *time.Time `json:"end_time"`                          // 结束时间
	ForceSync     bool       `json:"force_sync"`                        // 是否强制同步
	BatchSize     int        `json:"batch_size"`                        // 批次大小，默认20
	PageSize      int        `json:"page_size"`                         // 分页大小，默认100
}

// ContractMultiSyncRequest 多审批代码同步请求
type ContractMultiSyncRequest struct {
	ApprovalCodes []string   `json:"approval_codes" binding:"required"` // 审批定义Code列表，必填
	StartTime     *time.Time `json:"start_time"`                        // 开始时间，默认最近30天
	EndTime       *time.Time `json:"end_time"`                          // 结束时间，默认当前时间
	BatchSize     int        `json:"batch_size"`                        // 详情获取批次大小，默认20
	PageSize      int        `json:"page_size"`                         // 列表分页大小，默认100
	ForceSync     bool       `json:"force_sync"`                        // 是否强制同步，忽略最近同步时间
	MaxRetries    int        `json:"max_retries"`                       // 最大重试次数，默认3
	RetryDelay    int        `json:"retry_delay"`                       // 重试延迟（秒），默认5
}

// ContractExportRequest 合同导出请求
type ContractExportRequest struct {
	ContractListRequest

	ExportFields []string `json:"export_fields"` // 导出字段列表
	ExportFormat string   `json:"export_format"` // 导出格式 excel/csv
}

// ContractStatisticsRequest 合同统计请求
type ContractStatisticsRequest struct {
	ApprovalCode string     `json:"approval_code" form:"approval_code"` // 审批定义Code
	DepartmentId string     `json:"department_id" form:"department_id"` // 部门ID
	StartTime    *time.Time `json:"start_time" form:"start_time"`       // 开始时间
	EndTime      *time.Time `json:"end_time" form:"end_time"`           // 结束时间
	GroupBy      string     `json:"group_by" form:"group_by"`           // 分组字段 status/department/month
	ContractType string     `json:"contract_type" form:"contract_type"` // 合同类型
}

// ContractUpdateRequest 合同更新请求
type ContractUpdateRequest struct {
	ID             uint    `json:"id" binding:"required"` // 主键ID
	ContractType   string  `json:"contract_type"`         // 合同类型
	ContractTitle  string  `json:"contract_title"`        // 合同标题
	ContractAmount float64 `json:"contract_amount"`       // 合同金额
	Currency       string  `json:"currency"`              // 币种
}

// ContractDeleteRequest 合同删除请求
type ContractDeleteRequest struct {
	IDs []uint `json:"ids" binding:"required"` // 主键ID列表
}

// ContractCommentRequest 合同评论查询请求
type ContractCommentRequest struct {
	request.PageInfo

	ContractId uint   `json:"contract_id" form:"contract_id" binding:"required"` // 合同ID
	UserId     string `json:"user_id" form:"user_id"`                            // 用户ID
	OpenId     string `json:"open_id" form:"open_id"`                            // OpenID
}

// ContractTaskRequest 合同任务查询请求
type ContractTaskRequest struct {
	request.PageInfo

	ContractId uint   `json:"contract_id" form:"contract_id" binding:"required"` // 合同ID
	Status     string `json:"status" form:"status"`                              // 任务状态
	Type       string `json:"type" form:"type"`                                  // 任务类型
	UserId     string `json:"user_id" form:"user_id"`                            // 处理人ID
}

// ContractTimelineRequest 合同时间线查询请求
type ContractTimelineRequest struct {
	request.PageInfo

	ContractId uint   `json:"contract_id" form:"contract_id" binding:"required"` // 合同ID
	Type       string `json:"type" form:"type"`                                  // 操作类型
	UserId     string `json:"user_id" form:"user_id"`                            // 操作人ID
}

// ContractFileRequest 合同文件查询请求
type ContractFileRequest struct {
	request.PageInfo

	ContractId uint   `json:"contract_id" form:"contract_id" binding:"required"` // 合同ID
	CommentId  uint   `json:"comment_id" form:"comment_id"`                      // 评论ID
	Type       string `json:"type" form:"type"`                                  // 文件类型
}

// ContractSyncLogRequest 合同同步日志查询请求
type ContractSyncLogRequest struct {
	request.PageInfo

	ApprovalCode string     `json:"approval_code" form:"approval_code"` // 审批定义Code
	SyncType     string     `json:"sync_type" form:"sync_type"`         // 同步类型
	Status       string     `json:"status" form:"status"`               // 同步状态
	StartTime    *time.Time `json:"start_time" form:"start_time"`       // 开始时间
	EndTime      *time.Time `json:"end_time" form:"end_time"`           // 结束时间
}

// ContractFormParseRequest 合同表单解析请求
type ContractFormParseRequest struct {
	InstanceCode string `json:"instance_code" binding:"required"` // 实例代码
	FormData     string `json:"form_data" binding:"required"`     // 表单JSON数据
}

// ContractApprovalRequest 合同审批流程查询请求
type ContractApprovalRequest struct {
	ApprovalCode string `json:"approval_code" form:"approval_code" binding:"required"` // 审批定义Code
}

// ContractDashboardRequest 合同仪表板数据请求
type ContractDashboardRequest struct {
	ApprovalCode string     `json:"approval_code" form:"approval_code"` // 审批定义Code
	DepartmentId string     `json:"department_id" form:"department_id"` // 部门ID
	StartTime    *time.Time `json:"start_time" form:"start_time"`       // 开始时间
	EndTime      *time.Time `json:"end_time" form:"end_time"`           // 结束时间
	TimeRange    string     `json:"time_range" form:"time_range"`       // 时间范围 today/week/month/quarter/year
}
