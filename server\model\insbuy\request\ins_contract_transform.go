package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// BatchTransformReq 批量转换请求
type BatchTransformReq struct {
	ApprovalCode string `json:"approval_code" binding:"required" comment:"审批代码"`
	OperatorId   uint   `json:"operator_id" binding:"required" comment:"操作人ID"`
	OperatorName string `json:"operator_name" binding:"required" comment:"操作人姓名"`
}

// StagingSearchReq 转换结果搜索请求
type StagingSearchReq struct {
	request.PageInfo
	
	// 搜索条件
	SourceContractId   *uint  `json:"source_contract_id,omitempty" comment:"源合同ID"`
	SourceInstanceCode string `json:"source_instance_code,omitempty" comment:"源实例代码"`
	SourceApprovalCode string `json:"source_approval_code,omitempty" comment:"源审批代码"`
	TransformStatus    string `json:"transform_status,omitempty" comment:"转换状态"`
	AuditStatus        string `json:"audit_status,omitempty" comment:"审核状态"`
	StorageStatus      string `json:"storage_status,omitempty" comment:"存储状态"`
	ReportMonth        string `json:"report_month,omitempty" comment:"管报月份"`
	PaymentEntity      string `json:"payment_entity,omitempty" comment:"付款主体"`
	
	// 时间范围
	TransformTimeStart string `json:"transform_time_start,omitempty" comment:"转换开始时间"`
	TransformTimeEnd   string `json:"transform_time_end,omitempty" comment:"转换结束时间"`
	AuditTimeStart     string `json:"audit_time_start,omitempty" comment:"审核开始时间"`
	AuditTimeEnd       string `json:"audit_time_end,omitempty" comment:"审核结束时间"`
}

// BatchAuditReq 批量审核请求
type BatchAuditReq struct {
	StagingIds  []uint `json:"staging_ids" binding:"required" comment:"中间表ID列表"`
	AuditStatus string `json:"audit_status" binding:"required" comment:"审核状态"`
	AuditorId   uint   `json:"auditor_id" binding:"required" comment:"审核人ID"`
	AuditNote   string `json:"audit_note,omitempty" comment:"审核备注"`
}

// StoreToSourceReq 存储到源数据请求
type StoreToSourceReq struct {
	StagingIds []uint `json:"staging_ids" binding:"required" comment:"中间表ID列表"`
}

// UpdateStagingReq 更新转换记录请求
type UpdateStagingReq struct {
	ID uint `json:"id" binding:"required" comment:"记录ID"`
	
	// 可更新的标准化字段
	SerialNumber         *int    `json:"serial_number,omitempty" comment:"序号"`
	CompletionTime       *string `json:"completion_time,omitempty" comment:"完成时间"`
	ReportMonth          *string `json:"report_month,omitempty" comment:"管报月份"`
	Title                *string `json:"title,omitempty" comment:"标题"`
	PaymentEntity        *string `json:"payment_entity,omitempty" comment:"付款主体"`
	PaymentReason        *string `json:"payment_reason,omitempty" comment:"付款事由"`
	BusinessType         *string `json:"business_type,omitempty" comment:"业务类型"`
	ContractAmount       *string `json:"contract_amount,omitempty" comment:"合同签约金额"`
	ContractPaidAmount   *string `json:"contract_paid_amount,omitempty" comment:"合同已付金额"`
	CurrentRequestAmount *string `json:"current_request_amount,omitempty" comment:"本次请款金额"`
	PendingAmount        *string `json:"pending_amount,omitempty" comment:"待付款金额"`
	ReportConfirmAmount  *string `json:"report_confirm_amount,omitempty" comment:"管报确认金额"`
	TaxRate              *string `json:"tax_rate,omitempty" comment:"税率"`
	AmountExcludeTax     *string `json:"amount_exclude_tax,omitempty" comment:"不含税金额"`
	ExpenseCategory      *string `json:"expense_category,omitempty" comment:"费用类别"`
	IncludeInReport      *string `json:"include_in_report,omitempty" comment:"是否纳入管报"`
	AccountName          *string `json:"account_name,omitempty" comment:"户名"`
	ReportEntity         *string `json:"report_entity,omitempty" comment:"管报主体"`
	ReportEntityDetail   *string `json:"report_entity_detail,omitempty" comment:"管报主体明细"`
	ReportRegion         *string `json:"report_region,omitempty" comment:"管报区域"`
	InitiatorName        *string `json:"initiator_name,omitempty" comment:"发起人姓名"`
	InitiatorDepartment  *string `json:"initiator_department,omitempty" comment:"发起人部门"`
	Department           *string `json:"department,omitempty" comment:"部门"`
	
	// 明细相关字段
	ExpenseType      *string `json:"expense_type,omitempty" comment:"费用类型"`
	ExpenseLocation  *string `json:"expense_location,omitempty" comment:"费用地点"`
	ExpenseDateRange *string `json:"expense_date_range,omitempty" comment:"费用日期区间"`
	InvoiceType      *string `json:"invoice_type,omitempty" comment:"发票类型"`
}

// BatchSearchReq 批次搜索请求
type BatchSearchReq struct {
	request.PageInfo
	
	BatchNo       string `json:"batch_no,omitempty" comment:"批次号"`
	ApprovalCode  string `json:"approval_code,omitempty" comment:"审批代码"`
	OperationType string `json:"operation_type,omitempty" comment:"操作类型"`
	Status        string `json:"status,omitempty" comment:"批次状态"`
	OperatorId    *uint  `json:"operator_id,omitempty" comment:"操作人ID"`
	
	// 时间范围
	StartTimeBegin string `json:"start_time_begin,omitempty" comment:"开始时间范围-开始"`
	StartTimeEnd   string `json:"start_time_end,omitempty" comment:"开始时间范围-结束"`
}

// LogSearchReq 日志搜索请求
type LogSearchReq struct {
	request.PageInfo
	
	BatchNo          string `json:"batch_no,omitempty" comment:"批次号"`
	StagingId        *uint  `json:"staging_id,omitempty" comment:"中间表ID"`
	SourceContractId *uint  `json:"source_contract_id,omitempty" comment:"源合同ID"`
	OperationType    string `json:"operation_type,omitempty" comment:"操作类型"`
	OperationStatus  string `json:"operation_status,omitempty" comment:"操作状态"`
	OperatorId       *uint  `json:"operator_id,omitempty" comment:"操作人ID"`
	
	// 时间范围
	CreatedAtStart string `json:"created_at_start,omitempty" comment:"创建时间开始"`
	CreatedAtEnd   string `json:"created_at_end,omitempty" comment:"创建时间结束"`
}
