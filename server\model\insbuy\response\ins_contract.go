package response

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
)

// ContractListResponse 合同列表响应
type ContractListResponse struct {
	List     []ContractItem `json:"list"`
	Total    int64          `json:"total"`
	Page     int            `json:"page"`
	PageSize int            `json:"page_size"`
}

// ContractItem 合同列表项
type ContractItem struct {
	ID             uint       `json:"id"`
	ApprovalCode   string     `json:"approval_code"`
	ApprovalName   string     `json:"approval_name"`
	InstanceCode   string     `json:"instance_code"`
	SerialNumber   string     `json:"serial_number"`
	Uuid           string     `json:"uuid"`
	Status         string     `json:"status"`
	Reverted       bool       `json:"reverted"`
	StartTime      *time.Time `json:"start_time"`
	EndTime        *time.Time `json:"end_time"`
	UserId         string     `json:"user_id"`
	OpenId         string     `json:"open_id"`
	DepartmentId   string     `json:"department_id"`
	ContractType   string     `json:"contract_type"`
	ContractTitle  string     `json:"contract_title"`
	ContractAmount float64    `json:"contract_amount"`
	Currency       string     `json:"currency"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`

	// 统计信息
	CommentCount  int `json:"comment_count"`
	TaskCount     int `json:"task_count"`
	TimelineCount int `json:"timeline_count"`
	FileCount     int `json:"file_count"`
}

// ContractDetailResponse 合同详情响应
type ContractDetailResponse struct {
	insbuy.InsContract

	// 关联数据
	Comments []ContractCommentItem  `json:"comments"`
	Tasks    []ContractTaskItem     `json:"tasks"`
	Timeline []ContractTimelineItem `json:"timeline"`
	Files    []ContractFileItem     `json:"files"`

	// 表单解析数据
	FormFields []ContractFormField `json:"form_fields"`
}

// ContractCommentItem 合同评论项
type ContractCommentItem struct {
	insbuy.InsContractComment
	Files []ContractFileItem `json:"files"`
}

// ContractTaskItem 合同任务项
type ContractTaskItem struct {
	insbuy.InsContractTask
}

// ContractTimelineItem 合同时间线项
type ContractTimelineItem struct {
	insbuy.InsContractTimeline
}

// ContractFileItem 合同文件项
type ContractFileItem struct {
	insbuy.InsContractFile
}

// ContractFormField 合同表单字段
type ContractFormField struct {
	ID    string      `json:"id"`
	Name  string      `json:"name"`
	Type  string      `json:"type"`
	Value interface{} `json:"value"`
	Label string      `json:"label"`
}

// ContractSyncResponse 合同同步响应
type ContractSyncResponse struct {
	ApprovalCode string `json:"approval_code"`
	SyncType     string `json:"sync_type"`
	Status       string `json:"status"`
	Message      string `json:"message"`
	RecordCount  int    `json:"record_count"`
	ErrorCount   int    `json:"error_count"`
	StartTime    string `json:"start_time"`
	EndTime      string `json:"end_time"`
	Duration     string `json:"duration"`
}

// ContractBatchSyncResponse 批量合同同步响应
type ContractBatchSyncResponse struct {
	TotalCount   int                    `json:"total_count"`
	SuccessCount int                    `json:"success_count"`
	ErrorCount   int                    `json:"error_count"`
	Results      []ContractSyncResponse `json:"results"`
	StartTime    string                 `json:"start_time"`
	EndTime      string                 `json:"end_time"`
	Duration     string                 `json:"duration"`
}

// ContractMultiSyncResponse 多审批代码同步响应
type ContractMultiSyncResponse struct {
	Success        bool                       `json:"success"`         // 整体同步是否成功
	TotalCodes     int                        `json:"total_codes"`     // 总审批代码数量
	SuccessCodes   int                        `json:"success_codes"`   // 成功同步的审批代码数量
	FailedCodes    int                        `json:"failed_codes"`    // 失败的审批代码数量
	TotalRecords   int                        `json:"total_records"`   // 总记录数
	NewRecords     int                        `json:"new_records"`     // 新增记录数
	UpdatedRecords int                        `json:"updated_records"` // 更新记录数
	FailedRecords  int                        `json:"failed_records"`  // 失败记录数
	Results        []ContractSyncDetailResult `json:"results"`         // 详细同步结果
	StartTime      string                     `json:"start_time"`      // 同步开始时间
	EndTime        string                     `json:"end_time"`        // 同步结束时间
	Duration       string                     `json:"duration"`        // 同步耗时
	ErrorMsg       string                     `json:"error_msg"`       // 错误信息
}

// ContractSyncDetailResult 合同同步详细结果
type ContractSyncDetailResult struct {
	ApprovalCode   string                   `json:"approval_code"`   // 审批代码
	Success        bool                     `json:"success"`         // 是否成功
	TotalPages     int                      `json:"total_pages"`     // 总页数
	ProcessedPages int                      `json:"processed_pages"` // 已处理页数
	TotalRecords   int                      `json:"total_records"`   // 总记录数
	NewRecords     int                      `json:"new_records"`     // 新增记录数
	UpdatedRecords int                      `json:"updated_records"` // 更新记录数
	FailedRecords  int                      `json:"failed_records"`  // 失败记录数
	StartTime      string                   `json:"start_time"`      // 开始时间
	EndTime        string                   `json:"end_time"`        // 结束时间
	Duration       string                   `json:"duration"`        // 耗时
	ErrorMsg       string                   `json:"error_msg"`       // 错误信息
	PageResults    []ContractSyncPageResult `json:"page_results"`    // 分页结果详情
}

// ContractSyncPageResult 合同同步分页结果
type ContractSyncPageResult struct {
	PageToken     string   `json:"page_token"`      // 分页标记
	PageSize      int      `json:"page_size"`       // 页面大小
	RecordCount   int      `json:"record_count"`    // 记录数量
	SuccessCount  int      `json:"success_count"`   // 成功数量
	FailedCount   int      `json:"failed_count"`    // 失败数量
	HasMore       bool     `json:"has_more"`        // 是否还有更多
	NextPageToken string   `json:"next_page_token"` // 下一页标记
	ProcessTime   string   `json:"process_time"`    // 处理时间
	ErrorMessages []string `json:"error_messages"`  // 错误信息列表
}

// ContractStatisticsResponse 合同统计响应
type ContractStatisticsResponse struct {
	TotalCount      int                      `json:"total_count"`
	StatusStats     []ContractStatusStat     `json:"status_stats"`
	DepartmentStats []ContractDepartmentStat `json:"department_stats"`
	MonthlyStats    []ContractMonthlyStat    `json:"monthly_stats"`
	AmountStats     ContractAmountStat       `json:"amount_stats"`
}

// ContractStatusStat 合同状态统计
type ContractStatusStat struct {
	Status string `json:"status"`
	Count  int    `json:"count"`
	Ratio  string `json:"ratio"`
}

// ContractDepartmentStat 合同部门统计
type ContractDepartmentStat struct {
	DepartmentId   string  `json:"department_id"`
	DepartmentName string  `json:"department_name"`
	Count          int     `json:"count"`
	Amount         float64 `json:"amount"`
	Ratio          string  `json:"ratio"`
}

// ContractMonthlyStat 合同月度统计
type ContractMonthlyStat struct {
	Month  string  `json:"month"`
	Count  int     `json:"count"`
	Amount float64 `json:"amount"`
}

// ContractAmountStat 合同金额统计
type ContractAmountStat struct {
	TotalAmount   float64 `json:"total_amount"`
	AverageAmount float64 `json:"average_amount"`
	MaxAmount     float64 `json:"max_amount"`
	MinAmount     float64 `json:"min_amount"`
}

// ContractExportResponse 合同导出响应
type ContractExportResponse struct {
	FileName    string `json:"file_name"`
	FileUrl     string `json:"file_url"`
	FileSize    int64  `json:"file_size"`
	RecordCount int    `json:"record_count"`
	ExportTime  string `json:"export_time"`
}

// ContractDashboardResponse 合同仪表板响应
type ContractDashboardResponse struct {
	// 概览数据
	Overview ContractOverview `json:"overview"`

	// 趋势数据
	TrendData []ContractTrendData `json:"trend_data"`

	// 状态分布
	StatusDistribution []ContractStatusStat `json:"status_distribution"`

	// 部门分布
	DepartmentDistribution []ContractDepartmentStat `json:"department_distribution"`

	// 最近活动
	RecentActivities []ContractActivity `json:"recent_activities"`

	// 待处理任务
	PendingTasks []ContractPendingTask `json:"pending_tasks"`
}

// ContractOverview 合同概览
type ContractOverview struct {
	TotalCount    int     `json:"total_count"`
	PendingCount  int     `json:"pending_count"`
	ApprovedCount int     `json:"approved_count"`
	RejectedCount int     `json:"rejected_count"`
	TotalAmount   float64 `json:"total_amount"`
	AverageAmount float64 `json:"average_amount"`
	GrowthRate    string  `json:"growth_rate"`
	AmountGrowth  string  `json:"amount_growth"`
}

// ContractTrendData 合同趋势数据
type ContractTrendData struct {
	Date   string  `json:"date"`
	Count  int     `json:"count"`
	Amount float64 `json:"amount"`
}

// ContractActivity 合同活动
type ContractActivity struct {
	ID           uint      `json:"id"`
	InstanceCode string    `json:"instance_code"`
	Title        string    `json:"title"`
	Action       string    `json:"action"`
	UserId       string    `json:"user_id"`
	UserName     string    `json:"user_name"`
	CreateTime   time.Time `json:"create_time"`
}

// ContractPendingTask 待处理任务
type ContractPendingTask struct {
	ID           uint      `json:"id"`
	InstanceCode string    `json:"instance_code"`
	Title        string    `json:"title"`
	TaskType     string    `json:"task_type"`
	NodeName     string    `json:"node_name"`
	AssigneeId   string    `json:"assignee_id"`
	AssigneeName string    `json:"assignee_name"`
	CreateTime   time.Time `json:"create_time"`
	DueTime      time.Time `json:"due_time"`
	Priority     string    `json:"priority"`
}

// ContractSyncLogResponse 合同同步日志响应
type ContractSyncLogResponse struct {
	List     []ContractSyncLogItem `json:"list"`
	Total    int64                 `json:"total"`
	Page     int                   `json:"page"`
	PageSize int                   `json:"page_size"`
}

// ContractSyncLogItem 合同同步日志项
type ContractSyncLogItem struct {
	insbuy.InsContractSyncLog
}

// ContractFormParseResponse 合同表单解析响应
type ContractFormParseResponse struct {
	InstanceCode string                 `json:"instance_code"`
	Fields       []ContractFormField    `json:"fields"`
	ParsedData   map[string]interface{} `json:"parsed_data"`
}

// ContractApprovalResponse 合同审批流程响应
type ContractApprovalResponse struct {
	ApprovalCode string                 `json:"approval_code"`
	ApprovalName string                 `json:"approval_name"`
	Nodes        []ContractApprovalNode `json:"nodes"`
	Flows        []ContractApprovalFlow `json:"flows"`
}

// ContractApprovalNode 合同审批节点
type ContractApprovalNode struct {
	NodeId   string `json:"node_id"`
	NodeName string `json:"node_name"`
	NodeType string `json:"node_type"`
	Position int    `json:"position"`
}

// ContractApprovalFlow 合同审批流程
type ContractApprovalFlow struct {
	FromNode  string `json:"from_node"`
	ToNode    string `json:"to_node"`
	Condition string `json:"condition"`
}
