package response

import (
	"time"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"gorm.io/datatypes"
)

// TransformResultResponse 转换结果响应
type TransformResultResponse struct {
	Success       bool                                    `json:"success"`
	MainRecord    *ContractTransformStagingItem           `json:"main_record"`
	DetailRecords []*ContractTransformStagingItem         `json:"detail_records"`
	Errors        []string                                `json:"errors"`
	Warnings      []string                                `json:"warnings"`
}

// ContractTransformStagingItem 合同转换中间表项
type ContractTransformStagingItem struct {
	insbuy.InsContractTransformStaging
	
	// 关联数据
	SourceContractTitle string `json:"source_contract_title,omitempty"`
	AuditorName         string `json:"auditor_name,omitempty"`
	
	// 统计信息
	ProcessingDuration string `json:"processing_duration,omitempty"` // 处理耗时描述
}

// BatchTransformResponse 批量转换响应
type BatchTransformResponse struct {
	BatchNo     string `json:"batch_no"`
	BatchName   string `json:"batch_name"`
	TotalCount  int    `json:"total_count"`
	Status      string `json:"status"`
	Message     string `json:"message"`
}

// StagingListResponse 转换结果列表响应
type StagingListResponse struct {
	List  []ContractTransformStagingItem `json:"list"`
	Total int64                          `json:"total"`
}

// BatchItem 批次项
type BatchItem struct {
	insbuy.InsContractTransformBatch
	
	// 计算字段
	ProcessingDuration string  `json:"processing_duration,omitempty"` // 处理耗时
	SuccessRate        float64 `json:"success_rate,omitempty"`        // 成功率
	ProgressPercent    float64 `json:"progress_percent,omitempty"`    // 进度百分比
}

// BatchListResponse 批次列表响应
type BatchListResponse struct {
	List  []BatchItem `json:"list"`
	Total int64       `json:"total"`
}

// LogItem 日志项
type LogItem struct {
	insbuy.InsContractTransformLog
	
	// 关联数据
	SourceContractTitle string `json:"source_contract_title,omitempty"`
	ProcessingDuration  string `json:"processing_duration,omitempty"` // 处理耗时描述
}

// LogListResponse 日志列表响应
type LogListResponse struct {
	List  []LogItem `json:"list"`
	Total int64     `json:"total"`
}

// BatchDetailResponse 批次详情响应
type BatchDetailResponse struct {
	BatchItem
	
	// 详细统计
	StatusDistribution map[string]int `json:"status_distribution"` // 状态分布
	ErrorSummary       []ErrorSummary `json:"error_summary"`       // 错误汇总
	ProcessingSteps    []ProcessStep  `json:"processing_steps"`    // 处理步骤
}

// ErrorSummary 错误汇总
type ErrorSummary struct {
	ErrorType    string `json:"error_type"`    // 错误类型
	ErrorMessage string `json:"error_message"` // 错误信息
	Count        int    `json:"count"`         // 出现次数
	SampleIds    []uint `json:"sample_ids"`    // 样本ID
}

// ProcessStep 处理步骤
type ProcessStep struct {
	StepName    string    `json:"step_name"`    // 步骤名称
	Status      string    `json:"status"`       // 状态
	StartTime   time.Time `json:"start_time"`   // 开始时间
	EndTime     *time.Time `json:"end_time"`    // 结束时间
	Duration    string    `json:"duration"`     // 耗时
	Description string    `json:"description"`  // 描述
}

// StagingDetailResponse 转换记录详情响应
type StagingDetailResponse struct {
	ContractTransformStagingItem
	
	// 操作历史
	OperationHistory []LogItem `json:"operation_history"`
	
	// 原始表单数据解析
	FormFields []FormField `json:"form_fields"`
	
	// 验证结果
	ValidationResult ValidationResult `json:"validation_result"`
}

// FormField 表单字段
type FormField struct {
	FieldName    string      `json:"field_name"`    // 字段名
	FieldLabel   string      `json:"field_label"`   // 字段标签
	FieldType    string      `json:"field_type"`    // 字段类型
	OriginalValue interface{} `json:"original_value"` // 原始值
	MappedValue   interface{} `json:"mapped_value"`   // 映射值
	MappingRule   string      `json:"mapping_rule"`   // 映射规则
}

// ValidationResult 验证结果
type ValidationResult struct {
	IsValid  bool              `json:"is_valid"`  // 是否有效
	Errors   []ValidationError `json:"errors"`    // 错误列表
	Warnings []ValidationError `json:"warnings"`  // 警告列表
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`   // 字段名
	Message string `json:"message"` // 错误信息
	Code    string `json:"code"`    // 错误代码
}

// StatisticsResponse 统计响应
type StatisticsResponse struct {
	// 总体统计
	TotalContracts      int `json:"total_contracts"`       // 总合同数
	TransformedCount    int `json:"transformed_count"`     // 已转换数量
	PendingAuditCount   int `json:"pending_audit_count"`   // 待审核数量
	ApprovedCount       int `json:"approved_count"`        // 已审核通过数量
	RejectedCount       int `json:"rejected_count"`        // 审核驳回数量
	StoredCount         int `json:"stored_count"`          // 已存储数量
	
	// 按月统计
	MonthlyStats []MonthlyStatItem `json:"monthly_stats"`
	
	// 按审批类型统计
	ApprovalTypeStats []ApprovalTypeStatItem `json:"approval_type_stats"`
	
	// 处理效率统计
	EfficiencyStats EfficiencyStats `json:"efficiency_stats"`
}

// MonthlyStatItem 月度统计项
type MonthlyStatItem struct {
	Month            string `json:"month"`             // 月份
	ContractCount    int    `json:"contract_count"`    // 合同数量
	TransformedCount int    `json:"transformed_count"` // 转换数量
	ApprovedCount    int    `json:"approved_count"`    // 审核通过数量
	StoredCount      int    `json:"stored_count"`      // 存储数量
}

// ApprovalTypeStatItem 审批类型统计项
type ApprovalTypeStatItem struct {
	ApprovalCode     string `json:"approval_code"`     // 审批代码
	ApprovalName     string `json:"approval_name"`     // 审批名称
	ContractCount    int    `json:"contract_count"`    // 合同数量
	TransformedCount int    `json:"transformed_count"` // 转换数量
	SuccessRate      float64 `json:"success_rate"`     // 成功率
}

// EfficiencyStats 效率统计
type EfficiencyStats struct {
	AvgTransformTime float64 `json:"avg_transform_time"` // 平均转换时间(秒)
	AvgAuditTime     float64 `json:"avg_audit_time"`     // 平均审核时间(秒)
	TotalProcessTime float64 `json:"total_process_time"` // 总处理时间(秒)
	Throughput       float64 `json:"throughput"`         // 吞吐量(条/小时)
}
