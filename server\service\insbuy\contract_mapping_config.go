package insbuy

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
	"gopkg.in/yaml.v3"
)

// MappingConfigManager 映射配置管理器
type MappingConfigManager struct {
	configPath string
	rules      map[string]*FieldMappingRule
}

// NewMappingConfigManager 创建映射配置管理器
func NewMappingConfigManager(configPath string) *MappingConfigManager {
	return &MappingConfigManager{
		configPath: configPath,
		rules:      make(map[string]*FieldMappingRule),
	}
}

// LoadFromFile 从文件加载映射规则
func (m *MappingConfigManager) LoadFromFile(filename string) error {
	ctx, logger := global.Log4Task(context.Background(), "LoadMappingRulesFromFile",
		zap.String("filename", filename),
	)
	_ = ctx

	logger.Info("开始加载映射规则文件")

	// 构建完整文件路径
	fullPath := filepath.Join(m.configPath, filename)

	// 读取文件内容
	content, err := ioutil.ReadFile(fullPath)
	if err != nil {
		logger.Error("读取映射规则文件失败", zap.Error(err))
		return fmt.Errorf("读取映射规则文件失败: %w", err)
	}
	// 根据文件扩展名选择解析方式
	ext := filepath.Ext(filename)
	var rules map[string]*FieldMappingRule

	switch ext {
	case ".json":
		err = json.Unmarshal(content, &rules)
	case ".yaml", ".yml":
		err = yaml.Unmarshal(content, &rules)
	default:
		return fmt.Errorf("不支持的文件格式: %s", ext)
	}
	if err != nil {
		logger.Error("解析映射规则文件失败", zap.Error(err))
		return fmt.Errorf("解析映射规则文件失败: %w", err)
	}

	// 合并到现有规则中
	for code, rule := range rules {
		if rule != nil {
			m.rules[code] = rule
			logger.Info("成功加载映射规则",
				zap.String("approval_code", code),
				zap.String("approval_name", rule.ApprovalName),
				zap.Int("field_mappings_count", len(rule.FieldMappings)),
			)
		} else {
			logger.Warn("映射规则为空", zap.String("approval_code", code))
		}
	}

	logger.Info("映射规则文件加载完成",
		zap.Int("loaded_rules", len(rules)),
		zap.Int("total_rules", len(m.rules)),
	)

	return nil
}

// LoadFromDirectory 从目录加载所有映射规则文件
func (m *MappingConfigManager) LoadFromDirectory() error {
	ctx, logger := global.Log4Task(context.Background(), "LoadMappingRulesFromDirectory",
		zap.String("config_path", m.configPath),
	)
	_ = ctx

	logger.Info("开始加载映射规则目录")

	// 读取目录中的所有文件
	files, err := ioutil.ReadDir(m.configPath)
	if err != nil {
		logger.Error("读取映射规则目录失败", zap.Error(err))
		return fmt.Errorf("读取映射规则目录失败: %w", err)
	}

	loadedCount := 0
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		// 只处理支持的文件格式
		ext := filepath.Ext(file.Name())
		if ext != ".json" && ext != ".yaml" && ext != ".yml" {
			continue
		}

		if err := m.LoadFromFile(file.Name()); err != nil {
			logger.Warn("加载映射规则文件失败",
				zap.String("filename", file.Name()),
				zap.Error(err),
			)
			continue
		}

		loadedCount++
	}

	logger.Info("映射规则目录加载完成",
		zap.Int("loaded_files", loadedCount),
		zap.Int("total_rules", len(m.rules)),
	)

	return nil
}

// GetRule 获取指定审批代码的映射规则
func (m *MappingConfigManager) GetRule(approvalCode string) (*FieldMappingRule, bool) {
	rule, exists := m.rules[approvalCode]
	return rule, exists
}

// GetAllRules 获取所有映射规则
func (m *MappingConfigManager) GetAllRules() map[string]*FieldMappingRule {
	return m.rules
}

// AddRule 添加映射规则
func (m *MappingConfigManager) AddRule(approvalCode string, rule *FieldMappingRule) {
	m.rules[approvalCode] = rule
}

// GetConfigPath 获取配置路径
func (m *MappingConfigManager) GetConfigPath() string {
	return m.configPath
}

// SaveToFile 保存映射规则到文件
func (m *MappingConfigManager) SaveToFile(filename string) error {
	ctx, logger := global.Log4Task(context.Background(), "SaveMappingRulesToFile",
		zap.String("filename", filename),
	)
	_ = ctx

	logger.Info("开始保存映射规则到文件")

	// 构建完整文件路径
	fullPath := filepath.Join(m.configPath, filename)

	// 根据文件扩展名选择序列化方式
	ext := filepath.Ext(filename)
	var content []byte
	var err error

	switch ext {
	case ".json":
		content, err = json.MarshalIndent(m.rules, "", "  ")
	case ".yaml", ".yml":
		content, err = yaml.Marshal(m.rules)
	default:
		return fmt.Errorf("不支持的文件格式: %s", ext)
	}

	if err != nil {
		logger.Error("序列化映射规则失败", zap.Error(err))
		return fmt.Errorf("序列化映射规则失败: %w", err)
	}

	// 写入文件
	if err := ioutil.WriteFile(fullPath, content, 0644); err != nil {
		logger.Error("写入映射规则文件失败", zap.Error(err))
		return fmt.Errorf("写入映射规则文件失败: %w", err)
	}

	logger.Info("映射规则保存完成",
		zap.String("file_path", fullPath),
		zap.Int("rule_count", len(m.rules)),
	)

	return nil
}

// ValidateRule 验证映射规则的有效性
func (m *MappingConfigManager) ValidateRule(rule *FieldMappingRule) []string {
	var errors []string

	// 检查基本字段
	if rule.ApprovalCode == "" {
		errors = append(errors, "审批代码不能为空")
	}

	if rule.ApprovalName == "" {
		errors = append(errors, "审批名称不能为空")
	}

	if len(rule.FieldMappings) == 0 {
		errors = append(errors, "字段映射不能为空")
	}

	// 检查字段映射配置
	for fieldName, config := range rule.FieldMappings {
		if config.SourcePath == "" {
			errors = append(errors, fmt.Sprintf("字段 %s 的源路径不能为空", fieldName))
		}

		if config.TargetField == "" {
			errors = append(errors, fmt.Sprintf("字段 %s 的目标字段不能为空", fieldName))
		}

		if config.DataType == "" {
			errors = append(errors, fmt.Sprintf("字段 %s 的数据类型不能为空", fieldName))
		}

		// 验证数据类型
		validTypes := []string{"string", "int", "float", "bool", "time", "array"}
		isValidType := false
		for _, validType := range validTypes {
			if config.DataType == validType {
				isValidType = true
				break
			}
		}
		if !isValidType {
			errors = append(errors, fmt.Sprintf("字段 %s 的数据类型 %s 无效", fieldName, config.DataType))
		}
	}

	// 检查必填字段是否在字段映射中存在
	for _, requiredField := range rule.RequiredFields {
		if _, exists := rule.FieldMappings[requiredField]; !exists {
			errors = append(errors, fmt.Sprintf("必填字段 %s 在字段映射中不存在", requiredField))
		}
	}

	return errors
}

// GenerateTemplateRule 生成模板映射规则
func (m *MappingConfigManager) GenerateTemplateRule(approvalCode, approvalName string) *FieldMappingRule {
	return &FieldMappingRule{
		ApprovalCode: approvalCode,
		ApprovalName: approvalName,
		FieldMappings: map[string]FieldConfig{
			"application_number": {
				SourcePath:  "basic.instance_code",
				TargetField: "application_number",
				DataType:    "string",
				Required:    true,
				Description: "申请编号",
			},
			"title": {
				SourcePath:  "basic.approval_name",
				TargetField: "title",
				DataType:    "string",
				Required:    true,
				Description: "标题",
			},
			"application_status": {
				SourcePath:  "basic.status",
				TargetField: "application_status",
				DataType:    "string",
				Required:    true,
				Description: "申请状态",
			},
			"initiate_time": {
				SourcePath:  "basic.start_time",
				TargetField: "initiate_time",
				DataType:    "time",
				Required:    true,
				Description: "发起时间",
			},
			"initiator_user_id": {
				SourcePath:  "basic.user_id",
				TargetField: "initiator_user_id",
				DataType:    "string",
				Required:    true,
				Description: "发起人User ID",
			},
		},
		DefaultValues: map[string]interface{}{
			"data_version": "1.0",
		},
		RequiredFields: []string{
			"application_number",
			"title",
			"application_status",
			"initiate_time",
			"initiator_user_id",
		},
		ValidationRules: []ValidationRule{
			{
				Field:     "application_number",
				Rule:      "required",
				Parameter: "",
				Message:   "申请编号不能为空",
			},
		},
	}
}
