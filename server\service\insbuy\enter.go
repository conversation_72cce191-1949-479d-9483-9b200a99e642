package insbuy

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/ebus"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/worktime"
)

type ServiceGroup struct {
	hasInited bool

	InsBookService
	InsStoreService
	InsSupplierService
	InsVipMemberService
	InsDeskService
	InsBookInService
	InsSalerService
	InsWarehouseService
	InsWarehouseImportLogService
	InsDepositService
	InsMicroService
	InsProductService
	InsSysService
	InsOrderInfoService
	InsPaymentService
	InsGiftRulesService
	InsRechargeRuleService
	InsMaterialService
	InsCostCardService
	InsImportService
	InsCurrentUserService
	HyReport HyReportService
	InsSysDevPrinterService
	InsWarehouseInoutLogService
	InsWarehouseApplyService
	InsWarehousePurchaseService
	InsWarehouseOutService
	InsWarehouseInService
	InsWarehouseBaseService
	InsWarehouseEmptiesService
	ExtPay InsExternalPayService
	InsAuditService
	InsActivityService
	InsSalerStoreService
	InsSalerCodeService
	InsGiftQuotaAssignUserService
	InsStoreTerminalService
	InsNotesService
	InsCouponService
	InsServiceFeeService
	InsQueue  InsQueueService
	Report    InsReportService
	EventsLog InsEventsLogService
	Wechat    InsWechatService
	Rz        InsReconciliation

	Export4Partner Export4PartnerService

	worktime.InsWorkTime

	RpcBookDesk      RpcInsBookDeskServer
	RpcBookDeskAdmin RpcInsBookDeskAdminServer

	ActBookDesk InsActBookDeskService

	ebus ebus.Event
	InsUserRegisterService
	EventTracking InsSysEventService

	InsConfigService
	InsContractService
	ContractTransformService
}

var gSrv *ServiceGroup

func (S *ServiceGroup) Init() (err error) {
	if S.hasInited {
		return nil
	}

	if err = S.InsSysService.Init(); err != nil {
		return
	}
	if err = S.InsOrderInfoService.Init(); err != nil {
		return
	}
	gSrv = S

	if err = S.ExtPay.Init(); err != nil {
		return
	}

	if err = S.Export4Partner.Init(); err != nil {
		return
	}

	if err = S.HyReport.Init(); err != nil {
		return
	}

	if err = S.ActBookDesk.Init(); err != nil {
		return
	}
	if err = S.InsMaterialService.Init(); err != nil {
		return
	}

	S.hasInited = true

	//服务订阅事件注册
	err = S.ebus.Init()
	if err != nil {
		return
	}
	return err
}
