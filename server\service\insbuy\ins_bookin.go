package insbuy

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/errno"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/consumedmodel"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/eventsmodel"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/productmodel"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	insbuyReq "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	insbuyResp "github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/cache"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/eventslog"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insdata"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insdata/datasource"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insdesk"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insreport"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insstore"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/ebus"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jgorm"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
	"github.com/gin-gonic/gin"
	"github.com/xtulnx/jkit-go/jtime"
	"go.uber.org/zap"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strings"
	"time"
)

type InsBookInService struct {
}

func (S *InsBookInService) GetAreaList(r insbuyReq.InsBookInAreaList) (res *response.InsBookInAreaList, err error) {
	ctx, uid, storeId := r.GetCtx(), r.GetUserID(), r.GetUintStoreId()
	dbStore, dbArea := query.InsStore, query.InsDeskArea
	res = &response.InsBookInAreaList{}
	var conds = []gen.Condition{
		dbStore.ID.IsNotNull(),
	}
	// 默认只看可见的区域
	conds = append(conds, dbArea.Status.In(int(insbuy.StatOk)))
	conds = append(conds, dbArea.StoreId.In(gSrv.GetStoreIdPermissions(uid, storeId)))
	err = dbArea.WithContext(ctx).
		Join(dbStore, dbArea.StoreId.EqCol(dbStore.ID)).
		Where(conds...).
		Select(
			dbArea.StoreId.As("store_id"),
			dbArea.ID.As("area_id"),
			dbArea.Name.As("area_name"),
			dbArea.Status.As("area_status"),
			dbArea.Type.As("area_type"),
		).Order(dbArea.ID).Limit(5000).Scan(&res.List)
	if err != nil {
		return nil, errno.QueryFailed.WithError(err)
	}
	return res, nil
}
func (S *InsBookInService) GetDeskStatusList(r insbuyReq.InsBookInDeskStatusList) (res *response.InsBookInDeskStatusList, err error) {
	ctx, uid, storeId := r.GetCtx(), r.GetUserID(), r.GetUintStoreId()
	dbStore, dbDeskStatus := query.InsStore, query.InsDeskStatus
	res = &response.InsBookInDeskStatusList{}
	var conds = []gen.Condition{
		dbStore.ID.IsNotNull(),
	}
	conds = append(conds, dbDeskStatus.Status.In(int(insbuy.StatOk)))
	conds = append(conds, dbDeskStatus.StoreId.Eq(gSrv.GetStoreIdPermissions(uid, storeId)))
	err = dbDeskStatus.WithContext(ctx).
		Join(dbStore, dbDeskStatus.StoreId.EqCol(dbStore.ID)).
		Where(conds...).
		Select(
			dbDeskStatus.Name.As("name"),
			dbDeskStatus.ID.As("status_id"),
			dbDeskStatus.StoreId.As("store_id"),
			dbDeskStatus.Status.As("status"),
			dbDeskStatus.IsSys.As("is_sys"),
			dbDeskStatus.Style.As("style"),
		).Order(dbDeskStatus.ID).Limit(5000).Scan(&res.List)
	if err != nil {
		return nil, errno.QueryFailed.WithError(err)
	}
	return res, nil
}

func (S *InsBookInService) GetDeskList(r insbuyReq.InsBookInDeskList) (res *response.InsBookInDeskList, err error) {
	ctx, uid, storeId := r.GetCtx(), r.GetUserID(), r.GetUintStoreId()
	var (
		dbDeskStatus   = query.InsDeskStatus
		dbDesk         = query.InsDesk
		dbDeskCategory = query.InsDeskCategory
		dbDeskOpen     = query.InsDeskOpen
		dbMember       = query.InsVipMember
		dbInsSaler     = query.SysUser
	)
	res = &response.InsBookInDeskList{}
	conds := []gen.Condition{
		dbDesk.StoreId.Eq(int(gSrv.GetStoreIdPermissions(uid, storeId))),
	}
	if r.AreaId > 0 {
		conds = append(conds, dbDesk.DeskAreaId.In(int(r.AreaId)))
	}
	if len(r.Status) > 0 && r.Status[0] != 0 {
		intSlice := make([]int, len(r.Status))
		for i, val := range r.Status {
			intSlice[i] = int(val)
		}
		conds = append(conds, dbDesk.DeskStatusId.In(intSlice...))
	}
	if r.Style != nil {
		conds = append(conds, dbDeskStatus.Style.Eq(*r.Style))
	}
	if r.SalesmanId > 0 {
		salesmanIds, err2 := gSrv.GetSubordinateSalesId(r.SalesmanId, storeId)
		if err2 != nil {
			err = err2
			return
		}
		if len(salesmanIds) > 0 {
			conds = append(conds,
				dbDeskOpen.Where(dbDeskOpen.SalesmanId.In(salesmanIds...)).Or(dbDesk.GiveAttr.Eq(insbuy.DGAPublic.ToInt())))
		}
	}
	formDesk := dbDesk.As("form_desk")
	do := dbDesk.WithContext(ctx).
		LeftJoin(dbDeskOpen, dbDesk.OpenDeskId.EqCol(dbDeskOpen.ID)).
		LeftJoin(dbMember, dbDeskOpen.MemberId.EqCol(dbMember.ID)).       //会员
		LeftJoin(dbInsSaler, dbDeskOpen.SalesmanId.EqCol(dbInsSaler.ID)). //销售
		LeftJoin(formDesk, dbDeskOpen.FormDeskId.EqCol(formDesk.ID)).
		Join(dbDeskCategory, dbDesk.DeskCategoryId.EqCol(dbDeskCategory.ID)).
		Join(dbDeskStatus, dbDesk.DeskStatusId.EqCol(dbDeskStatus.ID)).
		Where(conds...).Order(dbDesk.SortOrder).DO
	SelectAppend(&do,
		dbDesk.DeskName.As("desk_name"),
		formDesk.DeskName.As("form_desk_name"),
		dbDesk.DeskAreaId.As("area_id"),
		dbDesk.StoreId.As("store_id"),
		dbDesk.ID.As("desk_id"),
		dbDesk.DeskAttr,
		dbDeskStatus.Style,
		dbDesk.DeskStatusId.As("status"),
		dbDeskCategory.Name.As("desk_category_name"),
		dbDeskOpen.MinAmount.As("min_amount"),
		dbDeskOpen.OpTime.As("open_time"),
		dbMember.Viplevel.As("viplevel"),
		dbMember.UserName.As("member_name"),
		dbDesk.GiveAttr,
		dbDeskOpen.TempMemberName,
		dbDeskOpen.BookType,
		dbDeskOpen.BookArrivalTime,
		dbMember.ID.As("member_id"),
		dbMember.Code.As("member_code"),
		dbDeskOpen.ID.As("open_desk_id"),
		dbDeskOpen.BookAmount,
		dbDeskOpen.PeopleNum.As("people_num"),
		dbDeskOpen.CustomerSource,
		clause.Expr{
			SQL: "CASE " +
				"WHEN ? IS NULL AND ? IS NOT NULL " +
				"THEN ? " +
				"ELSE ? " +
				"END AS `remark`",
			Vars: []interface{}{
				dbDeskOpen.Remark.RawExpr(),
				dbDesk.Remark.RawExpr(),
				dbDesk.Remark.RawExpr(),
				dbDeskOpen.Remark.RawExpr(),
			},
			WithoutParentheses: false,
		},
		dbDeskOpen.TurnoverNum,
		dbInsSaler.ID.As("salesman_id"),
		dbInsSaler.NickName.As("salesman_name"),
		dbInsSaler.Username.As("job_number"), //工号
		dbDeskStatus.Color.As("status_color"),
		dbDeskOpen.RemarkExt,
	)
	err = do.Limit(5000).Scan(&res.List)
	if err != nil {
		return nil, errno.QueryFailed.WithError(err)
	}
	//获取所有openDeskId
	openDeskIds := make([]int, 0)
	uOpenDeskIds := make([]uint, 0)
	for _, val := range res.List {
		if val.OpenDeskId > 0 {
			openDeskIds = append(openDeskIds, int(val.OpenDeskId))
			uOpenDeskIds = append(uOpenDeskIds, val.OpenDeskId)
		}
	}
	//销售id
	salesmanIds := make([]uint, 0)
	for _, val := range res.List {
		if val.SalesmanId > 0 {
			salesmanIds = append(salesmanIds, val.SalesmanId)
		}
	}
	m1, e1 := insreport.DataUserInfo(ctx, query.Q, insreport.DataUserInfoParam{UserIds: salesmanIds})
	if e1 != nil {
		err = e1
		return
	}
	orderMap, err := S.GetOrderConsumed(openDeskIds)
	if err != nil {
		return
	}
	sumMap, err := S.GetTurnoverSumMap(insdesk.TurnoverParam{
		StoreId: storeId,
	})
	if err != nil {
		return
	}
	//订单绑定
	for i, val := range res.List {
		if order, ok := orderMap[int(val.OpenDeskId)]; ok {
			res.List[i].ExistOrder = true //是否存在订单
			res.List[i].DeskConsumed = order.DeskConsumed
		}
		res.List[i].TurnoverNum = sumMap[int(val.DeskId)]
		if val.MemberId == 0 {
			res.List[i].MemberName = val.TempMemberName
		}
		if org, ok := m1[val.SalesmanId]; ok {
			res.List[i].OrganizationName = org.OrgName
		}
	}
	return res, nil
}

// GetOrderListByOpenDeskId 根据opendeskId 获取order list
// todo::可提取封装为子查询
func (S *InsBookInService) GetOrderListByOpenDeskId(openDeskIds []int) (res []insbuyResp.GetOpenDeskOrderAmountRes, err error) {
	var (
		dbOrderInfo        = query.InsOrderInfo
		dbOrderInfoDetails = query.InsOrderInfoDetails
		dbUser             = query.SysUser
	)
	orderList := make([]insbuyResp.GetOpenDeskOrderAmountRes, 0)
	do := dbOrderInfo.Where(dbOrderInfo.OpenDeskId.In(openDeskIds...)).
		Where(dbOrderInfoDetails.DeletedAt.IsNull()).
		Join(dbOrderInfoDetails, dbOrderInfo.OrderId.EqCol(dbOrderInfoDetails.OrderId)).
		LeftJoin(dbUser, dbOrderInfoDetails.CashierId.EqCol(dbUser.ID)).DO
	S.OrderListSelect(&do)
	err = do.Group(dbOrderInfo.OpenDeskId, dbOrderInfoDetails.OrderId, dbOrderInfoDetails.ProductId).Scan(&orderList)
	if err != nil {
		return
	}
	res = orderList
	return
}

// OrderListSelect select
func (S *InsBookInService) OrderListSelect(do *gen.DO) {
	var (
		dbOrderInfo        = query.InsOrderInfo
		dbOrderInfoDetails = query.InsOrderInfoDetails
		dbUser             = query.SysUser
	)
	SelectAppend(do,
		dbOrderInfoDetails.CashierId,
		dbUser.NickName.As("cashier_name"),
		dbOrderInfo.OpenDeskId.As("open_desk_id"),
		dbOrderInfo.OrderSn.As("order_sn"),
		clause.Expr{
			SQL: "IFNULL(SUM((? + ?) * ?),0) as consumed",
			Vars: []interface{}{
				dbOrderInfoDetails.Nums.RawExpr(),
				dbOrderInfoDetails.ReturnNum.RawExpr(),
				dbOrderInfoDetails.ProductPrice.RawExpr(),
			},
			WithoutParentheses: false,
		},
		clause.Expr{
			SQL: `
				IFNULL(
						SUM(
							CASE
							WHEN ? = 0 THEN
								(
									? + ?
								) * ?
							ELSE
								0
							END
						),
						0
					) as wait_amount
				`,
			Vars: []interface{}{
				dbOrderInfoDetails.IsPay.RawExpr(),
				dbOrderInfoDetails.Nums.RawExpr(),
				dbOrderInfoDetails.ReturnNum.RawExpr(),
				dbOrderInfoDetails.ProductPrice.RawExpr(),
			},
			WithoutParentheses: false,
		},
		clause.Expr{
			SQL: `
				IFNULL(
						SUM(
							CASE
							WHEN ? and ? THEN
								?
							ELSE
								0
							END
						),
						0
					) as paid_amount
				`,
			Vars: []interface{}{
				dbOrderInfoDetails.IsPay.Eq(1).RawExpr(),
				dbOrderInfoDetails.OrderType.Eq(insbuy.OTProduct.ToInt()).RawExpr(),
				dbOrderInfoDetails.RealTotalPrice.RawExpr(), //实际支付金额
			},
			WithoutParentheses: false,
		},
		clause.Expr{
			SQL: `
				IFNULL(
						SUM(
							CASE
							WHEN ? THEN
								 ?
							ELSE
								0
							END
						),
						0
					) as discount_fee
				`,
			Vars: []interface{}{
				dbOrderInfoDetails.IsPay.Eq(1).RawExpr(),
				dbOrderInfoDetails.DiscountTotalFee.RawExpr(),
			},
			WithoutParentheses: false,
		},
		clause.Expr{
			SQL: `
				IFNULL(
						SUM(
							CASE
							WHEN ?  THEN
							   ?
							ELSE
								0
							END
						),
						0
					) as service_fee
				`,
			Vars: []interface{}{
				dbOrderInfoDetails.IsPay.Eq(1).RawExpr(),
				dbOrderInfoDetails.ServiceTotalFee.RawExpr(),
			},
		},
		clause.Expr{
			SQL: `
				IFNULL(
						SUM(
							CASE
							WHEN ?  THEN
								?
							ELSE
								0
							END
						),
						0
					) as coupon_fee
				`,
			Vars: []interface{}{
				dbOrderInfoDetails.IsPay.Eq(1).RawExpr(),
				dbOrderInfoDetails.CouponTotalPrice.RawExpr(),
			},
		},
		clause.Expr{
			SQL: `
				IFNULL(
						SUM(
							CASE
							WHEN ?  THEN
								 ?
							ELSE
								0
							END
						),
						0
					) as erase_price
				`,
			Vars: []interface{}{
				dbOrderInfoDetails.IsPay.Eq(1).RawExpr(),
				dbOrderInfoDetails.EraseTotalPrice.RawExpr(),
			},
		},
		clause.Expr{
			SQL: `
				IFNULL(
						SUM(
							CASE
							WHEN ? THEN
								 ?
							ELSE
								0
							END
						),
						0
					) as player_price
				`,
			Vars: []interface{}{
				dbOrderInfoDetails.IsPay.Eq(1).RawExpr(),
				dbOrderInfoDetails.PlayerTotalPrice.RawExpr(),
			},
		},
	)
}

// GetOrderListBySalesmanId 根据销售id 获取order list
func (S *InsBookInService) GetOrderListBySalesmanId(salesmanId uint, startDay time.Time, endDay time.Time, storeId uint) (res []insbuyResp.GetOpenDeskOrderAmountRes, err error) {
	var (
		dbDeskOpen         = query.InsDeskOpen
		dbOrderInfo        = query.InsOrderInfo
		dbOrderInfoDetails = query.InsOrderInfoDetails
	)
	orderList := make([]insbuyResp.GetOpenDeskOrderAmountRes, 0)
	do := dbOrderInfo.Where(dbDeskOpen.SalesmanId.Eq(salesmanId)).
		Where(dbOrderInfoDetails.DeletedAt.IsNull(),
			dbDeskOpen.BusinessDay.Gte(startDay),
			dbDeskOpen.BusinessDay.Lte(endDay),
			dbDeskOpen.StoreId.Eq(storeId),
		).
		Join(dbOrderInfoDetails, dbOrderInfo.OrderId.EqCol(dbOrderInfoDetails.OrderId)).
		Join(dbDeskOpen, dbOrderInfo.OpenDeskId.EqCol(dbDeskOpen.ID)).DO
	SelectAppend(&do,
		dbDeskOpen.SalesmanId,
		dbOrderInfo.OpenDeskId.As("open_desk_id"),
		dbOrderInfo.OrderSn.As("order_sn"),
		clause.Expr{
			SQL: "IFNULL(SUM((? + ?) * ?),0) as consumed",
			Vars: []interface{}{
				dbOrderInfoDetails.Nums.RawExpr(),
				dbOrderInfoDetails.ReturnNum.RawExpr(),
				dbOrderInfoDetails.ProductPrice.RawExpr(),
			},
			WithoutParentheses: false,
		},
	)
	err = do.Group(dbOrderInfo.OpenDeskId, dbOrderInfoDetails.OrderId, dbOrderInfoDetails.ProductId).Scan(&orderList)
	if err != nil {
		return
	}
	res = orderList
	return
}

type orderBill struct {
	OpenDeskId   int     `json:"open_desk_id"`
	PayPrice     float64 `json:"payPrice"`
	PayCode      string  `json:"payCode"`
	CardPayPrice float64 `json:"cardPayPrice"`
}

// GetOrderMap 获取订单map
func (S *InsBookInService) GetOrderMap(orderMap map[int]*insbuyResp.GetOpenDeskOrderAmountRes, billMap map[int]orderBill, giveMap []insbuyResp.GetGiftRecordByOpenDeskIdRes) (res map[int]*insbuyResp.GetOpenDeskOrderAmountRes, err error) {
	//订单map
	for i, v := range orderMap {
		bill := billMap[int(v.OpenDeskId)]
		orderMap[i].BalancePayAmount = jtypes.JPrice(bill.CardPayPrice)
	}
	for _, v := range giveMap {
		if orderMap[v.OpenDeskId] != nil {
			orderMap[v.OpenDeskId].GiveAmount += jtypes.JPrice(v.TotalPrice)
		}
	}
	res = orderMap
	return
}

// OpenDesk 开台
func (S *InsBookInService) OpenDesk(r insbuyReq.InsBookInOpenDesk) (res *response.InsBookInOpenDesk, err error) {
	err = TryTransaction(func(tx *query.Query) (err error) {
		res, err = S.TryOpenDesk(r.GetCtx(), tx, insbuyReq.TryOpenDeskParams{
			OpenDesk: r.OpenDesk,
			UserId:   r.GetUserID(),
			StoreId:  r.GetUintStoreId(),
			SStoreId: r.GetStoreId(),
		})
		return
	})
	if err != nil {
		return
	}
	ebus.Publish(global.EVENTDESKOpenSuccess, res.OpenDeskId)
	return
}

func (S *InsBookInService) TryOpenDesk(ctx context.Context, tx *query.Query, r insbuyReq.TryOpenDeskParams) (res *response.InsBookInOpenDesk, err error) {
	var (
		dbDeskOpen    = tx.InsDeskOpen
		dbDeskOpenLog = tx.InsDeskOpenLog
		dbDesk        = tx.InsDesk
	)
	uStoreId, uid := r.StoreId, r.UserId
	reportRes, err := S.GetStoreRevenueReport(tx, uStoreId)
	if err != nil {
		return
	}
	if reportRes.ID == 0 {
		err = errno.Forbidden.WithMsg("营业已结束,暂不能开台")
		return
	}
	day := jgorm.DefaultBusinessDay(time.Now(), "", "")
	if !reportRes.OpenDate.Equal(day) && r.Force == nil {
		err = errno.NewErrWithCode(insbuy.BusinessErr(insbuy.InsBuyOpenDeskBusinessErr))
		return
	}
	if r.Force != nil && *r.Force == 1 {
		//结束营业-并开始营业
		err = S.tryEndBusinessDay(ctx, tx, uStoreId, reportRes.OpenDate, uid)
		if err != nil {
			return
		}
		err = S.tyrStartBusinessDay(ctx, tx, uStoreId)
		if err != nil {
			return
		}
	}
	deskRes, err := S.TryCheckDeskIsOpen(context.Background(), tx, r.DeskId, int(uStoreId))
	if err != nil {
		return
	}
	now := time.Now() //预约和开台都用这个时间，但是预约重新开台时这个时间更新
	deskOpenData := insbuy.InsDeskOpen{
		DeskId:          r.DeskId,
		MemberId:        r.MemberId,
		SalesmanId:      r.SalesmanId,
		PeopleNum:       int(r.PeopleNum),
		MinAmount:       r.MinAmount,
		Remark:          r.Remark,
		RemarkExt:       r.RemarkExt.ToJson(),
		OpTime:          &now,
		StoreId:         uStoreId,
		OpenDeskSn:      S.generateOpenDeskSn(ctx, r.SStoreId),
		Status:          insbuy.ODSOn.ToInt(),
		TempMemberName:  r.TempMemberName,
		BookType:        r.BookType,
		BookArrivalTime: r.BookArrivalTime,
		BookAmount:      r.BookAmount,
		CustomerSource:  r.CustomerSource,
	}
	if S.checkBookType(r.BookType) {
		bookTime := time.Now()
		deskOpenData.BookTime = &bookTime
	}
	err = dbDeskOpen.Create(&deskOpenData)
	if err != nil {
		err = errno.QueryFailed.WithError(err)
		return
	}
	//空闲转订单
	oldStatusId := deskRes.DeskStatusId

	newStatus := int(insbuy.DeskStatusOrder)
	if r.BookType > 0 {
		newStatus = int(insbuy.DeskStatusReserve)
	}
	newStatusId, err := S.GetStatusIdByStyle(uStoreId, newStatus)
	if err != nil {
		err = errno.QueryFailed.WithError(err)
	}
	logData := insbuy.InsDeskOpenLog{
		OpenDeskId:  deskOpenData.ID,
		DeskId:      r.DeskId,
		OldStatusId: &oldStatusId,
		NewStatusId: &newStatusId,
		StoreId:     uStoreId,
		OperatorId:  uid,
		Event:       insbuy.DActionOpen.Str(),
	}
	err = dbDeskOpenLog.Create(&logData)
	if err != nil {
		err = errno.QueryFailed.WithError(err)
		return
	}

	desk := insbuy.InsDesk{
		DeskStatusId: newStatusId,
		OpenDeskId:   deskOpenData.ID,
	}
	_, err = dbDesk.Where([]gen.Condition{
		dbDesk.ID.Eq(r.DeskId),
	}...).Limit(1).Updates(&desk)
	if err != nil {
		err = errno.QueryFailed.WithError(err)
		return
	}
	res = new(response.InsBookInOpenDesk)
	res.OpenDeskId = deskOpenData.ID
	return
}

// 是否是预约
func (S *InsBookInService) checkBookType(bookType int) bool {
	if insbuy.BookType(bookType) != insbuy.BTNone {
		return true
	}
	return false
}

// 桌台状态是否是使用中
func (S *InsBookInService) checkDeskStatusIsOrder(style int) bool {
	if insbuy.DeskStatus(style) == insbuy.DeskStatusOrder {
		return true
	}
	return false
}

// BookOpenDesk 预约开台
// 预约的台子 状态进行切换即可和开台时间
func (S *InsBookInService) BookOpenDesk(req insbuyReq.BookOpenDeskReq) (res *response.InsBookInOpenDesk, err error) {
	//修改开台绑定的桌台号
	err = TryTransaction(func(tx *query.Query) (err error) {
		var (
			dbDesk     = tx.InsDesk
			dbDeskOpen = tx.InsDeskOpen
		)
		//查询原桌台信息
		deskId, err := S.GetDeskIdByOpenDeskId(req.OpenDeskId)
		if err != nil {
			return
		}
		if !S.checkDeskIsExist(deskId) {
			return errno.Forbidden.WithMsg("桌台不存在")
		}
		uStoreId, uid := req.GetUintStoreId(), req.GetUserID()
		openStatusId, err := S.GetStatusIdByStyle(uStoreId, S.OpenAction().ToInt())
		if err != nil {
			err = errno.QueryFailed.WithError(err)
		}
		_, err = dbDesk.Where(dbDesk.ID.Eq(deskId)).Select(
			dbDesk.DeskStatusId,
		).Updates(insbuy.InsDesk{
			DeskStatusId: openStatusId,
		})
		if err != nil {
			return
		}
		openTime := time.Now()
		_, err = dbDeskOpen.Where(dbDeskOpen.ID.Eq(req.OpenDeskId)).
			Select(dbDeskOpen.BookType, dbDeskOpen.OpTime).
			Updates(insbuy.InsDeskOpen{
				BookType: 0,
				OpTime:   &openTime,
			})
		if err != nil {
			return
		}
		deskRes, err := S.GetDeskInfoById(deskId, int(uStoreId))
		if err != nil {
			return
		}
		//日志记录
		err = S.DeskOpenLog(tx, insbuyReq.OpenLogParams{
			OpenDeskId:  req.OpenDeskId,
			StoreId:     req.GetUintStoreId(),
			DeskId:      deskId,
			OldStatusId: deskRes.DeskStatusId,
			NewStatusId: openStatusId,
			OperatorId:  uid,
			Event:       insbuy.DActionOpen,
		})
		if err != nil {
			return
		}
		res = new(response.InsBookInOpenDesk)
		res.OpenDeskId = req.OpenDeskId
		return
	})
	if err != nil {
		return
	}
	ebus.Publish(global.EVENTDESKOpenSuccess, res.OpenDeskId)
	return
}

// 桌台是否有预约活动-小程序开卡配置存在时 线下不能开台
func (S *InsBookInService) checkDeskDeskConf(deskId uint, storeId uint, tx *query.Query) bool {
	dbAct := tx.InsActBookDeskConf
	total, err := dbAct.Where(dbAct.StoreId.Eq(storeId),
		dbAct.DeskId.Eq(deskId),
		dbAct.BookDate.Eq(jgorm.DefaultBusinessDay(time.Now(), "", "")),
		dbAct.BeginTime.Lte(time.Now()),
		dbAct.EndTime.Gte(time.Now()), dbAct.Status.Eq(insbuy.StatOk.Int())).Count()
	if err != nil {
		return false
	}
	return total > 0
}

// OpenDeskHistoryList 分页获取获取开台历史
func (S *InsBookInService) OpenDeskHistoryList(r insbuyReq.OpenDeskHistoryListReq) (res []insbuyResp.InsBookInDeskListItem, total int64, err error) {
	ctx, _, storeId := r.GetCtx(), r.GetUserID(), r.GetUintStoreId()
	limit := r.PageSize
	offset := r.PageSize * (r.Page - 1)
	var (
		dbDeskStatus    = query.InsDeskStatus
		dbDesk          = query.InsDesk
		dbDeskCategory  = query.InsDeskCategory
		dbDeskStatusLog = query.InsDeskStatusLog
		dbDeskOpen      = query.InsDeskOpen
		dbMember        = query.InsVipMember
		dbInsSaler      = query.SysUser
	)
	conds := []gen.Condition{}
	if r.DeskId > 0 {
		conds = append(conds, dbDesk.ID.Eq(r.DeskId))
	}
	if r.AreaId > 0 {
		conds = append(conds, dbDesk.DeskAreaId.Eq(int(r.AreaId)))
	}
	if r.BusinessDay != nil {
		currentTime := *r.BusinessDay
		dateOnly := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, time.Local)
		conds = append(conds, dbDeskOpen.BusinessDay.Eq(dateOnly))
	} else {
		if r.StartTime != nil {
			startTime := *r.StartTime
			dateOnly := utils.GetDate(utils.ConvertToLocalTime(startTime))
			conds = append(conds, dbDeskOpen.BusinessDay.Gte(dateOnly))
		}
		if r.EndTime != nil {
			endTime := *r.EndTime
			dateOnly := utils.GetDate(utils.ConvertToLocalTime(endTime))
			conds = append(conds, dbDeskOpen.BusinessDay.Lte(dateOnly))
		}
	}
	conds = append(conds, dbDeskOpen.StoreId.Eq(r.GetUintStoreId()))
	if r.SalesmanId > 0 {
		salesmanIds, err2 := gSrv.GetSubordinateSalesId(r.SalesmanId, storeId)
		if err2 != nil {
			err = err2
			return
		}
		if len(salesmanIds) > 0 {
			conds = append(conds, dbDeskOpen.SalesmanId.In(salesmanIds...))
		}
	}
	if r.SalerId > 0 {
		conds = append(conds, dbDeskOpen.SalesmanId.Eq(r.SalerId))
	}
	do := dbDeskOpen.WithContext(ctx).
		LeftJoin(dbDesk, dbDesk.ID.EqCol(dbDeskOpen.DeskId)).
		Join(dbDeskStatus, dbDesk.DeskStatusId.EqCol(dbDeskStatus.ID)).
		Join(dbDeskCategory, dbDesk.DeskCategoryId.EqCol(dbDeskCategory.ID)).
		LeftJoin(dbDeskStatusLog, dbDesk.ID.EqCol(dbDeskStatusLog.DeskId)).
		LeftJoin(dbMember, dbDeskOpen.MemberId.EqCol(dbMember.ID)).       //会员
		LeftJoin(dbInsSaler, dbDeskOpen.SalesmanId.EqCol(dbInsSaler.ID)). //销售
		Where(conds...).Order(dbDesk.SortOrder).DO
	SelectAppend(&do,
		dbDesk.DeskName.As("desk_name"),
		dbDesk.DeskAreaId.As("area_id"),
		dbDesk.StoreId.As("store_id"),
		dbDesk.ID.As("desk_id"),
		dbDeskStatus.Style,
		dbDesk.DeskStatusId.As("status"),
		dbDeskCategory.Name.As("desk_category_name"),
		dbDeskOpen.MinAmount.As("min_amount"),
		dbDeskOpen.OpTime.As("open_time"),
		dbDeskOpen.CloseTime,
		dbMember.Viplevel.As("viplevel"),
		dbMember.UserName.As("member_name"),
		dbDeskOpen.TempMemberName,
		dbDeskOpen.BookType,
		dbDeskOpen.BookArrivalTime,
		dbMember.ID.As("member_id"),
		dbMember.Code.As("member_code"),
		dbDeskOpen.ID.As("open_desk_id"),
		dbDeskOpen.PeopleNum.As("people_num"),
		dbDeskOpen.Remark,
		dbDeskOpen.TurnoverNum,
		dbInsSaler.ID.As("salesman_id"),
		dbInsSaler.NickName.As("salesman_name"),
		dbDeskStatus.Color.As("status_color"),
	)
	total, err = do.Count()
	if err != nil {
		return
	}
	if gSrv.IsExport(r.IsExport) {
		err = do.Scan(&res)
		if err != nil {
			return
		}
	} else {
		err = do.Limit(limit).Offset(offset).Scan(&res)
		if err != nil {
			return
		}
	}
	//获取所有openDeskId
	openDeskIds := make([]int, 0)
	uOpenDeskIds := make([]uint, 0)
	for _, val := range res {
		if val.OpenDeskId > 0 {
			openDeskIds = append(openDeskIds, int(val.OpenDeskId))
			uOpenDeskIds = append(uOpenDeskIds, val.OpenDeskId)
		}
	}
	orderMap, err := S.GetOrderConsumed(openDeskIds)
	if err != nil {
		return
	}
	sumMap, err := S.GetTurnoverSumMap(insdesk.TurnoverParam{
		StoreId: storeId,
	})
	if err != nil {
		return
	}
	//销售id
	salesmanIds := make([]uint, 0)
	for _, val := range res {
		if val.SalesmanId > 0 {
			salesmanIds = append(salesmanIds, val.SalesmanId)
		}
	}
	m1, e1 := insreport.DataUserInfo(ctx, query.Q, insreport.DataUserInfoParam{UserIds: salesmanIds})
	if e1 != nil {
		err = e1
		return
	}
	//订单绑定
	filterRes := make([]insbuyResp.InsBookInDeskListItem, 0)
	for i, val := range res {
		if order, ok := orderMap[int(val.OpenDeskId)]; ok {
			res[i].ExistOrder = true
			res[i].DeskConsumed = order.DeskConsumed
		}
		res[i].TurnoverNum = sumMap[int(val.DeskId)]
		if val.MemberId == 0 {
			res[i].MemberName = val.TempMemberName
		}
		if org, ok := m1[val.SalesmanId]; ok {
			res[i].OrganizationName = org.OrgName
		}
		if r.SalerOrg > 0 {
			if org, ok := m1[val.SalesmanId]; ok && org.OrgId == r.SalerOrg {
				filterRes = append(filterRes, res[i])
			}
		} else {
			filterRes = append(filterRes, res[i])
		}
	}
	res = filterRes //过滤
	return
}

// GetDeskStatusStyle 获取桌台状态style
func (S *InsBookInService) GetDeskStatusStyle(storeId uint, deskId uint) (style int, err error) {
	var (
		dbDesk       = query.InsDesk
		dbDeskStatus = query.InsDeskStatus
	)
	err = dbDesk.Where(dbDesk.ID.Eq(deskId), dbDesk.StoreId.Eq(int(storeId))).
		Join(dbDeskStatus, dbDesk.DeskStatusId.EqCol(dbDeskStatus.ID)).
		Select(dbDeskStatus.Style).
		Scan(&style)
	if err != nil {
		return
	}
	return
}

// GetDeskInfo 获取桌台基础信息
func (S *InsBookInService) GetDeskInfo(storeId uint, deskId uint) (res insbuyResp.InsBookInDeskInfo, err error) {
	var (
		dbDesk       = query.InsDesk
		dbDeskStatus = query.InsDeskStatus
	)
	err = dbDesk.Where(dbDesk.ID.Eq(deskId), dbDesk.StoreId.Eq(int(storeId))).
		Join(dbDeskStatus, dbDesk.DeskStatusId.EqCol(dbDeskStatus.ID)).
		Select(
			dbDesk.ID.As("desk_id"),
			dbDesk.OpenDeskId,
			dbDesk.DeskName,
			dbDeskStatus.Style.As("status"),
		).
		Scan(&res)
	if err != nil {
		return
	}
	return
}

// GetStatusIdByStyle 桌台状态转换状态id
func (S *InsBookInService) GetStatusIdByStyle(storeId uint, style int) (res int, err error) {
	dbStatus := query.InsDeskStatus
	status, err := dbStatus.Where([]gen.Condition{
		dbStatus.Style.Eq(style),
		dbStatus.StoreId.Eq(storeId),
	}...).First()
	if err != nil {
		return
	}
	res = int(status.ID)
	return
}

// TryCheckDeskIsOpen 事务验证验证桌台是否被占用
func (S *InsBookInService) TryCheckDeskIsOpen(ctx context.Context, q *query.Query, deskId uint, storeId int) (res insbuy.InsDesk, err error) {
	var (
		dbDesk = q.InsDesk
	)
	err = dbDesk.Where([]gen.Condition{
		dbDesk.ID.Eq(deskId),
		dbDesk.StoreId.Eq(storeId),
	}...).Scan(&res)
	if err != nil {
		return
	}
	if res.ID == 0 {
		err = errno.Forbidden.WithMsg("当店桌台不存在,请刷新后重试")
		return
	}
	if res.OpenDeskId > 0 {
		err = errno.Forbidden.WithMsg("桌台已被占用")
		return
	}
	return
}

// GetDeskInfoById 根据桌台id查询桌台信息
func (S *InsBookInService) GetDeskInfoById(deskId uint, storeId int) (res insbuy.InsDesk, err error) {
	var (
		dbDesk = query.InsDesk
	)
	err = dbDesk.Where([]gen.Condition{
		dbDesk.ID.Eq(deskId),
		dbDesk.StoreId.Eq(storeId),
	}...).Scan(&res)
	if err != nil {
		return
	}
	return
}

// CheckDeskIsFree 验证桌台是否空闲
func (S *InsBookInService) CheckDeskIsFree(openDeskId uint, uStoreId uint) (res *insbuy.InsDeskOpen, err error) {
	var (
		dbDeskOpen = query.InsDeskOpen
		dbDesk     = query.InsDesk
	)
	res, err = dbDeskOpen.Where([]gen.Condition{
		dbDeskOpen.ID.Eq(openDeskId),
		dbDeskOpen.StoreId.Eq(uStoreId),
	}...).Join(dbDesk, dbDeskOpen.ID.EqCol(dbDesk.OpenDeskId)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errno.QueryFailed.WithError(err)
		return
	}
	if res == nil {
		err = errno.Forbidden.WithMsg("桌台未开台")
	}
	return
}

// GetDeskInfoByOpenDeskId 根据开台id获取桌台信息
func (S *InsBookInService) GetDeskInfoByOpenDeskId(openDeskId uint) (res insbuy.InsDesk, err error) {
	var (
		dbDesk = query.InsDesk
	)
	err = dbDesk.Where(dbDesk.OpenDeskId.Eq(openDeskId)).Scan(&res)
	if err != nil {
		return
	}
	return
}

// GetDeskIdByOpenDeskId 根据开台id获取桌台id
func (S *InsBookInService) GetDeskIdByOpenDeskId(openDeskId uint) (deskId uint, err error) {
	var (
		dbDeskOpen = query.InsDeskOpen
	)
	err = dbDeskOpen.Where(dbDeskOpen.ID.Eq(openDeskId)).Select(dbDeskOpen.DeskId).Scan(&deskId)
	if err != nil {
		return
	}
	return
}

// CloseAllDesk 一键关台
// 清台
func (S *InsBookInService) CloseAllDesk(req insbuyReq.InsBookInCloseAllDeskResp) (res insbuyResp.InsBookInCloseAllDesk, err error) {
	//查询当前店铺的所有还在开台的桌台
	var (
		dbDesk = query.InsDesk
	)
	desks := make([]insbuy.InsDesk, 0)
	err = dbDesk.WithContext(context.Background()).
		Where(dbDesk.StoreId.Eq(int(req.GetUintStoreId())),
			dbDesk.OpenDeskId.Gt(0)).Scan(&desks)
	if err != nil {
		return
	}
	virtual := make([]string, 0)
	for _, desk := range desks {
		if *desk.DeskAttr == 1 {
			virtual = append(virtual, desk.DeskName)
		}
	}
	if len(virtual) > 0 {
		err = errno.InternalServerError.WithMsg(fmt.Sprintf("虚拟台无法一键清台,请指定真实桌台:%s", strings.Join(virtual, ",")))
		return
	}
	openDeskId := make([]uint, 0)
	for _, v := range desks {
		_, err = S.CloseOpenDesk(insbuyReq.InsBookInCloseDeskReq{
			OpenDeskId:   v.OpenDeskId,
			WithStoreId:  req.WithStoreId,
			WithAuthUser: req.WithAuthUser,
		})
		if err != nil {
			global.GVA_LOG.Error("关台失败", zap.Error(err))
			continue
		}
		openDeskId = append(openDeskId, v.OpenDeskId)
	}
	res.OpenDeskId = openDeskId
	return
}

// CloseOpenDesk 关台-清台
func (S *InsBookInService) CloseOpenDesk(req insbuyReq.InsBookInCloseDeskReq) (resp insbuyResp.InsBookInCloseDesk, err error) {
	var openDeskId uint
	err = TryTransaction(func(tx *query.Query) (err error) {
		var (
			dbDesk      = tx.InsDesk
			dbDeskOpen  = tx.InsDeskOpen
			dbOrderInfo = tx.InsOrderInfo
		)
		//判断当前桌台是否已经开台
		openDesk, err := S.CheckDeskIsFree(req.OpenDeskId, req.GetUintStoreId())
		if err != nil {
			return
		}
		//未结账金额验证
		billResp := insbuyResp.GetOrderBillResp{}
		billResp, err = gSrv.GetOrderBill(insbuyReq.GetOrderBillReq{
			OpenDeskId: int(req.OpenDeskId),
		})
		if err != nil {
			err = errno.Forbidden.WithMsg("待付金额错误")
			return
		}
		if billResp.NeedPayPrice > 0 {
			err = errno.Forbidden.WithMsg("还未结账，不能关台")
			return
		}
		isOrder, _ := dbOrderInfo.Where(dbOrderInfo.OpenDeskId.Eq(int(req.OpenDeskId))).Count()
		if openDesk.BookType == 0 && req.CancelDesk == 1 && isOrder > 0 {
			err = errno.Forbidden.WithMsg("已开台,不能进行撤台操作")
			return
		}
		storeId := req.GetUintStoreId()
		//空闲转订单
		oldStatus := openDesk.Status
		oldStatusId, newStatusId, err := S.getCloseStatusId(storeId, oldStatus)
		if err != nil {
			return
		}
		err = S.DeskOpenLog(tx, insbuyReq.OpenLogParams{
			OpenDeskId:  req.OpenDeskId,
			StoreId:     req.GetUintStoreId(),
			DeskId:      openDesk.DeskId,
			OldStatusId: oldStatusId,
			NewStatusId: newStatusId,
			OperatorId:  req.GetUserID(),
			Event:       insbuy.DActionClean,
		})
		if err != nil {
			return
		}
		closeTime := time.Now()
		turnoverNum, err := S.nextTurnoverNum(tx, openDesk.DeskId, storeId)
		if err != nil {
			return
		}
		closeData := insbuy.InsDeskOpen{
			Status:      insbuy.ODSOff.ToInt(),
			CloseTime:   &closeTime,
			TurnoverNum: turnoverNum,
			RealDeskId:  req.RealDeskId,
		}
		if req.CancelDesk == 1 {
			closeData.DeletedAt = gorm.DeletedAt{
				Time:  time.Now(),
				Valid: true,
			}
		}
		_, _ = dbDeskOpen.Select(
			dbDeskOpen.Status,
			dbDeskOpen.CloseTime,
			dbDeskOpen.TurnoverNum,
			dbDeskOpen.DeletedAt,
			dbDeskOpen.RealDeskId,
		).Where(dbDeskOpen.ID.Eq(openDesk.ID)).Updates(&closeData)
		desk := insbuy.InsDesk{
			DeskStatusId: newStatusId,
			OpenDeskId:   0,
		}
		_, err = dbDesk.Select(
			dbDesk.DeskStatusId,
			dbDesk.OpenDeskId,
		).Where([]gen.Condition{
			dbDesk.ID.Eq(openDesk.DeskId),
		}...).Limit(1).Updates(&desk)
		if err != nil {
			return
		}
		openDeskId = openDesk.ID
		return
	})
	if err != nil {
		return
	}
	ebus.Publish(global.EVENTDESKCloseSuccess, openDeskId)
	return
}

// getCloseStatusId 关台状态id获取
func (S *InsBookInService) getCloseStatusId(storeId uint, oldStatus int) (oldStatusId int, newStatusId int, err error) {
	//空闲转订单
	newStatus := int(insbuy.DeskStatusDf)
	//获取状态id
	oldStatusId, err = S.GetStatusIdByStyle(storeId, oldStatus)
	if err != nil {
		err = errno.QueryFailed.WithError(err)
		return
	}
	newStatusId, err = S.GetStatusIdByStyle(storeId, newStatus)
	if err != nil {
		err = errno.QueryFailed.WithError(err)
		return
	}
	return
}

// DeskOpenLog 桌台开台状态日志记录
func (S *InsBookInService) DeskOpenLog(tx *query.Query, params insbuyReq.OpenLogParams) (err error) {
	var (
		dbDeskOpenLog = query.InsDeskOpenLog
	)
	if tx != nil {
		dbDeskOpenLog = &tx.InsDeskOpenLog
	}
	logData := insbuy.InsDeskOpenLog{
		OpenDeskId:  params.OpenDeskId,
		StoreId:     params.StoreId,
		FormDeskId:  params.FormDeskId,
		DeskId:      params.DeskId,
		OldStatusId: &params.OldStatusId,
		NewStatusId: &params.NewStatusId,
		OperatorId:  params.OperatorId,
		Remark:      params.Remark,
		Event:       params.Event.Str(),
	}
	err = dbDeskOpenLog.Create(&logData)
	if err != nil {
		return
	}
	return
}

// generateOpenDeskSn 生成开台编号
func (S *InsBookInService) generateOpenDeskSn(ctx context.Context, storeId string) (openDeskSn string) {
	openDeskSn, err := gSrv.SnOpenDesk.Next(ctx, storeId)
	if err != nil {
		return
	}
	return
}

func (S *InsBookInService) OpenDeskDetail(r insbuyReq.InsBookInOpenDeskDetail) (res *response.InsBookInOpenDeskDetail, err error) {
	var (
		dbMember       = query.InsVipMember
		dbInsSaler     = query.SysUser
		dbDeskCategory = query.InsDeskCategory
		dbDesk         = query.InsDesk
		dbFromDesk     = query.InsDesk.As("from_desk")
		dbDeskArea     = query.InsDeskArea
		dbDeskOpen     = query.InsDeskOpen
		dbDeskStatus   = query.InsDeskStatus
	)

	res = new(response.InsBookInOpenDeskDetail)
	err = dbDeskOpen.Where([]gen.Condition{
		dbDeskOpen.ID.Eq(r.OpenDeskId),
	}...).Join(dbDesk, dbDeskOpen.DeskId.EqCol(dbDesk.ID)).
		Join(dbDeskCategory, dbDesk.DeskCategoryId.EqCol(dbDeskCategory.ID)).
		LeftJoin(dbMember, dbDeskOpen.MemberId.EqCol(dbMember.ID)).       //会员
		LeftJoin(dbInsSaler, dbDeskOpen.SalesmanId.EqCol(dbInsSaler.ID)). //销售
		LeftJoin(dbDeskStatus, dbDesk.DeskStatusId.EqCol(dbDeskStatus.ID)).
		LeftJoin(dbDeskArea, dbDesk.DeskAreaId.EqCol(dbDesk.ID)).
		LeftJoin(dbFromDesk, dbDeskOpen.FormDeskId.EqCol(dbFromDesk.ID)).
		Select(
			dbDesk.ID.As("desk_id"),
			dbDesk.DeskName.As("desk_name"),
			dbDeskCategory.Name.As("desk_cate_gory_name"),
			dbDeskOpen.PeopleNum.As("people_num"),
			dbDeskOpen.OpTime.As("open_time"),
			dbDeskOpen.ID.As("open_desk_id"),
			dbDeskOpen.Remark.As("remark"),
			dbDeskOpen.MinAmount.As("min_amount"),
			dbDeskOpen.TempMemberName,
			dbDeskOpen.BookType,
			dbDeskOpen.BookArrivalTime,
			dbMember.Phone.As("vip_phone"),
			dbMember.Viplevel.As("viplevel"),
			dbMember.UserName.As("member_name"),
			dbMember.ID.As("member_id"),
			dbInsSaler.ID.As("salesman_id"),
			dbInsSaler.NickName.As("salesman_name"),
			dbDeskStatus.Style,
			dbDeskOpen.OpenDeskSn.As("open_desk_sn"),
			dbDeskOpen.CloseTime,
			dbDeskArea.Name.As("desk_area"),
			dbDeskOpen.BookAmount,
			dbDeskOpen.CustomerSource,
			dbFromDesk.DeskName.As("from_desk_name"),
		).
		Scan(&res)
	if err != nil {
		return nil, err
	}
	openDeskIds := []int{int(r.OpenDeskId)}
	consumed, err := gSrv.GetOrderConsumed(openDeskIds)
	if err != nil {
		return
	}
	calculateConsumed := gSrv.CalculateConsumed(consumed)
	res.DeskConsumed = calculateConsumed

	res.VipPhone = HideIdent(res.VipPhone)
	return
}

// UpdateOpenDesk 修改开台
func (S *InsBookInService) UpdateOpenDesk(c *gin.Context, r insbuyReq.InsBookInOpenDesk) (res *insbuyResp.InsBookInUpdateOpenDesk, err error) {
	//日志记录
	log := eventslog.NewEventsLog(c, eventsmodel.AtTable, eventsmodel.ETTableUpdated)
	log.SetMethod(c.Request.Method)
	//查询old数据
	log.SetAggregateID(r.OpenDeskId)

	dbDeskOpen := query.InsDeskOpen
	before, err := dbDeskOpen.Where([]gen.Condition{
		dbDeskOpen.ID.Eq(r.OpenDeskId),
	}...).First()
	if err != nil {
		return nil, errno.QueryFailed.WithError(err)
	}
	log.SetBefore(before)
	tempMemberName := r.TempMemberName
	if r.MemberId > 0 {
		tempMemberName = ""
	}
	deskOpenData := insbuy.InsDeskOpen{
		MemberId:        r.MemberId,
		SalesmanId:      r.SalesmanId,
		PeopleNum:       int(r.PeopleNum),
		MinAmount:       r.MinAmount,
		Remark:          r.Remark,
		RemarkExt:       r.RemarkExt.ToJson(),
		TempMemberName:  tempMemberName,
		BookType:        r.BookType,
		BookArrivalTime: r.BookArrivalTime,
		BookAmount:      r.BookAmount,
		CustomerSource:  r.CustomerSource,
	}
	_, err = dbDeskOpen.Where([]gen.Condition{
		dbDeskOpen.ID.Eq(r.OpenDeskId),
	}...).Select(
		dbDeskOpen.MemberId,
		dbDeskOpen.SalesmanId,
		dbDeskOpen.PeopleNum,
		dbDeskOpen.MinAmount,
		dbDeskOpen.Remark,
		dbDeskOpen.TempMemberName,
		dbDeskOpen.BookType,
		dbDeskOpen.BookArrivalTime,
		dbDeskOpen.BookAmount,
		dbDeskOpen.CustomerSource,
	).Updates(&deskOpenData)
	if err != nil {
		return nil, errno.QueryFailed.WithError(err)
	}
	//日志记录
	_ = log.SetAfterByAggregateID(context.Background())
	log.WriteLog(c)
	return res, nil
}

// AddTempDesk
func (S *InsBookInService) AddTempDesk(ctx context.Context, r insbuyReq.InsBookInAddTempDesk) (res response.InsBookInAddTempDesk, err error) {
	// TODO:
	return res, nil
}

// checkDeskIsExist 判断桌台是否存在
func (S *InsBookInService) checkDeskIsExist(deskId uint) (res bool) {
	if deskId == 0 {
		res = false
	} else {
		res = true
	}
	return
}

// ChangeDesk 换桌
// 目标桌：开台
// 来源桌：关台
func (S *InsBookInService) ChangeDesk(req insbuyReq.InsDeskChangeDeskReq) (resp insbuyResp.ChangeDeskResp, err error) {
	//修改开台绑定的桌台号
	var (
		uStoreId = req.GetUintStoreId()
	)
	err = TryTransaction(func(tx *query.Query) (err error) {
		var (
			dbOpen = tx.InsDeskOpen
			dbDesk = tx.InsDesk
		)
		//查询原桌台信息
		deskRes, err := S.GetDeskInfoByOpenDeskId(req.OpenDeskId)
		deskId := deskRes.ID
		if err != nil {
			return
		}
		if !S.checkDeskIsExist(deskId) {
			err = errno.Forbidden.WithMsg("桌台不存在或未开台")
			return
		}
		style, err := S.GetDeskStatusStyle(uStoreId, deskId)
		if err != nil {
			return
		}
		if style == int(insbuy.DeskStatusDf) {
			return errno.QueryNotFound.WithMsg("空闲桌台无需换桌,请刷新后再试")
		}
		_, err = dbOpen.Where(dbOpen.ID.Eq(req.OpenDeskId)).Updates(insbuy.InsDeskOpen{
			DeskId:     req.NewDeskId,
			FormDeskId: deskId,
		})
		if err != nil {
			return
		}
		openDesk := insbuy.InsDesk{
			DeskStatusId: deskRes.DeskStatusId,
			OpenDeskId:   req.OpenDeskId,
		}
		upN, err := dbDesk.Where([]gen.Condition{
			dbDesk.ID.Eq(req.NewDeskId),
			dbDesk.OpenDeskId.Eq(0),
		}...).Limit(1).Updates(&openDesk)
		if err != nil {
			err = errno.QueryFailed.WithError(err)
			return
		}
		if upN.RowsAffected == 0 {
			err = errno.Forbidden.WithMsg("桌台不存在或者已被占用,请重新换桌")
			return
		}
		uid := req.GetUserID()
		err = S.DeskOpenLog(tx, insbuyReq.OpenLogParams{
			OpenDeskId:  req.OpenDeskId,
			StoreId:     uStoreId,
			FormDeskId:  deskRes.ID,
			DeskId:      req.NewDeskId,
			OldStatusId: deskRes.DeskStatusId,
			NewStatusId: deskRes.DeskStatusId,
			OperatorId:  uid,
			Event:       insbuy.DActionChange,
		})
		if err != nil {
			return
		}
		//原桌台处理--------------
		newStatusId, err := S.GetStatusIdByStyle(uStoreId, S.ChangeAction().ToInt())
		if err != nil {
			err = errno.QueryFailed.WithError(err)
		}
		desk := insbuy.InsDesk{
			DeskStatusId: newStatusId,
			OpenDeskId:   0,
		}
		_, err = dbDesk.Select(
			dbDesk.OpenDeskId,
			dbDesk.DeskStatusId).Where([]gen.Condition{
			dbDesk.ID.Eq(deskId),
		}...).Limit(1).Updates(&desk)
		if err != nil {
			err = errno.QueryFailed.WithError(err)
			return
		}
		//日志记录
		resp.OpenDeskId = req.OpenDeskId
		return
	})
	if err != nil {
		return
	}
	ebus.Publish(global.EVENTDESKChangeSuccess, resp.OpenDeskId)
	return
}

// MergeDesk 并桌
// 目标桌：开台
// 来源桌：关台
func (S *InsBookInService) MergeDesk(req insbuyReq.InsDeskMergeDeskReq) (resp insbuyResp.MergeDeskResp, err error) {
	//修改开台绑定的桌台号
	var (
		uStoreId = req.GetUintStoreId()
		uid      = req.GetUserID()
	)
	logger := global.GVA_LOG.With(zap.String("func", "MergeDesk"), zap.Uint("storeId", uStoreId))
	err = TryTransaction(func(tx *query.Query) (err error) {
		var (
			dbOpen          = tx.InsDeskOpen
			dbDesk          = tx.InsDesk
			dbOrderInfo     = tx.InsOrderInfo
			dbShipment      = tx.InsShipment
			dbOrderBill     = tx.InsOrderBill
			dbDeposit       = tx.InsDeposit
			dbGiftLog       = tx.InsGiftLog
			dbGiftLogDetail = tx.InsGiftLogDetail
		)
		//1. check阶段
		//查询原桌台信息
		sourceDeskRes, err := S.GetDeskInfoByOpenDeskId(req.OpenDeskId)
		if err != nil {
			return
		}
		if !S.checkDeskIsExist(sourceDeskRes.ID) {
			return errno.Forbidden.WithMsg("桌台不存在")
		}
		//查询目标桌台信息
		targetDeskRes, err := S.GetDeskInfoById(req.NewDeskId, int(uStoreId))
		if !S.checkDeskIsExist(targetDeskRes.ID) {
			return errno.Forbidden.WithMsg("桌台不存在")
		}
		if targetDeskRes.OpenDeskId == 0 {
			return errno.Forbidden.WithMsg("目标桌台未开台,暂不支持并台")
		}
		//查询目标桌台开台信息
		targetOpenDeskRes := insbuy.InsDeskOpen{}
		err = dbOpen.Where(dbOpen.ID.Eq(targetDeskRes.OpenDeskId)).Scan(&targetOpenDeskRes)
		if err != nil {
			return
		}
		//查询原桌台开台信息
		sourceOpenDeskRes := insbuy.InsDeskOpen{}
		err = dbOpen.Where(dbOpen.ID.Eq(req.OpenDeskId)).Scan(&sourceOpenDeskRes)
		if err != nil {
			return
		}
		//判断定位人是否相同
		if targetOpenDeskRes.SalesmanId != sourceOpenDeskRes.SalesmanId {
			return errno.Forbidden.WithMsg("定位人不同，暂不支持并台")
		}

		//2. 修改数据阶段----------
		//查询来源桌台信息-跟客户端看到的数据一样-退款的数据不需要转移
		//1. 订单
		orderRes, err := gSrv.GetOrderDetails(int(sourceDeskRes.OpenDeskId))
		if err != nil {
			return
		}
		orderIds := make([]uint64, 0)
		for _, v := range orderRes {
			orderIds = append(orderIds, v.OrderId)
		}
		logger.Info("sourceOpenDeskRes", zap.Any("sourceOpenDeskRes", sourceOpenDeskRes))
		logger.Info("targetOpenDeskRes", zap.Any("targetOpenDeskRes", targetOpenDeskRes))
		logger.Info("orderIds", zap.Any("orderIds", orderIds))
		//修改订单桌台号和开台号 todo::没有保留历史数据
		_, err = dbOrderInfo.Where(dbOrderInfo.OrderId.In(orderIds...)).
			Updates(insbuy.InsOrderInfo{
				DeskId:     int(targetDeskRes.ID),
				OpenDeskId: int(targetDeskRes.OpenDeskId),
			})
		//出品表
		_, err = dbShipment.Where(dbShipment.OrderId.In(orderIds...)).Updates(insbuy.InsShipment{
			DeskId:     targetDeskRes.ID,
			OpenDeskId: targetDeskRes.OpenDeskId,
		})
		if err != nil {
			return
		}
		//赠送
		giftLogIds := make([]uint, 0)
		for _, v := range orderRes {
			for _, details := range v.OrderProducts {
				giftLogIds = append(giftLogIds, details.GiftLogId)
			}
		}
		logger.Info("giftLogIds", zap.Any("giftLogIds", giftLogIds))
		logId := make([]uint, 0)
		err = dbGiftLogDetail.Where(dbGiftLogDetail.ID.In(giftLogIds...)).Pluck(dbGiftLogDetail.LogId, &logId)
		if err != nil {
			return
		}
		_, err = dbGiftLog.Where(
			dbGiftLog.ID.In(logId...),
			dbGiftLog.OpenDeskId.Eq(sourceOpenDeskRes.ID),
		).Updates(insbuy.InsGiftLog{
			OpenDeskId: targetDeskRes.OpenDeskId,
		})
		if err != nil {
			return
		}
		//2. 账单
		sourceBills, err := gSrv.GetOrderBill(insbuyReq.GetOrderBillReq{
			OpenDeskId: int(sourceDeskRes.OpenDeskId),
		})
		if err != nil {
			return
		}
		billIds := make([]uint, 0)
		for _, v := range sourceBills.OrderBillPayInfo {
			billIds = append(billIds, v.ID)
		}
		logger.Info("billIds", zap.Any("billIds", billIds))
		_, err = dbOrderBill.Where(dbOrderBill.ID.In(billIds...),
			dbOrderBill.OpenDeskId.Eq(int(sourceDeskRes.OpenDeskId))).
			Updates(insbuy.InsOrderBill{
				OpenDeskId: int(targetDeskRes.OpenDeskId),
			})
		if err != nil {
			return
		}
		//取酒记录
		outList, err := gSrv.GetOpenDeskOutList(insbuyReq.GetDeskDepositGoodsListReq{
			OpenDeskId: sourceDeskRes.OpenDeskId,
		})
		if err != nil {
			return
		}
		depositIds := make([]uint, 0)
		for _, v := range outList {
			depositIds = append(depositIds, v.DepositId)
		}
		logger.Info("depositIds", zap.Any("depositIds", depositIds))
		_, err = dbDeposit.Where(dbDeposit.ID.In(depositIds...)).Updates(insbuy.InsDeposit{
			OpenDeskId: targetDeskRes.OpenDeskId,
			DeskId:     int(targetDeskRes.ID),
		})
		if err != nil {
			return
		}
		//3. 原桌台清台
		//修改原桌台状态
		err = S.DeskOpenLog(tx, insbuyReq.OpenLogParams{
			OpenDeskId:  sourceDeskRes.OpenDeskId,
			StoreId:     uStoreId,
			DeskId:      sourceDeskRes.ID,
			OldStatusId: int(insbuy.DeskStatusOrder),
			NewStatusId: S.MergeAction().ToInt(),
			OperatorId:  uid,
			Event:       insbuy.DActionMerge,
		})
		if err != nil {
			return
		}
		newStatusId, err := S.GetStatusIdByStyle(uStoreId, S.ChangeAction().ToInt())
		if err != nil {
			err = errno.QueryFailed.WithError(err)
		}
		desk := insbuy.InsDesk{
			DeskStatusId: newStatusId,
			OpenDeskId:   0,
		}
		_, err = dbDesk.Select(
			dbDesk.OpenDeskId,
			dbDesk.DeskStatusId).Where([]gen.Condition{
			dbDesk.ID.Eq(sourceDeskRes.ID),
		}...).Limit(1).Updates(&desk)
		if err != nil {
			err = errno.QueryFailed.WithError(err)
			return
		}
		closeTime := time.Now()
		_, err = dbOpen.Where(dbOpen.ID.Eq(req.OpenDeskId)).
			Select(dbOpen.CloseTime).Updates(insbuy.InsDeskOpen{
			CloseTime: &closeTime,
		})
		//日志记录
		resp.OpenDeskId = req.OpenDeskId
		return
	})
	if err != nil {
		return
	}
	return
}

// ChangeDeskStatus 桌台状态修改
// 仅做基础状态,没有特殊逻辑的状态修改，如：清洁,故障,维护
func (S *InsBookInService) ChangeDeskStatus(req insbuyReq.InsDeskChangeDeskStatusReq) (err error) {
	//修改开台绑定的桌台号
	err = TryTransaction(func(tx *query.Query) (err error) {
		var (
			dbDesk = tx.InsDesk
		)
		//查询原桌台信息
		deskRes, err := S.GetDeskInfoById(req.DeskId, int(req.GetUintStoreId()))
		if err != nil {
			return
		}
		if deskRes.OpenDeskId > 0 && req.Style == int(insbuy.DeskStatusLock) {
			return errno.Forbidden.WithMsg("桌台使用中，不能锁台")
		}
		uStoreId := req.GetUintStoreId()
		newStatusId, err := S.GetStatusIdByStyle(uStoreId, req.Style)
		if err != nil {
			err = errno.QueryFailed.WithError(err)
		}
		err = S.DeskOpenLog(tx, insbuyReq.OpenLogParams{
			StoreId:     uStoreId,
			DeskId:      req.DeskId,
			OldStatusId: deskRes.DeskStatusId,
			NewStatusId: newStatusId,
			OperatorId:  req.GetUserID(),
			Event:       insbuy.DActionCustom,
		})
		if err != nil {
			return
		}
		_, err = dbDesk.Where(dbDesk.ID.Eq(req.DeskId)).Select(
			dbDesk.DeskStatusId,
			dbDesk.Remark,
		).Updates(insbuy.InsDesk{
			DeskStatusId: newStatusId,
			Remark:       req.Remark,
		})
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}
	return
}

// GetTurnoverSumMap 统计复台次数
func (S *InsBookInService) GetTurnoverSumMap(param insdesk.TurnoverParam) (res map[int]int, err error) {
	var (
		dbDeskOpen = query.InsDeskOpen
		dbMember   = query.InsVipMember
		dbInsSaler = query.SysUser
	)
	storeId := param.StoreId
	conditions := make([]gen.Condition, 0)
	conditions = append(conditions, dbDeskOpen.BookType.Eq(0))
	conditions = append(conditions, dbDeskOpen.CloseTime.IsNotNull())
	conditions = append(conditions, dbDeskOpen.StoreId.Eq(storeId))
	startOfDay, endOfDay := time.Time{}, time.Time{}
	if param.BeginTime.IsZero() && param.EndTime.IsZero() {
		startOfDay, endOfDay = gSrv.GetTodayRevenueReportTime(storeId)
	} else {
		startOfDay = param.BeginTime
		endOfDay = param.EndTime
	}
	conditions = append(conditions, dbDeskOpen.OpTime.Between(startOfDay, endOfDay))

	type turnover struct {
		DeskId      int `json:"deskId"`
		TurnoverSum int `json:"turnoverSum"`
	}
	turnoverRes := make([]turnover, 0)
	err = dbDeskOpen.Where(conditions...).
		LeftJoin(dbMember, dbDeskOpen.MemberId.EqCol(dbMember.ID)).
		LeftJoin(dbInsSaler, dbDeskOpen.SalesmanId.EqCol(dbInsSaler.ID)).
		Select(
			dbDeskOpen.DeskId.As("desk_id"),
			dbDeskOpen.ID.Count().As("turnover_sum"),
		).Group(dbDeskOpen.DeskId).Scan(&turnoverRes)
	if err != nil {
		return
	}
	res = make(map[int]int)
	for _, v := range turnoverRes {
		res[v.DeskId] = v.TurnoverSum
	}
	return
}

//根据

// GetOpenDeskListByDeskId 获取当前桌台当天开台的数据列表
func (S *InsBookInService) GetOpenDeskListByDeskId(req insbuyReq.GetOpenDeskListReq) (resp []insbuyResp.GetOpenDeskListResp, err error) {
	var (
		dbDeskOpen = query.InsDeskOpen
		dbDesk     = query.InsDesk
		dbMember   = query.InsVipMember
		dbInsSaler = query.SysUser
	)
	conditions := make([]gen.Condition, 0)
	if req.DeskId != nil && *req.DeskId > 0 {
		deskId := *req.DeskId
		conditions = append(conditions, dbDeskOpen.DeskId.Eq(deskId))
	}
	conditions = append(conditions, dbDeskOpen.BookType.Eq(0))
	conditions = append(conditions, dbDeskOpen.StoreId.Eq(req.GetUintStoreId()))

	startOfDay, endOfDay := gSrv.GetTodayRevenueReportTime(req.GetUintStoreId())
	conditions = append(conditions, dbDeskOpen.OpTime.Between(startOfDay, endOfDay))

	err = dbDeskOpen.Where(conditions...).
		LeftJoin(dbMember, dbDeskOpen.MemberId.EqCol(dbMember.ID)).
		LeftJoin(dbDesk, dbDeskOpen.DeskId.EqCol(dbDesk.ID)).
		LeftJoin(dbInsSaler, dbDeskOpen.SalesmanId.EqCol(dbInsSaler.ID)).
		Select(
			dbDeskOpen.ID.As("open_desk_id"),
			dbDeskOpen.DeskId.As("desk_id"),
			dbDeskOpen.OpenDeskSn,
			dbDeskOpen.SalesmanId,
			dbInsSaler.NickName.As("salesman_name"),
			dbDeskOpen.MemberId,
			dbMember.UserName.As("member_name"),
			dbDeskOpen.TempMemberName,
			dbDeskOpen.OpTime.As("open_time"),
			dbDeskOpen.CloseTime,
			dbDeskOpen.TurnoverNum.As("open_num"),
			dbDesk.DeskName,
		).Order(dbDeskOpen.ID.Desc()).
		Scan(&resp)
	if err != nil {
		return
	}
	//获取所有openDeskId
	openDeskIds := make([]int, 0)
	uOpenDeskIds := make([]uint, 0)
	for _, val := range resp {
		if val.OpenDeskId > 0 {
			openDeskIds = append(openDeskIds, int(val.OpenDeskId))
			uOpenDeskIds = append(uOpenDeskIds, val.OpenDeskId)
		}
	}
	orderMap, err := S.GetOrderConsumed(openDeskIds)
	if err != nil {
		return
	}
	//订单绑定
	openNumMap := make(map[uint]int)
	for i, val := range resp {
		if order, ok := orderMap[int(val.OpenDeskId)]; ok {
			resp[i].Consumed = order.Consumed
		}
		openNumMap[val.DeskId] += 1
		resp[i].OpenNum = openNumMap[resp[i].DeskId]
		if val.MemberId == 0 {
			resp[i].MemberName = val.TempMemberName
		}
	}
	return
}

// ReOpenDesk 复台回滚重新绑定桌台状态
func (S *InsBookInService) ReOpenDesk(req insbuyReq.InsBookInReOpenDeskReq) (resp insbuyResp.InsBookInReOpenDeskResp, err error) {
	err = TryTransaction(func(tx *query.Query) (err error) {
		var (
			dbDeskOpen    = tx.InsDeskOpen
			dbDeskOpenLog = tx.InsDeskOpenLog
			dbDesk        = tx.InsDesk
		)
		uStoreId, uid := req.GetUintStoreId(), req.GetUserID()
		deskRes, err := S.TryCheckDeskIsOpen(context.Background(), tx, req.DeskId, int(uStoreId))
		if err != nil {
			return
		}
		_, err = dbDeskOpen.Where(dbDeskOpen.ID.Eq(req.OpenDeskId)).Update(dbDeskOpen.Status, insbuy.ODSOn.ToInt())
		if err != nil {
			err = errno.QueryFailed.WithError(err)
			return
		}
		//空闲转订单
		oldStatusId := deskRes.DeskStatusId

		newStatus := int(insbuy.DeskStatusOrder)
		newStatusId, err := S.GetStatusIdByStyle(uStoreId, newStatus)
		if err != nil {
			err = errno.QueryFailed.WithError(err)
		}
		logData := insbuy.InsDeskOpenLog{
			OpenDeskId:  req.OpenDeskId,
			DeskId:      req.DeskId,
			OldStatusId: &oldStatusId,
			NewStatusId: &newStatusId,
			StoreId:     uStoreId,
			OperatorId:  uid,
		}
		err = dbDeskOpenLog.Create(&logData)
		if err != nil {
			err = errno.QueryFailed.WithError(err)
			return
		}

		desk := insbuy.InsDesk{
			DeskStatusId: newStatusId,
			OpenDeskId:   req.OpenDeskId,
		}
		_, err = dbDesk.Where([]gen.Condition{
			dbDesk.ID.Eq(req.DeskId),
		}...).Limit(1).Updates(&desk)
		if err != nil {
			err = errno.QueryFailed.WithError(err)
			return
		}
		resp.OpenDeskId = req.OpenDeskId
		return
	})
	if err != nil {
		return
	}
	return
}

// ProductChangeDesk 商品转桌
// 仅未支付的商品可以转桌,目标桌台必须是开台状态
func (S *InsBookInService) ProductChangeDesk(req insbuyReq.ProductChangeDeskReq) (resp insbuyResp.ProductChangeDeskResp, err error) {
	var (
		dbDesk             = query.InsDesk
		dbOrderInfoDetails = query.InsOrderInfoDetails
		newOpenDeskId      uint
	)
	//判断目标桌状态
	err = dbDesk.Where(dbDesk.ID.Eq(req.NewDeskId)).
		Where(dbDesk.StoreId.Eq(int(req.GetUintStoreId()))).
		Select(dbDesk.OpenDeskId).Scan(&newOpenDeskId)
	if err != nil {
		return
	}
	if newOpenDeskId == 0 {
		err = errno.Forbidden.WithMsg("目标桌台未开台")
		return
	}
	//判断商品是否已支付
	productIds := make([]uint, 0)
	orderDetailsIds := make([]uint64, 0)
	for _, v := range req.ProductInfo {
		productIds = append(productIds, v.ProductId)
		orderDetailsIds = append(orderDetailsIds, v.OrderDetailsId)
	}
	count, err := dbOrderInfoDetails.Where([]gen.Condition{
		field.Uint64(dbOrderInfoDetails.ID).In(orderDetailsIds...),
		dbOrderInfoDetails.ProductId.In(productIds...),
		dbOrderInfoDetails.IsPay.Eq(1),
	}...).Count()
	if err != nil {
		return
	}
	if count > 0 {
		err = errno.Forbidden.WithMsg("商品已支付，暂不支持转桌")
		return
	}
	err = TryTransaction(func(tx *query.Query) (err error) {
		var (
			txOrderInfoDetails = tx.InsOrderInfoDetails
			txOrderInfo        = tx.InsOrderInfo
		)
		products := make([]insbuyResp.ChangeDeskProductList, 0)
		err = txOrderInfoDetails.Where([]gen.Condition{
			field.Uint64(dbOrderInfoDetails.ID).In(orderDetailsIds...),
			dbOrderInfoDetails.ProductId.In(productIds...),
		}...).Select(
			dbOrderInfoDetails.ID.As("order_details_id"),
			dbOrderInfoDetails.OptionItems,
			dbOrderInfoDetails.OrderId,
			dbOrderInfoDetails.ProductId,
			dbOrderInfoDetails.ProductName,
			dbOrderInfoDetails.ProductPrice,
			dbOrderInfoDetails.Unit,
			dbOrderInfoDetails.Remark,
			dbOrderInfoDetails.WarehouseId,
		).Scan(&products)
		if err != nil {
			return
		}
		//数量组合map
		productNumMap := make(map[uint64]int)
		for _, v := range req.ProductInfo {
			productNumMap[v.OrderDetailsId] = v.Num
		}
		cartList := insbuyResp.GetOrderShoppingCartList{}
		cartProduct := make([]insbuyResp.CartProduct, 0)
		for _, v := range products {
			num := productNumMap[v.OrderDetailsId]
			optionItems := make([]productmodel.ClientSideOptionItems, 0)
			_ = json.Unmarshal([]byte(v.OptionItems), &optionItems)
			cartProduct = append(cartProduct, insbuyResp.CartProduct{
				ProductId:     v.ProductId,
				ProductName:   v.ProductName,
				ProductPrice:  v.ProductPrice,
				OriginalPrice: v.ProductPrice,
				GiftPrice:     0,
				Unit:          v.Unit,
				Remark:        v.Remark,
				WarehouseId:   v.WarehouseId,
				Nums:          num,
				OptionItems:   optionItems,
			})
		}
		cartList.Remark = "转桌"
		cartList.DeskId = int(req.NewDeskId)
		cartList.OpenDeskId = int(req.GetUintStoreId())
		if len(cartProduct) == 0 {
			err = errno.Forbidden.WithMsg("转入商品为空")
			return
		}
		cartList.Products = cartProduct
		//为目标桌创建订单
		saveRes, err := gSrv.SaveOrder(req.GetCtx(), tx, insbuyReq.SaveOrderInfoParams{

			OpenDeskId: int(newOpenDeskId),
			StoreId:    req.GetUintStoreId(),
			Remark:     "转桌",
			DeskId:     int(req.NewDeskId),
			WaiterId:   req.GetUserID(),
		}, cartList)
		if err != nil {
			return
		}
		//修改原订单的数据
		//修改原订单的商品金额
		orderInfo := make([]insbuyReq.OrderRefundParams, 0)
		for _, v := range req.ProductInfo {
			conditions := []gen.Condition{
				field.Uint64(dbOrderInfoDetails.ID).Eq(v.OrderDetailsId),
				dbOrderInfoDetails.ProductId.Eq(v.ProductId),
			}
			first := &insbuy.InsOrderInfoDetails{}
			first, err = txOrderInfoDetails.Where(conditions...).First()
			if err != nil {
				return
			}
			//剩余数量 ReturnNum 是负数
			remainingNum := first.Nums + first.ReturnNum
			if remainingNum-v.Num < 0 {
				err = errno.Forbidden.WithMsg("商品数量不足")
				return
			}
			nums := remainingNum - v.Num
			//减少金额
			amount := float64(v.Num) * first.ProductPrice
			if nums == 0 { //如果为0 删除订单
				_, err = txOrderInfoDetails.Where(conditions...).Delete()
				if err != nil {
					return
				}
			} else {
				_, err = txOrderInfoDetails.Where(conditions...).
					UpdateSimple(txOrderInfoDetails.Nums.Sub(v.Num))
				if err != nil {
					return
				}
			}
			order := insbuy.InsOrderInfo{}
			err = txOrderInfo.Where(txOrderInfo.OrderId.Eq(v.OrderId)).
				Select(txOrderInfo.OrderAmount, txOrderInfo.GoodsAmount).Scan(&order)
			if err != nil {
				return
			}
			_, err = txOrderInfo.Where(txOrderInfo.OrderId.Eq(v.OrderId)).
				Updates(insbuy.InsOrderInfo{
					GoodsAmount: order.GoodsAmount - amount,
					OrderAmount: order.OrderAmount - amount,
				})
			if err != nil {
				return
			}
			orderInfo = append(orderInfo, insbuyReq.OrderRefundParams{
				OrderDetailsId: uint64(first.ID),
				Nums:           v.Num,
			})
		}
		//仓库订单退单
		err = gSrv.KitTryReturnStock(tx, KitTryReturnStockParams{
			OrderInfo: orderInfo,
			StoreId:   req.GetUintStoreId(),
			UserId:    req.GetUserID(),
			IsDeleted: true,
		})
		if err != nil {
			return
		}
		resp.OrderId = saveRes.OrderId
		return
	})
	if err != nil {
		return
	}
	return
}

// GetMemberIdByOpenDeskId 根据openDeskId 查询会员id
func (S *InsBookInService) GetMemberIdByOpenDeskId(openDeskId uint) (memberId uint, err error) {
	var (
		dbDeskOpen = query.InsDeskOpen
	)
	err = dbDeskOpen.Where(dbDeskOpen.ID.Eq(openDeskId)).Select(dbDeskOpen.MemberId).Scan(&memberId)
	if err != nil {
		return
	}
	return
}

// OpenAction 开台动作
func (S *InsBookInService) OpenAction() insbuy.DeskStatus {
	return S.DeskActionStatus(insbuy.DActionOpen)
}

// MergeAction 并桌动作
func (S *InsBookInService) MergeAction() insbuy.DeskStatus {
	return S.DeskActionStatus(insbuy.DActionMerge)
}

// ChangeAction 换桌动作
func (S *InsBookInService) ChangeAction() insbuy.DeskStatus {
	return S.DeskActionStatus(insbuy.DActionChange)
}

// DeskActionStatus 桌台动作对应状态
func (S *InsBookInService) DeskActionStatus(action insbuy.DeskAction) (res insbuy.DeskStatus) {
	switch action {
	case insbuy.DActionClean:
		res = insbuy.DeskStatusDf
	case insbuy.DActionChange:
		res = insbuy.DeskStatusDf
	case insbuy.DActionOpen:
		res = insbuy.DeskStatusOrder
	case insbuy.DActionMerge:
		res = insbuy.DeskStatusDf
	}
	return
}

// GetDeskAreaByDeskId 查询桌台的区域
func (S *InsBookInService) GetDeskAreaByDeskId(openDeskId uint) (res insbuyResp.InsBookInDeskArea, err error) {
	var (
		dbDeskArea = query.InsDeskArea
		dbDesk     = query.InsDesk
	)
	err = dbDesk.Where(dbDesk.OpenDeskId.Eq(openDeskId)).Join(dbDeskArea, dbDesk.DeskAreaId.EqCol(dbDeskArea.ID)).Select(
		dbDeskArea.ID.As("area_id"),
		dbDeskArea.Name.As("area_name"),
		dbDeskArea.WarehouseId,
	).Scan(&res)
	if err != nil {
		return
	}
	return
}

// GetTurnoverByDeskId 根据桌台获取翻台数
func (S *InsBookInService) GetTurnoverByDeskId(deskId uint) (res int, err error) {
	redis := global.GVA_REDIS
	if redis == nil {
		return
	}
	ctx := context.Background()
	//格式化当天日期+桌台id
	timeKey := fmt.Sprintf("%s_%d", time.Now().Format("2006-01-02"), deskId)
	key := cache.GetTurnoverNumKey(timeKey)
	err = redis.Get(ctx, key).Scan(&res)
	if err != nil {
		return
	}
	return
}

// 增加翻台数
func (S *InsBookInService) nextTurnoverNum(tx *query.Query, deskId uint, storeId uint) (res int, err error) {
	res, err = S.maxTurnoverNumByDeskId(tx, deskId, storeId)
	if err != nil {
		return
	}
	res++
	return
}

// maxTurnoverNumByDeskId 获取当前桌台的最大翻台数
func (S *InsBookInService) maxTurnoverNumByDeskId(tx *query.Query, deskId uint, storeId uint) (res int, err error) {
	var (
		dbDeskOpen = tx.InsDeskOpen
	)
	reportRes, err := S.GetStoreRevenueReport(tx, storeId)
	if err != nil {
		return
	}
	_, endOfDay := gSrv.GetTodayTime()
	do := dbDeskOpen.Where(dbDeskOpen.DeskId.Eq(deskId)).
		Where(dbDeskOpen.OpTime.Between(reportRes.StartTime, endOfDay)).DO
	SelectAppend(&do,
		clause.Expr{
			SQL: "IFNULL(MAX(?),0) as turnover_num",
			Vars: []interface{}{
				dbDeskOpen.TurnoverNum.RawExpr()},
			WithoutParentheses: false,
		},
	)
	err = do.Scan(&res)
	if err != nil {
		return
	}
	return
}

// SetTurnoverByDeskId 根据桌台设置翻台数
func (S *InsBookInService) SetTurnoverByDeskId(deskId uint) (err error) {
	redis := global.GVA_REDIS
	ctx := context.Background()
	//格式化当天日期+桌台id
	timeKey := fmt.Sprintf("%s_%d", time.Now().Format("2006-01-02"), deskId)
	key := cache.GetTurnoverNumKey(timeKey)
	err = redis.Incr(ctx, key).Err()
	if err != nil {
		return
	}
	err = redis.Expire(ctx, key, time.Hour*24).Err()
	if err != nil {
		return
	}
	return
}

// GetDeskConsumed 获取所有桌台的消费和待付金额
func (S *InsBookInService) GetDeskConsumed(req insbuyReq.GetDeskConsumedReq) (res consumedmodel.DeskConsumed, err error) {
	storeId := req.GetUintStoreId()
	report, err := gSrv.GetStoreRevenueReport(query.Q, storeId)
	if err != nil {
		return
	}
	res = consumedmodel.DeskConsumed{
		BusinessDay:         report.OpenDate.Format(time.DateOnly),
		Consumed:            jtypes.JPrice(report.Revenue),
		WaitAmount:          jtypes.JPrice(report.UnpaidAmount),
		PaidAmount:          jtypes.JPrice(report.PaidAmount),
		GiveAmount:          jtypes.JPrice(report.GiveAmount),
		BalancePayAmount:    jtypes.JPrice(report.BalancePayAmount),
		DiscountFee:         jtypes.JPrice(report.DiscountFee), //这里不用叠加已经外外部叠加过了
		CouponFee:           jtypes.JPrice(report.CouponFee),
		ServiceFee:          jtypes.JPrice(report.ServiceFee),
		PlayerPrice:         jtypes.JPrice(report.PlayerPrice),
		TicketAmount:        jtypes.JPrice(report.TicketAmount),
		OfflineTicketAmount: jtypes.JPrice(report.OfflineTicketAmount),
	}
	return
}

// GetOrderConsumed 根据桌台计算消费等数据
func (S *InsBookInService) GetOrderConsumed(openDeskIds []int) (res map[int]*insbuyResp.GetOpenDeskOrderAmountRes, err error) {
	uOpenDeskIds := make([]uint, 0)
	for _, v := range openDeskIds {
		uOpenDeskIds = append(uOpenDeskIds, uint(v))
	}
	//获取赠送记录
	giveMap, err := gSrv.GetGiftRecordByOpenDeskId(uOpenDeskIds)
	if err != nil {
		return
	}
	//获取所有订单
	orderList, err := S.GetOrderListByOpenDeskId(openDeskIds)
	if err != nil {
		return
	}
	consumed := S.CalculateDeskConsumed(orderList)

	billMap, err := gSrv.GetBillMap(openDeskIds)
	if err != nil {
		return
	}
	res, err = S.GetOrderMap(consumed, billMap, giveMap)
	if err != nil {
		return
	}
	return
}

// CalculateDeskConsumed 计算桌台消费
func (S *InsBookInService) CalculateDeskConsumed(orderList []insbuyResp.GetOpenDeskOrderAmountRes) (res map[int]*insbuyResp.GetOpenDeskOrderAmountRes) {
	orderMap := make(map[int]*insbuyResp.GetOpenDeskOrderAmountRes)
	for _, val := range orderList {
		if _, ok := orderMap[int(val.OpenDeskId)]; !ok {
			orderMap[int(val.OpenDeskId)] = &insbuyResp.GetOpenDeskOrderAmountRes{
				OpenDeskId: val.OpenDeskId,
				OrderSn:    val.OrderSn,
			}
		}
		order := orderMap[int(val.OpenDeskId)]
		order.Consumed += val.Consumed
		order.WaitAmount += val.WaitAmount
		order.PaidAmount += val.PaidAmount
		order.DiscountFee += val.DiscountFee + val.CouponFee + val.ErasePrice //优惠金额+优惠券金额+抹零
		order.PlayerPrice += val.PlayerPrice
		order.CouponFee += val.CouponFee
		order.ServiceFee += val.ServiceFee
		orderMap[int(val.OpenDeskId)] = order
	}
	res = orderMap
	return
}

// CalculateSaleConsumed 计算销售消费
func (S *InsBookInService) CalculateSaleConsumed(orderList []insbuyResp.GetOpenDeskOrderAmountRes) (res map[int]*insbuyResp.GetOpenDeskOrderAmountRes) {
	orderMap := make(map[int]*insbuyResp.GetOpenDeskOrderAmountRes)
	for _, val := range orderList {
		if _, ok := orderMap[int(val.SalesmanId)]; !ok {
			orderMap[int(val.SalesmanId)] = &insbuyResp.GetOpenDeskOrderAmountRes{
				OpenDeskId: val.SalesmanId,
				OrderSn:    val.OrderSn,
			}
		}
		order := orderMap[int(val.SalesmanId)]
		order.Consumed += val.Consumed
		orderMap[int(val.SalesmanId)] = order
	}
	res = orderMap
	return
}

// CalculateConsumed 计算桌台订单总额OrderAmount
func (S *InsBookInService) CalculateConsumed(consumed map[int]*insbuyResp.GetOpenDeskOrderAmountRes) (res consumedmodel.DeskConsumed) {
	for _, v := range consumed {
		res.Consumed += v.Consumed
		res.WaitAmount += v.WaitAmount
		res.PaidAmount += v.PaidAmount
		res.GiveAmount += v.GiveAmount
		res.DiscountFee += v.DiscountFee + v.CouponFee
		res.CouponFee += v.CouponFee
		res.ServiceFee += v.ServiceFee
		res.BalancePayAmount += v.BalancePayAmount
	}
	return
}

// GetOpenDeskConsumedRecord 桌台信息记录接口
// 主要为小程序使用
// 商品记录
// 赠送记录
// 存取酒记录
// 其他记录
func (S *InsBookInService) GetOpenDeskConsumedRecord(req insbuyReq.GetOpenDeskConsumedRecord) (resp insbuyResp.GetOpenDeskConsumedRecordResp, err error) {
	//拆分为不同的方法
	var (
		dbOrderInfo        = query.InsOrderInfo
		dbOrderInfoDetails = query.InsOrderInfoDetails
	)
	err = dbOrderInfo.Where(dbOrderInfo.OpenDeskId.Eq(req.OpenDeskId)).
		Join(dbOrderInfoDetails, dbOrderInfo.OrderId.EqCol(dbOrderInfoDetails.OrderId)).
		Select(
			dbOrderInfoDetails.ProductName,
			dbOrderInfoDetails.Nums.As("product_num"),
			dbOrderInfoDetails.ProductPrice,
			dbOrderInfoDetails.ShipmentStatus,
			dbOrderInfoDetails.OrderStatus,
			dbOrderInfoDetails.OrderType,
		).
		Scan(&resp.OrderRecord)
	if err != nil {
		return
	}
	openDeskIds := []uint{uint(req.OpenDeskId)}
	//赠送
	resp.GiftRecord, err = gSrv.GetGiftRecordByOpenDeskId(openDeskIds)
	if err != nil {
		return
	}
	//存取酒记录
	resp.DepositRecord, err = gSrv.GetDeskDepositRecordByOpenDeskIds(openDeskIds)
	if err != nil {
		return
	}
	return
}

// GetOpenDeskIdsByStoreId  根据分店id 获取当天所有开台的openDeskIds(包含已关闭的)
func (S *InsBookInService) GetOpenDeskIdsByStoreId(storeId []int, businessDay time.Time) (openDeskIds []int, err error) {
	var (
		dbDeskOpen = query.InsDeskOpen
	)
	uStoreIds := make([]uint, 0)
	for _, v := range storeId {
		uStoreIds = append(uStoreIds, uint(v))
	}
	conds := []gen.Condition{
		dbDeskOpen.StoreId.In(uStoreIds...),
		dbDeskOpen.BusinessDay.Gte(businessDay),
		dbDeskOpen.BusinessDay.Lte(businessDay),
	}
	do := dbDeskOpen.
		Where(conds...).DO
	SelectAppend(&do,
		dbDeskOpen.ID.As("open_desk_id"),
	)
	err = do.Limit(5000).Scan(&openDeskIds)
	if err != nil {
		err = errno.QueryFailed.WithError(err)
		return
	}
	return
}

// GetStoreRevenueReport 获取当前营业的班结报表
func (S *InsBookInService) GetStoreRevenueReport(tx *query.Query, storeId uint) (res insbuy.InsStoreRevenueReport, err error) {
	var (
		dbStoreRevenueReport = query.InsStoreRevenueReport
	)
	if tx != nil {
		dbStoreRevenueReport = &tx.InsStoreRevenueReport
	}
	conditions := make([]gen.Condition, 0)
	conditions = append(conditions, dbStoreRevenueReport.StoreId.Eq(storeId))
	conditions = append(conditions, dbStoreRevenueReport.StoreStatus.Eq(1))
	err = dbStoreRevenueReport.Where(conditions...).Limit(1).Scan(&res)
	if err != nil {
		return
	}
	return
}

// GetStoreRevenueReportByDate 获取指定日期的营业班结表
func (S *InsBookInService) GetStoreRevenueReportByDate(tx *query.Query, storeId uint, businessDay time.Time) (res insbuy.InsStoreRevenueReport, err error) {
	var (
		dbStoreRevenueReport = query.InsStoreRevenueReport
	)
	if tx != nil {
		dbStoreRevenueReport = &tx.InsStoreRevenueReport
	}
	conditions := make([]gen.Condition, 0)
	conditions = append(conditions, dbStoreRevenueReport.StoreId.Eq(storeId))
	conditions = append(conditions, dbStoreRevenueReport.OpenDate.Gte(businessDay))
	conditions = append(conditions, dbStoreRevenueReport.OpenDate.Lte(businessDay))
	err = dbStoreRevenueReport.Where(conditions...).Scan(&res)
	if err != nil {
		return
	}
	return
}

// EndBusinessDay 结束营业日
func (S *InsBookInService) EndBusinessDay(req insbuyReq.EndBusinessDayReq) (err error) {
	err = TryTransaction(func(tx *query.Query) (err error) {
		businessDay := _businessDayFormStr(req.BusinessDay)
		err = S.tryEndBusinessDay(context.Background(), tx, req.GetUintStoreId(), businessDay, req.GetUserID())
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}
	return
}

func (S *InsBookInService) tryEndBusinessDay(ctx context.Context, tx *query.Query, storeId uint, businessDay time.Time, uid uint) (err error) {
	//查询是否已经结束营业日
	reportRes, err := S.GetStoreRevenueReport(tx, storeId)
	if err != nil {
		return
	}
	//验证是否还有台没有清除
	count, err := gSrv.GetOpenDeskCountByStoreId(int(storeId))
	if err != nil {
		return
	}
	if count > 0 {
		err = errno.Forbidden.WithMsg("还有桌台未清台,不能结束营业")
		return
	}
	report, err := S.GetOrderReportToday(storeId, businessDay)
	if err != nil {
		return
	}
	S.RefreshDayReport(tx, report, uid, reportRes.ID, true)
	return
}

// ManualStartBusinessDay 手动开始营业日
func (S *InsBookInService) ManualStartBusinessDay(req insbuyReq.StartBusinessDayReq) (err error) {
	err = TryTransaction(func(tx *query.Query) (err error) {
		storeId := req.GetUintStoreId()
		err = S.tyrStartBusinessDay(context.Background(), tx, storeId)
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}
	return
}

func (S *InsBookInService) tyrStartBusinessDay(ctx context.Context, tx *query.Query, storeId uint) (err error) {
	//查询是否已经结束营业日
	reportRes, err := S.GetStoreRevenueReport(tx, storeId)
	if err != nil {
		return
	}
	if reportRes.ID > 0 {
		err = errno.Forbidden.WithMsg("当前分店已经开始营业")
		return
	}
	err = S.CreateNextDayReport(tx, storeId)
	if err != nil {
		return
	}
	return
}

// CreateNextDayReport 创建下一日班结表
func (S *InsBookInService) CreateNextDayReport(tx *query.Query, storeId uint) (err error) {
	logger := global.GVA_LOG.With(zap.String("func", "CreateNextDayReport"), zap.Uint("storeId", storeId))
	var (
		dbStoreRevenueReport = tx.InsStoreRevenueReport
	)
	day := jgorm.DefaultBusinessDay(time.Now(), "", "")
	startTime := day.Add(9 * time.Hour) //固定为营业日的早上9点
	openDate := time.Date(day.Year(), day.Month(), day.Day(), 0, 0, 0, 0, time.Local)
	data := insbuy.InsStoreRevenueReport{
		StoreId:     storeId,
		StoreStatus: 1,
		StartTime:   startTime,
		OpenDate:    openDate,
	}
	err = dbStoreRevenueReport.Omit(dbStoreRevenueReport.EndTime).Create(&data)
	if err != nil {
		return
	}
	logger.Info("创建下一日班结表成功", zap.Time("openDate", openDate))
	ebus.Publish(global.EVENTBUSINESSStart, context.Background(), nil, data)
	return
}

func (S *InsBookInService) RefreshDayReport(tx *query.Query, params insbuyResp.CalculateOrderReportRes, operatorId uint, reportID uint, isEnd bool) {
	var (
		dbStoreRevenueReport = query.InsStoreRevenueReport
	)
	if tx != nil {
		dbStoreRevenueReport = &tx.InsStoreRevenueReport
	}
	conditions := make([]gen.Condition, 0)
	conditions = append(conditions, dbStoreRevenueReport.ID.Eq(reportID))
	report := insbuy.InsStoreRevenueReport{
		OpenNum:             params.OpenNum,
		OrderNum:            params.OrderNum,
		Revenue:             params.TotalAmount,
		GiveAmount:          params.GiftAmount,
		PaidAmount:          params.PaidAmount,
		UnpaidAmount:        params.UnpaidAmount,
		DiscountFee:         params.DiscountFee,
		CouponFee:           params.CouponFee,
		ServiceFee:          params.ServiceFee,
		ErasePrice:          params.ErasePrice,
		PlayerPrice:         params.PlayerPrice,
		BalancePayAmount:    params.BalancePayAmount,
		BalanceGiveAmount:   params.BalanceGiftAmount,
		TicketAmount:        params.TicketAmount,
		OfflineTicketAmount: params.OfflineTicketAmount,
		OperatorId:          operatorId,
	}
	omit := []field.Expr{
		dbStoreRevenueReport.StartTime,
		dbStoreRevenueReport.OpenDate,
	}
	if isEnd {
		if params.History.IsZero() { //历史数据不更新结束时间
			report.EndTime = time.Now()
		} else {
			report.EndTime = params.History
		}
		report.StoreStatus = 3
	} else {
		report.StoreStatus = 1
		omit = append(omit, dbStoreRevenueReport.EndTime)
	}
	_, err := dbStoreRevenueReport.Where(conditions...).Select(
		dbStoreRevenueReport.OpenNum,
		dbStoreRevenueReport.OrderNum,
		dbStoreRevenueReport.Revenue,
		dbStoreRevenueReport.GiveAmount,
		dbStoreRevenueReport.PaidAmount,
		dbStoreRevenueReport.UnpaidAmount,
		dbStoreRevenueReport.BalancePayAmount,
		dbStoreRevenueReport.BalanceGiveAmount,
		dbStoreRevenueReport.OperatorId,
		dbStoreRevenueReport.EndTime,
		dbStoreRevenueReport.StoreStatus,
		dbStoreRevenueReport.StartTime,
		dbStoreRevenueReport.OpenDate,
		dbStoreRevenueReport.DiscountFee,
		dbStoreRevenueReport.CouponFee,
		dbStoreRevenueReport.ServiceFee,
		dbStoreRevenueReport.ErasePrice,
		dbStoreRevenueReport.PlayerPrice,
		dbStoreRevenueReport.TicketAmount,
		dbStoreRevenueReport.OfflineTicketAmount,
	).Omit(omit...).Updates(report)
	if err != nil {
		return
	}
	if isEnd {
		ebus.Publish(global.EVENTBUSINESSEnd, context.Background(), tx, report)
	}
	return
}

// GetStoreRevenueReportList 获取当前正在营业的所有班结表数据
func (S *InsBookInService) GetStoreRevenueReportList() (res []insbuy.InsStoreRevenueReport, err error) {
	var (
		dbStoreRevenueReport = query.InsStoreRevenueReport
	)
	conditions := make([]gen.Condition, 0)
	conditions = append(conditions, dbStoreRevenueReport.StoreStatus.Eq(1))
	err = dbStoreRevenueReport.Where(conditions...).Scan(&res)
	if err != nil {
		return
	}
	return
}

// RefreshBusinessData 手动刷新营业数据
func (S *InsBookInService) RefreshBusinessData(req insbuyReq.EndBusinessDayReq) (err error) {
	err = TryTransaction(func(tx *query.Query) (err error) {
		//查询是否已经结束营业日
		var (
			storeId   = req.GetUintStoreId()
			reportRes = insbuy.InsStoreRevenueReport{}
		)
		businessDay := _businessDayFormStr(req.BusinessDay)
		if !businessDay.IsZero() {
			reportRes, err = S.GetStoreRevenueReportByDate(tx, storeId, businessDay)
		} else {
			reportRes, err = S.GetStoreRevenueReport(tx, storeId)
			if err != nil {
				return
			}
		}
		report, err := S.GetOrderReportToday(storeId, businessDay)
		if err != nil {
			return
		}
		var isEnd bool
		if reportRes.StoreStatus == 3 {
			isEnd = true
			report.History = reportRes.EndTime
		}
		S.RefreshDayReport(tx, report, req.GetUserID(), reportRes.ID, isEnd)
		return
	})
	if err != nil {
		return
	}
	return
}

// GetOrderReportToday 获取当天营业数据
func (S *InsBookInService) GetOrderReportToday(storeId uint, businessDay time.Time) (report insbuyResp.CalculateOrderReportRes, err error) {
	var openDeskId []int
	openDeskId, err = gSrv.GetOpenDeskIdsByStoreId([]int{int(storeId)}, businessDay)
	if err != nil {
		return
	}
	bill, err := gSrv.GetOrderBillByTime(openDeskId, false)
	if err != nil {
		return
	}
	//获取所有订单
	orderList, err := gSrv.GetOrderListByOpenDeskId(openDeskId)
	if err != nil {
		return
	}
	//获取赠送记录
	giveMap, err := gSrv.GetGiftRecordByOpenDeskId(S.convertUintOpenDeskId(openDeskId))
	if err != nil {
		return
	}
	//获取门票数据
	tickets, err := insstore.TicketConsumeFromLocal(context.Background(), query.Q, insstore.TicketConsumeParams{
		StoreId:   []uint{storeId},
		StartTime: businessDay,
		EndTime:   businessDay,
	})
	if err != nil {
		return
	}
	//获取所有订单
	report = gSrv.CalculateOrderReport(bill, orderList, giveMap)
	for _, v := range tickets {
		if v.DataType == 1 {
			report.TicketAmount += v.VerifyAmount
		} else {
			report.OfflineTicketAmount += v.VerifyAmount
		}
	}
	report.OpenNum = len(openDeskId)
	return
}

// 转换为uintOpenDeskId
func (S *InsBookInService) convertUintOpenDeskId(openDeskIds []int) (res []uint) {
	for _, v := range openDeskIds {
		res = append(res, uint(v))
	}
	return
}

// GetOpenDeskCountByStoreId 获取当前分店的开台数量
func (S *InsBookInService) GetOpenDeskCountByStoreId(storeId int) (count int64, err error) {
	var (
		dbDesk = query.InsDesk
	)
	count, err = dbDesk.Where(dbDesk.StoreId.Eq(storeId)).
		Where(dbDesk.OpenDeskId.Gt(0)).Count()
	if err != nil {
		return
	}
	return
}

// GetOpenDeskInfo 桌台查询接口
// - 常用信息
// - 商品消费
// - 支取商品
// - 待支付
// - 其他信息
func (S *InsBookInService) GetOpenDeskInfo(req insbuyReq.GetOpenDeskInfoReq) (resp insbuyResp.GetOpenDeskInfoResp, err error) {
	//返回一个空结构体 GetOpenDeskInfoResp
	//
	openDeskId := req.OpenDeskId
	common, err := S.GetOpenDeskBaseInfo(openDeskId)
	if err != nil {
		return
	}
	goods, err := S.GetOpenDeskGoodsInfo(openDeskId)
	if err != nil {
		return
	}
	takeWine, err := S.GetDeskTakeWineLog(openDeskId)
	if err != nil {
		return
	}
	unpaid, err := S.GetDeskBillInfo(openDeskId)
	if err != nil {
		return
	}
	common.GoodsAmount = unpaid.ActualPrice
	common.PaidGoodsAmount = unpaid.ActualPrice
	common.ConsumptionAmount = unpaid.ActualPrice
	common.DiscountFee = unpaid.DiscountFee
	other, err := S.GetOpenDeskOtherInfo(openDeskId)
	if err != nil {
		return
	}
	goodsAmount := 0.0
	for _, v := range goods {
		goodsAmount += v.TotalPrice
	}
	resp = insbuyResp.GetOpenDeskInfoResp{
		Common:       common,
		Goods:        goods,
		TakeWine:     takeWine,
		Unpaid:       unpaid,
		Other:        other,
		GoodsAmount:  goodsAmount,
		UnpaidAmount: unpaid.NeedPayPrice,
	}

	return
}

// GetOpenDeskBaseInfo 获取开台基础信息
func (S *InsBookInService) GetOpenDeskBaseInfo(openDeskId uint) (res insbuyResp.GetOpenDeskInfoCommon, err error) {
	var (
		dbDeskOpen     = query.InsDeskOpen
		dbDesk         = query.InsDesk
		dbDeskCategory = query.InsDeskCategory
		dbMember       = query.InsVipMember
		dbSalesman     = query.SysUser
	)
	do := dbDeskOpen.Where(dbDeskOpen.ID.Eq(openDeskId)).
		LeftJoin(dbDesk, dbDeskOpen.DeskId.EqCol(dbDesk.ID)).
		LeftJoin(dbDeskCategory, dbDesk.DeskCategoryId.EqCol(dbDeskCategory.ID)).
		LeftJoin(dbMember, dbDeskOpen.MemberId.EqCol(dbMember.ID)).
		LeftJoin(dbSalesman, dbDeskOpen.SalesmanId.EqCol(dbSalesman.ID)).DO
	SelectAppend(&do,
		dbDeskOpen.OpenDeskSn.As("open_sn"),
		dbDeskCategory.Name.As("desk_type"),
		dbSalesman.NickName.As("booker"),
		dbSalesman.NickName.As("salesman"),
		dbMember.UserName.As("vip_name"),
		dbDeskOpen.OpTime.DateFormat("%Y-%m-%d %H:%i:%s").As("open_time"),
		clause.Expr{
			SQL: "IFNULL(?,?) as vip_name",
			Vars: []interface{}{
				dbMember.UserName.RawExpr(),
				dbDeskOpen.TempMemberName.RawExpr(),
			},
			WithoutParentheses: false,
		},
	)
	err = do.Scan(&res)
	if err != nil {
		return
	}
	return
}

// GetOpenDeskGoodsInfo 获取开台商品信息
func (S *InsBookInService) GetOpenDeskGoodsInfo(openDeskId uint) (res []insbuyResp.GetOpenDeskInfoGoods, err error) {
	var (
		dbOrderInfo        = query.InsOrderInfo
		dbOrderInfoDetails = query.InsOrderInfoDetails
		dbProduct          = query.InsProduct
		dbWaiter           = query.SysUser
	)
	gift := dbWaiter.As("gift")
	do := dbOrderInfo.Where(dbOrderInfo.OpenDeskId.Eq(int(openDeskId)), dbOrderInfoDetails.DeletedAt.IsNull()).
		Join(dbOrderInfoDetails, dbOrderInfo.OrderId.EqCol(dbOrderInfoDetails.OrderId)).
		LeftJoin(dbProduct, dbOrderInfoDetails.ProductId.EqCol(dbProduct.ID)).
		LeftJoin(dbWaiter, dbOrderInfoDetails.WaiterId.EqCol(dbWaiter.ID)).
		LeftJoin(gift, dbOrderInfoDetails.GiftUserId.EqCol(gift.ID)).DO
	SelectAppend(&do,
		clause.Expr{
			SQL: "(?+?)  as nums2",
			Vars: []interface{}{
				dbOrderInfoDetails.Nums.RawExpr(),
				dbOrderInfoDetails.ReturnNum.RawExpr(),
			},
			WithoutParentheses: false,
		},
		clause.Expr{
			SQL: "(?+?)*? as total_price",
			Vars: []interface{}{
				dbOrderInfoDetails.Nums.RawExpr(),
				dbOrderInfoDetails.ReturnNum.RawExpr(),
				dbOrderInfoDetails.ProductPrice.RawExpr(),
			},
			WithoutParentheses: false,
		},
		dbOrderInfoDetails.ID.As("order_details_id"),
		dbOrderInfoDetails.OrderType,
		dbProduct.IsPackage,
		dbOrderInfoDetails.ProductId,
		dbOrderInfoDetails.ProductName,
		dbOrderInfoDetails.Nums.As("product_num"),
		dbOrderInfoDetails.ReturnNum,
		dbOrderInfoDetails.ProductPrice,
		dbOrderInfoDetails.Unit,
		dbOrderInfoDetails.Remark,
		dbOrderInfoDetails.OptionItems,
		dbOrderInfoDetails.GiftUserId,
		dbWaiter.NickName.As("waiter_name"),
		gift.NickName.As("gift_user_name"),
		dbOrderInfo.CreatedAt.As("order_time"),
	)
	err = do.Having(field.NewUint("", "nums2").Gt(0)).Scan(&res)
	if err != nil {
		return
	}
	if err != nil {
		return
	}
	detailsIds := make([]uint64, 0)
	for _, v := range res {
		detailsIds = append(detailsIds, v.OrderDetailsId)
	}
	packageMap, err := gSrv.GetOrderPackageDetailsMapByIds(detailsIds, "")
	if err != nil {
		return
	}
	for k, v := range res {
		if val, ok := packageMap[v.OrderDetailsId]; ok {
			for _, info := range val {
				res[k].SubProduct = append(res[k].SubProduct, insbuyResp.GetOpenDeskInfoGoodsSubProduct{
					SubProductName: info.ProductName,
					SubProductNum:  info.ProductNum,
					SubProductUnit: info.Unit,
				})
			}
		}
	}
	return
}

// GetDeskTakeWineLog openDeskId 获取桌台取酒信息
func (S *InsBookInService) GetDeskTakeWineLog(openDeskId uint) (res []insbuyResp.GetOpenDeskInfoTakeWine, err error) {
	var (
		dbDeposit    = query.InsDeposit
		dbDepositLog = query.InsDepositLog
		dbVipMember  = query.InsVipMember
		dbMaterial   = query.InsMaterial
	)
	res = make([]insbuyResp.GetOpenDeskInfoTakeWine, 0)
	//添加当前桌台取酒的数据
	err = dbDeposit.Where([]gen.Condition{
		dbDeposit.OpenDeskId.Eq(openDeskId),
		dbDeposit.Type.Eq(insbuy.DepositOutType),
		dbDeposit.Status.Eq(1),
	}...).
		LeftJoin(dbDepositLog, dbDeposit.ID.EqCol(dbDepositLog.DepositId)).
		Join(dbMaterial, dbDepositLog.MaterialId.EqCol(dbMaterial.ID)).
		LeftJoin(dbVipMember, dbDeposit.VipMemberId.EqCol(dbVipMember.ID)).
		Select(
			dbDepositLog.ProductId,
			dbMaterial.MaterialName.As("product_name"),
			dbDepositLog.Spec,
			dbDepositLog.Num.As("product_num"),
			dbDepositLog.CreatedAt.As("take_wine_time"),
			dbVipMember.UserName.As("take_wine_name"),
		).Scan(&res)
	if err != nil {
		return
	}
	return
}

// GetDeskBillInfo 获取当前桌台账单信息
func (S *InsBookInService) GetDeskBillInfo(openDeskId uint) (res insbuyResp.GetOpenDeskInfoUnpaid, err error) {
	resp, err := gSrv.GetOrderBill(insbuyReq.GetOrderBillReq{
		OpenDeskId: int(openDeskId),
	})
	if err != nil {
		return
	}
	res = insbuyResp.GetOpenDeskInfoUnpaid{
		ReceivablePrice: resp.ReceivablePrice,
		ActualPrice:     resp.ActualPrice,
		NeedPayPrice:    resp.NeedPayPrice,
		DiscountFee:     resp.DiscountAmount + resp.PlayerPrice,
		ServiceFee:      resp.ServiceFee,
	}
	return
}

// GetOpenDeskOtherInfo 获取其他信息
func (S *InsBookInService) GetOpenDeskOtherInfo(openDeskId uint) (res insbuyResp.GetOpenDeskInfoOther, err error) {
	var (
		dbDeskOpen = query.InsDeskOpen
		dbDesk     = query.InsDesk
	)
	err = dbDeskOpen.Where(dbDeskOpen.ID.Eq(openDeskId)).
		Join(dbDesk, dbDeskOpen.DeskId.EqCol(dbDesk.ID)).
		Select(
			dbDesk.DeskName.As("open_room_name"),
			dbDeskOpen.PeopleNum,
			dbDeskOpen.MinAmount.As("min_consumption"),
			dbDeskOpen.Remark,
		).Scan(&res)
	if err != nil {
		return
	}
	return
}

// ScanCodeOrder 扫码点单接口
// 根据qr code 查询对应的桌台和店铺信息
func (S *InsBookInService) ScanCodeOrder(req insbuyReq.ScanCodeOrderReq) (res insbuyResp.ScanCodeOrderResp, err error) {
	logger := global.GVA_LOG.With(zap.String("func", "ScanCodeOrder"), zap.Any("req", req))
	if req.QrCode == "" {
		err = errno.QueryFailed.WithMsg("桌台信息获取失败")
		return
	}
	dbDesk := query.InsDesk
	dbDeskArea := query.InsDeskArea
	dbStore := query.InsStore
	dbOpen := query.InsDeskOpen
	err = dbDesk.Where(dbDesk.QrCodeKey.Eq(req.QrCode)).
		LeftJoin(dbOpen, dbDesk.OpenDeskId.EqCol(dbOpen.ID)).
		LeftJoin(dbStore, dbDesk.StoreId.EqCol(dbStore.ID)).
		LeftJoin(dbDeskArea, dbDesk.DeskAreaId.EqCol(dbDeskArea.ID)).
		Select(
			dbStore.ID.As("store_id"),
			dbStore.InsCode.As("ins_code"),
			dbStore.Logo.As("store_logo"),
			dbDesk.ID.As("desk_id"),
			dbDesk.DeskName.As("desk_name"),
			dbDesk.OpenDeskId.As("open_desk_id"),
			dbDeskArea.Name.As("area_name"),
		).
		Scan(&res)
	if err != nil {
		logger.Error("查询桌台信息失败", zap.Error(err))
		return
	}
	if res.DeskId == 0 {
		err = errno.QueryFailed.WithMsg("未找到桌台信息,请重试")
		logger.Error("查询桌台信息失败", zap.Error(err))
		return
	}
	//获取店铺配置的微信小程序appid
	store, err := gSrv.GetInsStore(res.StoreId)
	if err != nil {
		return
	}
	if appid, ok := store.Ext["wx_appid"]; ok {
		res.WxAppId = appid.(string)
	}
	if qrcode, ok := store.Ext["qr_code"]; ok {
		res.WxQrCode = qrcode.(string)
	}
	if subscribe, ok := store.Ext["subscribe"]; ok {
		if i, ok2 := subscribe.(json.Number); ok2 {
			su, _ := i.Int64()
			res.Subscribe = int(su)
		}
	}
	return
}

// ScanOpenDesk 扫码开台接口-免token验证
func (S *InsBookInService) ScanOpenDesk(req insbuyReq.InsBookInOpenDesk) (res *insbuyResp.InsBookInOpenDesk, err error) {
	logger := global.GVA_LOG.With(zap.String("func", "ScanOpenDesk"), zap.Any("req", req))
	err = TryTransaction(func(tx *query.Query) (err error) {
		req.OpenDesk.CustomerSource = 5
		req.OpenDesk.SalesmanId = insbuy.ScanOpenId
		res, err = S.TryOpenDesk(context.Background(), tx, insbuyReq.TryOpenDeskParams{
			UserId:   req.GetUserID(),
			StoreId:  req.GetUintStoreId(),
			SStoreId: req.GetStoreId(),
			OpenDesk: req.OpenDesk,
		})
		if err != nil {
			logger.Error("扫码开台失败", zap.Error(err))
			return
		}
		return
	})
	if err != nil {
		return
	}
	ebus.Publish(global.EVENTDESKOpenSuccess, res.OpenDeskId)
	return
}

// OpenDeskSnapshot 开台报表快照
func (S *InsBookInService) OpenDeskSnapshot(req insbuyReq.OpenDeskSnapshotReq) {
	dp := insreport.DailyReportParam{
		StartDate: jtime.Str2Date(req.StartDate),
		EndDate:   jtime.Str2Date(req.EndDate),
		PageNum:   int(1),
		PageSize:  int(0),
	}
	if req.StoreId != 0 {
		dp.StoreIds = append(dp.StoreIds, req.StoreId)
	}
	resp, _, err := insreport.DailyOpenDesk(context.Background(), query.Q, dp)
	if err != nil {
		return
	}
	err = TryTransaction(func(tx *query.Query) (err error) {
		dbOpenDeskSnapshot := tx.InsOpenDeskSnapshot
		snapshots := make([]*insbuy.InsOpenDeskSnapshot, 0)
		var storeIds []uint
		if list, ok := resp.List.List.([]map[string]interface{}); ok {
			m := make(map[uint]uint)
			for _, v := range list {
				data := &insbuy.InsOpenDeskSnapshot{}
				if storeId, ok2 := v["store_id"].(uint); ok2 {
					m[storeId] = storeId
					data.StoreId = storeId
				}
				if d, ok2 := v["store_name"].(string); ok2 {
					data.StoreName = d
				}
				data.BusinessDay = jtypes.JDate{
					Time:  jtime.Str2Time(fmt.Sprintf("%v-%02v-%02v", v["year"], v["month"], v["day"])),
					Valid: true,
				}
				if d, ok2 := v["desk_id"].(uint); ok2 {
					data.DeskId = d
				}
				if d, ok2 := v["desk_name"].(string); ok2 {
					data.DeskName = d
				}
				if d, ok2 := v["desk_open_id"].(uint); ok2 {
					data.OpenDeskId = d
				}
				if d, ok2 := v["income_amount"].(float64); ok2 {
					data.IncomeAmount = d
				}
				if d, ok2 := v["countable_income"].(float64); ok2 {
					data.CountableIncome = d
				}
				if d, ok2 := v["real_income"].(float64); ok2 {
					data.RealIncome = d
				}
				if d, ok2 := v["charity_fund"].(float64); ok2 {
					data.CharityFund = d
				}
				snapshots = append(snapshots, data)
			}
			for _, v := range m {
				storeIds = append(storeIds, v)
			}
			_, err = dbOpenDeskSnapshot.Where(dbOpenDeskSnapshot.StoreId.In(storeIds...),
				dbOpenDeskSnapshot.BusinessDay.Gte(jtime.Str2Time(req.StartDate)),
				dbOpenDeskSnapshot.BusinessDay.Lte(jtime.Str2Time(req.EndDate))).Delete()
			if err != nil {
				return
			}
			//按批次写入 500条为1批
			batchSize := 500
			for i := 0; i < len(snapshots); i += batchSize {
				lenth := len(snapshots)
				if i+batchSize < len(snapshots) {
					lenth = i + batchSize
				}
				batch := snapshots[i:lenth]
				err = dbOpenDeskSnapshot.CreateInBatches(batch, len(batch))
				if err != nil {
					return
				}
			}
		}
		return
	})
	if err != nil {
		return
	}
	return
}

func (S *InsBookInService) OpenDeskSnapshotJson(req insbuyReq.OpenDeskSnapshotReq) {
	dp := insreport.DailyReportParam{
		StartDate: jtime.Str2Time(req.StartDate),
		EndDate:   jtime.Str2Time(req.EndDate),
		PageNum:   int(1),
		PageSize:  int(0),
	}
	if req.StoreId != 0 {
		dp.StoreIds = append(dp.StoreIds, req.StoreId)
	}
	resp, _, err := insreport.DailyOpenDesk(context.Background(), query.Q, dp)
	if err != nil {
		return
	}
	m := make(map[uint]uint)
	header := make([]datasource.SimpleReportRespDataHeader, 0)
	for _, v := range resp.List.Header {
		multi := []datasource.SimpleReportRespDataHeader{}
		for _, v2 := range v.Multi {
			multi = append(multi, datasource.SimpleReportRespDataHeader{
				Title: v2.Title,
				Field: v2.Field,
				Color: v2.Color,
			})
		}
		dataHeader := datasource.SimpleReportRespDataHeader{
			Title: v.Title,
			Field: v.Field,
			Color: v.Color,
		}
		if len(multi) > 0 {
			dataHeader.Multi = multi
		}
		header = append(header, dataHeader)
	}
	storeIds := []uint{}
	dtos := make(map[uint]datasource.SourceStorageData, 0) //按店
	if list, ok := resp.List.List.([]map[string]interface{}); ok {
		for _, v := range list {
			sprintf := fmt.Sprintf("%v-%02v-%02v", v["year"], v["month"], v["day"])
			if storeId, ok2 := v["store_id"].(uint); ok2 {
				m[storeId] = storeId
				if dt, ok3 := dtos[storeId]; !ok3 {
					//主数据
					data := &insbuy.InsReportIntermediateResult{
						StoreID:  storeId,
						DataType: "JSON",
						Name:     "店铺营业开台表",
						Code:     datasource.DscOpen.ToString(),
						Status:   1,
					}
					ext := datasource.DscOpenDataHeaderExt{
						Header: header,
					}
					data.Ext, _ = json.Marshal(ext)
					//详情
					resultDetails := make([]*insbuy.InsReportIntermediateResultDetails, 0)
					var openDeskId uint = 0
					if o, ok4 := v["desk_open_id"].(uint); ok4 {
						openDeskId = o
					}
					dataKey := fmt.Sprintf("%v-%v-%v", sprintf, data.StoreID, openDeskId)
					details := &insbuy.InsReportIntermediateResultDetails{
						StoreID: storeId,
						DataKey: dataKey,
					}
					str2Time := jtime.Str2Time(sprintf)
					details.BusinessDay = jgorm.NewBusinessDayType(str2Time.Year(), str2Time.Month(), str2Time.Day())
					resultData := &datasource.DscOpenDataExt{
						Field: v,
					}
					details.ResultData, _ = json.Marshal(resultData)
					resultDetails = append(resultDetails, details)
					dtos[storeId] = datasource.SourceStorageData{
						Data: data,
						List: resultDetails,
					}
				} else {
					var openDeskId uint = 0
					if o, ok4 := v["desk_open_id"].(uint); ok4 {
						openDeskId = o
					}
					dataKey := fmt.Sprintf("%v-%v-%v", sprintf, storeId, openDeskId)
					details := &insbuy.InsReportIntermediateResultDetails{
						StoreID: storeId,
						DataKey: dataKey,
					}
					str2Time := jtime.Str2Time(sprintf)
					details.BusinessDay = jgorm.NewBusinessDayType(str2Time.Year(), str2Time.Month(), str2Time.Day())
					resultData := &datasource.DscOpenDataExt{
						Field: v,
					}
					details.ResultData, _ = json.Marshal(resultData)
					dt.List = append(dt.List, details)
					dtos[storeId] = dt
				}
			}
		}
		for _, v := range m {
			storeIds = append(storeIds, v)
		}
		err = insdata.NewDataSourceAdapter(datasource.DscOpen).Storage(context.Background(), query.Q, datasource.SourceStorageParams{
			StartDate: jtime.Str2Time(req.StartDate),
			EndDate:   jtime.Str2Time(req.EndDate),
			StoreIds:  storeIds,
			Data:      dtos,
		})
		if err != nil {
			return
		}
		return
	}
}
