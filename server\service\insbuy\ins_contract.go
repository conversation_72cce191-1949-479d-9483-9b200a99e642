package insbuy

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gorm.io/gen"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insfinance"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type InsContractService struct {
	InsCurrentUserService
}

// GetContractList 获取合同列表
func (s *InsContractService) GetContractList(req request.ContractListRequest) (resp response.ContractListResponse, err error) {
	global.GVA_LOG.Info("开始查询合同列表", zap.Any("request", req))

	db := query.InsContract
	q := db.WithContext(context.Background())

	// 构建查询条件
	conditions := make([]gen.Condition, 0)

	if req.ApprovalCode != "" {
		conditions = append(conditions, db.ApprovalCode.Eq(req.ApprovalCode))
	}
	if req.Status != "" {
		conditions = append(conditions, db.Status.Eq(req.Status))
	}
	if req.ContractType != "" {
		conditions = append(conditions, db.ContractType.Eq(req.ContractType))
	}
	if req.UserId != "" {
		conditions = append(conditions, db.UserId.Eq(req.UserId))
	}
	if req.DepartmentId != "" {
		conditions = append(conditions, db.DepartmentId.Eq(req.DepartmentId))
	}
	if req.InstanceCode != "" {
		conditions = append(conditions, db.InstanceCode.Eq(req.InstanceCode))
	}
	if req.SerialNumber != "" {
		conditions = append(conditions, db.SerialNumber.Like("%"+req.SerialNumber+"%"))
	}
	if req.ContractTitle != "" {
		conditions = append(conditions, db.ContractTitle.Like("%"+req.ContractTitle+"%"))
	}

	// 时间范围查询
	if req.StartTimeFrom != nil {
		conditions = append(conditions, db.StartTime.Gte(*req.StartTimeFrom))
	}
	if req.StartTimeTo != nil {
		conditions = append(conditions, db.StartTime.Lte(*req.StartTimeTo))
	}
	if req.EndTimeFrom != nil {
		conditions = append(conditions, db.EndTime.Gte(*req.EndTimeFrom))
	}
	if req.EndTimeTo != nil {
		conditions = append(conditions, db.EndTime.Lte(*req.EndTimeTo))
	}

	// 金额范围查询
	if req.AmountFrom != nil {
		conditions = append(conditions, db.ContractAmount.Gte(*req.AmountFrom))
	}
	if req.AmountTo != nil {
		conditions = append(conditions, db.ContractAmount.Lte(*req.AmountTo))
	}

	// 应用查询条件 - 使用GORM Gen的链式调用
	for _, condition := range conditions {
		q = q.Where(condition)
	}

	// 排序 - 使用GORM Gen的字段方法
	if req.OrderBy != "" && req.OrderType != "" {
		switch strings.ToLower(req.OrderBy) {
		case "created_at":
			if strings.ToLower(req.OrderType) == "asc" {
				q = q.Order(db.CreatedAt)
			} else {
				q = q.Order(db.CreatedAt.Desc())
			}
		case "start_time":
			if strings.ToLower(req.OrderType) == "asc" {
				q = q.Order(db.StartTime)
			} else {
				q = q.Order(db.StartTime.Desc())
			}
		case "contract_amount":
			if strings.ToLower(req.OrderType) == "asc" {
				q = q.Order(db.ContractAmount)
			} else {
				q = q.Order(db.ContractAmount.Desc())
			}
		default:
			q = q.Order(db.CreatedAt.Desc()) // 默认排序
		}
	} else {
		q = q.Order(db.CreatedAt.Desc()) // 默认按创建时间倒序
	}

	// 分页查询 - 使用GORM Gen的方法
	offset := (req.Page - 1) * req.PageSize

	// 先获取总数
	total, err := q.Count()
	if err != nil {
		global.GVA_LOG.Error("查询合同总数失败", zap.Error(err))
		return
	}

	// 再获取分页数据
	contracts, err := q.Offset(offset).Limit(req.PageSize).Find()
	if err != nil {
		global.GVA_LOG.Error("查询合同列表失败", zap.Error(err))
		return
	}

	// 转换响应数据
	var items []response.ContractItem
	for _, contract := range contracts {
		item := response.ContractItem{
			ID:             contract.ID,
			ApprovalCode:   contract.ApprovalCode,
			ApprovalName:   contract.ApprovalName,
			InstanceCode:   contract.InstanceCode,
			SerialNumber:   contract.SerialNumber,
			Uuid:           contract.Uuid,
			Status:         contract.Status,
			Reverted:       contract.Reverted,
			StartTime:      contract.StartTime,
			EndTime:        contract.EndTime,
			UserId:         contract.UserId,
			OpenId:         contract.OpenId,
			DepartmentId:   contract.DepartmentId,
			ContractType:   contract.ContractType,
			ContractTitle:  contract.ContractTitle,
			ContractAmount: contract.ContractAmount,
			Currency:       contract.Currency,
			CreatedAt:      contract.CreatedAt,
			UpdatedAt:      contract.UpdatedAt,
		}

		// 查询统计信息
		item.CommentCount = s.getContractCommentCount(contract.ID)
		item.TaskCount = s.getContractTaskCount(contract.ID)
		item.TimelineCount = s.getContractTimelineCount(contract.ID)
		item.FileCount = s.getContractFileCount(contract.ID)

		items = append(items, item)
	}

	resp = response.ContractListResponse{
		List:     items,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	global.GVA_LOG.Info("查询合同列表成功",
		zap.Int64("total", total),
		zap.Int("count", len(items)),
	)

	return
}

// GetContractDetail 获取合同详情
func (s *InsContractService) GetContractDetail(req request.ContractDetailRequest) (resp response.ContractDetailResponse, err error) {
	global.GVA_LOG.Info("开始查询合同详情", zap.Any("request", req))

	db := query.InsContract
	q := db.WithContext(context.Background())

	// 根据不同条件查询 - 使用GORM Gen的正确语法
	var contract *insbuy.InsContract
	if req.ID > 0 {
		contract, err = q.Where(db.ID.Eq(req.ID)).First()
	} else if req.InstanceCode != "" {
		contract, err = q.Where(db.InstanceCode.Eq(req.InstanceCode)).First()
	} else if req.Uuid != "" {
		contract, err = q.Where(db.Uuid.Eq(req.Uuid)).First()
	} else {
		err = fmt.Errorf("缺少查询条件")
		return
	}

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			err = fmt.Errorf("合同不存在")
		} else {
			global.GVA_LOG.Error("查询合同详情失败", zap.Error(err))
		}
		return
	}

	resp.InsContract = *contract

	// 查询关联数据
	resp.Comments, err = s.getContractComments(contract.ID)
	if err != nil {
		global.GVA_LOG.Error("查询合同评论失败", zap.Error(err))
		return
	}

	resp.Tasks, err = s.getContractTasks(contract.ID)
	if err != nil {
		global.GVA_LOG.Error("查询合同任务失败", zap.Error(err))
		return
	}

	resp.Timeline, err = s.getContractTimeline(contract.ID)
	if err != nil {
		global.GVA_LOG.Error("查询合同时间线失败", zap.Error(err))
		return
	}

	resp.Files, err = s.getContractFiles(contract.ID)
	if err != nil {
		global.GVA_LOG.Error("查询合同文件失败", zap.Error(err))
		return
	}

	// 解析表单数据
	resp.FormFields, err = s.parseContractForm(contract.Form)
	if err != nil {
		global.GVA_LOG.Error("解析合同表单失败", zap.Error(err))
		// 表单解析失败不影响整体查询
		err = nil
	}

	global.GVA_LOG.Info("查询合同详情成功",
		zap.Uint("contract_id", contract.ID),
		zap.String("instance_code", contract.InstanceCode),
	)

	return
}

// SyncContractData 同步合同数据
func (s *InsContractService) SyncContractData(req request.ContractSyncRequest) (resp response.ContractSyncResponse, err error) {
	global.GVA_LOG.Info("开始同步合同数据", zap.Any("request", req))

	startTime := time.Now()

	// 记录同步日志
	syncLog := insbuy.InsContractSyncLog{
		ApprovalCode: req.ApprovalCode,
		SyncType:     "manual",
		Status:       "running",
		StartTime:    startTime,
	}

	dbSyncLog := query.InsContractSyncLog
	err = dbSyncLog.WithContext(context.Background()).Create(&syncLog)
	if err != nil {
		global.GVA_LOG.Error("创建同步日志失败", zap.Error(err))
		return
	}

	// 创建飞书客户端
	feishuClient := utils.NewFeishuClient()
	err = feishuClient.ValidateConfig()
	if err != nil {
		global.GVA_LOG.Error("飞书配置验证失败", zap.Error(err))
		s.updateSyncLog(syncLog.ID, "failed", 0, err.Error())
		return
	}

	ctx := context.Background()
	var recordCount int
	var errorCount int

	// 设置默认时间范围
	if req.StartTime == nil {
		defaultStart := time.Now().AddDate(0, -1, 0) // 默认查询最近一个月
		req.StartTime = &defaultStart
	}
	if req.EndTime == nil {
		defaultEnd := time.Now()
		req.EndTime = &defaultEnd
	}

	// 分页获取合同列表
	pageToken := ""
	for {
		listReq := utils.ContractListRequest{
			ApprovalCode: req.ApprovalCode,
			StartTime:    *req.StartTime,
			EndTime:      *req.EndTime,
			PageSize:     100,
			PageToken:    pageToken,
		}

		listResp, err := feishuClient.GetContractList(ctx, listReq)
		if err != nil {
			global.GVA_LOG.Error("获取合同列表失败", zap.Error(err))
			errorCount++
			break
		}

		if len(listResp.InstanceCodeList) == 0 {
			break
		}

		// 批量获取合同详情
		details, errors := feishuClient.BatchGetContractDetails(ctx, listResp.InstanceCodeList)
		errorCount += len(errors)

		// 保存合同数据
		for _, detail := range details {
			err = s.saveContractData(detail)
			if err != nil {
				global.GVA_LOG.Error("保存合同数据失败",
					zap.String("instance_code", detail.InstanceCode),
					zap.Error(err),
				)
				errorCount++
			} else {
				recordCount++
			}
		}

		// 检查是否还有更多数据
		if !listResp.HasMore {
			break
		}
		pageToken = listResp.PageToken
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 更新同步日志
	status := "success"
	errorMsg := ""
	if errorCount > 0 {
		status = "partial_success"
		errorMsg = fmt.Sprintf("同步过程中发生 %d 个错误", errorCount)
	}

	s.updateSyncLog(syncLog.ID, status, recordCount, errorMsg)

	resp = response.ContractSyncResponse{
		ApprovalCode: req.ApprovalCode,
		SyncType:     "manual",
		Status:       status,
		Message:      fmt.Sprintf("同步完成，成功处理 %d 条记录", recordCount),
		RecordCount:  recordCount,
		ErrorCount:   errorCount,
		StartTime:    startTime.Format("2006-01-02 15:04:05"),
		EndTime:      endTime.Format("2006-01-02 15:04:05"),
		Duration:     duration.String(),
	}

	global.GVA_LOG.Info("同步合同数据完成",
		zap.String("approval_code", req.ApprovalCode),
		zap.Int("record_count", recordCount),
		zap.Int("error_count", errorCount),
		zap.Duration("duration", duration),
	)

	return
}

// SyncMultipleContractData 多审批代码批量同步合同数据
func (s *InsContractService) SyncMultipleContractData(req request.ContractMultiSyncRequest) (resp response.ContractMultiSyncResponse, err error) {
	startTime := time.Now()

	ctx, logger := global.Log4Task(context.Background(), "SyncMultipleContractData",
		zap.Strings("approval_codes", req.ApprovalCodes),
		zap.Int("total_codes", len(req.ApprovalCodes)),
		zap.Int("batch_size", req.BatchSize),
		zap.Int("page_size", req.PageSize),
	)

	logger.Info("开始多审批代码批量同步合同数据")

	// 参数验证和默认值设置
	if err = s.validateMultiSyncRequest(&req); err != nil {
		logger.Error("参数验证失败", zap.Error(err))
		return response.ContractMultiSyncResponse{
			Success:   false,
			ErrorMsg:  fmt.Sprintf("参数验证失败: %v", err),
			StartTime: startTime.Format("2006-01-02 15:04:05"),
		}, err
	}

	// 初始化响应结构
	resp = response.ContractMultiSyncResponse{
		Success:    true,
		TotalCodes: len(req.ApprovalCodes),
		Results:    make([]response.ContractSyncDetailResult, 0, len(req.ApprovalCodes)),
		StartTime:  startTime.Format("2006-01-02 15:04:05"),
	}

	// 创建飞书合同服务实例
	feishuService := s.createFeishuContractService()
	if feishuService == nil {
		err = fmt.Errorf("创建飞书服务失败")
		logger.Error("创建飞书合同服务失败")
		resp.Success = false
		resp.ErrorMsg = err.Error()
		return
	}

	// 逐个处理每个审批代码
	for i, approvalCode := range req.ApprovalCodes {
		approvalCtx, approvalLogger := global.Log4Task(ctx, "syncSingleApprovalCode",
			zap.String("approval_code", approvalCode),
			zap.Int("index", i+1),
			zap.Int("total", len(req.ApprovalCodes)),
		)

		approvalLogger.Info("开始同步审批代码")

		// 同步单个审批代码
		result := s.syncSingleApprovalCode(approvalCtx, feishuService, approvalCode, req, approvalLogger)
		resp.Results = append(resp.Results, result)

		// 更新统计信息
		if result.Success {
			resp.SuccessCodes++
		} else {
			resp.FailedCodes++
			resp.Success = false // 任何一个失败都标记整体为失败
		}

		resp.TotalRecords += result.TotalRecords
		resp.NewRecords += result.NewRecords
		resp.UpdatedRecords += result.UpdatedRecords
		resp.FailedRecords += result.FailedRecords

		// 审批代码间的延迟，避免API限流
		if i < len(req.ApprovalCodes)-1 {
			time.Sleep(time.Duration(req.RetryDelay) * time.Second)
		}
	}

	endTime := time.Now()
	resp.EndTime = endTime.Format("2006-01-02 15:04:05")
	resp.Duration = endTime.Sub(startTime).String()

	// 设置最终状态
	if resp.FailedCodes > 0 {
		if resp.SuccessCodes > 0 {
			resp.ErrorMsg = fmt.Sprintf("部分同步失败: 成功 %d 个，失败 %d 个审批代码", resp.SuccessCodes, resp.FailedCodes)
		} else {
			resp.ErrorMsg = "所有审批代码同步失败"
		}
	}

	logger.Info("多审批代码批量同步完成",
		zap.Int("total_codes", resp.TotalCodes),
		zap.Int("success_codes", resp.SuccessCodes),
		zap.Int("failed_codes", resp.FailedCodes),
		zap.Int("total_records", resp.TotalRecords),
		zap.Int("new_records", resp.NewRecords),
		zap.Int("updated_records", resp.UpdatedRecords),
		zap.Int("failed_records", resp.FailedRecords),
		zap.String("duration", resp.Duration),
		zap.Bool("overall_success", resp.Success),
	)

	return
}

// validateMultiSyncRequest 验证多审批代码同步请求参数
func (s *InsContractService) validateMultiSyncRequest(req *request.ContractMultiSyncRequest) error {
	if len(req.ApprovalCodes) == 0 {
		return fmt.Errorf("审批代码列表不能为空")
	}

	// 限制审批代码数量，避免单次同步过多
	if len(req.ApprovalCodes) > 20 {
		return fmt.Errorf("单次同步的审批代码数量不能超过20个")
	}

	// 验证审批代码格式
	for i, code := range req.ApprovalCodes {
		if code == "" {
			return fmt.Errorf("第%d个审批代码不能为空", i+1)
		}
		if len(code) < 10 {
			return fmt.Errorf("第%d个审批代码格式不正确: %s", i+1, code)
		}
	}

	// 设置默认值
	if req.BatchSize <= 0 {
		req.BatchSize = 20 // 默认批次大小
	}
	if req.BatchSize > 50 {
		req.BatchSize = 50 // 限制最大批次大小
	}

	if req.PageSize <= 0 {
		req.PageSize = 100 // 默认分页大小
	}
	if req.PageSize > 200 {
		req.PageSize = 200 // 飞书API限制
	}

	if req.MaxRetries <= 0 {
		req.MaxRetries = 3 // 默认重试3次
	}
	if req.MaxRetries > 10 {
		req.MaxRetries = 10 // 限制最大重试次数
	}

	if req.RetryDelay <= 0 {
		req.RetryDelay = 5 // 默认延迟5秒
	}
	if req.RetryDelay > 60 {
		req.RetryDelay = 60 // 限制最大延迟60秒
	}

	// 设置默认时间范围
	if req.StartTime == nil {
		defaultStart := time.Now().AddDate(0, 0, -30) // 默认最近30天
		req.StartTime = &defaultStart
	}
	if req.EndTime == nil {
		defaultEnd := time.Now()
		req.EndTime = &defaultEnd
	}

	// 验证时间范围
	if req.EndTime.Before(*req.StartTime) {
		return fmt.Errorf("结束时间不能早于开始时间")
	}

	// 限制时间范围，避免数据量过大
	if req.EndTime.Sub(*req.StartTime) > 365*24*time.Hour {
		return fmt.Errorf("时间范围不能超过365天")
	}

	return nil
}

// createFeishuContractService 创建飞书合同服务实例
func (s *InsContractService) createFeishuContractService() *insfinance.FeishuContractService {
	// 创建飞书合同服务实例
	return insfinance.NewFeishuContractService()
}

// syncSingleApprovalCode 同步单个审批代码的合同数据
func (s *InsContractService) syncSingleApprovalCode(ctx context.Context, feishuService *insfinance.FeishuContractService, approvalCode string, req request.ContractMultiSyncRequest, logger *zap.Logger) response.ContractSyncDetailResult {
	startTime := time.Now()

	result := response.ContractSyncDetailResult{
		ApprovalCode: approvalCode,
		Success:      true,
		StartTime:    startTime.Format("2006-01-02 15:04:05"),
		PageResults:  make([]response.ContractSyncPageResult, 0),
	}

	logger.Info("开始同步单个审批代码",
		zap.Time("start_time", *req.StartTime),
		zap.Time("end_time", *req.EndTime),
	)

	// 记录同步日志
	syncLogId, err := s.createSyncLog(approvalCode, "multi_sync")
	if err != nil {
		logger.Error("创建同步日志失败", zap.Error(err))
		result.Success = false
		result.ErrorMsg = fmt.Sprintf("创建同步日志失败: %v", err)
		return result
	}

	// 分页获取合同列表并处理
	pageToken := ""
	pageNum := 0

	for {
		pageNum++
		pageStartTime := time.Now()

		pageCtx, pageLogger := global.Log4Task(ctx, "processSinglePage",
			zap.Int("page_num", pageNum),
			zap.String("page_token", pageToken),
		)

		pageLogger.Info("开始处理分页")

		// 获取当前页的合同列表
		pageResult := s.processSinglePage(pageCtx, feishuService, approvalCode, pageToken, req, pageLogger)
		result.PageResults = append(result.PageResults, pageResult)

		// 更新统计信息
		result.TotalRecords += pageResult.RecordCount
		result.NewRecords += pageResult.SuccessCount - pageResult.FailedCount // 简化计算，实际需要区分新增和更新
		result.FailedRecords += pageResult.FailedCount
		result.ProcessedPages++

		pageLogger.Info("分页处理完成",
			zap.Int("record_count", pageResult.RecordCount),
			zap.Int("success_count", pageResult.SuccessCount),
			zap.Int("failed_count", pageResult.FailedCount),
			zap.Bool("has_more", pageResult.HasMore),
			zap.Duration("page_duration", time.Since(pageStartTime)),
		)

		// 检查是否还有更多页面
		if !pageResult.HasMore {
			break
		}

		pageToken = pageResult.NextPageToken
		result.TotalPages++

		// 页面间延迟，避免API限流
		time.Sleep(200 * time.Millisecond)
	}

	endTime := time.Now()
	result.EndTime = endTime.Format("2006-01-02 15:04:05")
	result.Duration = endTime.Sub(startTime).String()
	result.TotalPages = pageNum

	// 更新同步日志
	status := "success"
	if result.FailedRecords > 0 {
		if result.TotalRecords > result.FailedRecords {
			status = "partial_success"
			result.ErrorMsg = fmt.Sprintf("部分记录同步失败: 成功 %d，失败 %d", result.TotalRecords-result.FailedRecords, result.FailedRecords)
		} else {
			status = "failed"
			result.Success = false
			result.ErrorMsg = "所有记录同步失败"
		}
	}

	s.updateSyncLog(syncLogId, status, result.TotalRecords, result.ErrorMsg)

	logger.Info("单个审批代码同步完成",
		zap.Int("total_pages", result.TotalPages),
		zap.Int("processed_pages", result.ProcessedPages),
		zap.Int("total_records", result.TotalRecords),
		zap.Int("new_records", result.NewRecords),
		zap.Int("failed_records", result.FailedRecords),
		zap.String("duration", result.Duration),
		zap.Bool("success", result.Success),
	)

	return result
}

// createSyncLog 创建同步日志
func (s *InsContractService) createSyncLog(approvalCode, syncType string) (uint, error) {
	syncLog := &insbuy.InsContractSyncLog{
		ApprovalCode: approvalCode,
		SyncType:     syncType,
		Status:       "running",
		StartTime:    time.Now(),
		EndTime:      time.Now(),
	}

	// 使用GORM Gen创建同步日志
	db := query.InsContractSyncLog
	err := db.WithContext(context.Background()).Create(syncLog)
	if err != nil {
		return 0, fmt.Errorf("创建同步日志失败: %w", err)
	}

	return syncLog.ID, nil
}

// processSinglePage 处理单个分页的合同数据
func (s *InsContractService) processSinglePage(ctx context.Context, feishuService *insfinance.FeishuContractService, approvalCode, pageToken string, req request.ContractMultiSyncRequest, logger *zap.Logger) response.ContractSyncPageResult {
	pageStartTime := time.Now()

	result := response.ContractSyncPageResult{
		PageToken:     pageToken,
		PageSize:      req.PageSize,
		ProcessTime:   pageStartTime.Format("2006-01-02 15:04:05"),
		ErrorMessages: make([]string, 0),
	}

	// 步骤1: 调用飞书服务获取合同列表
	listReq := insfinance.ContractListRequest{
		ApprovalCode: approvalCode,
		StartTime:    *req.StartTime,
		EndTime:      *req.EndTime,
		PageSize:     req.PageSize,
		PageToken:    pageToken,
	}

	logger.Info("调用飞书API获取合同列表",
		zap.String("page_token", pageToken),
		zap.Int("page_size", req.PageSize),
	)

	listResp, err := feishuService.GetContractList(ctx, listReq)
	if err != nil {
		errorMsg := fmt.Sprintf("获取合同列表失败: %v", err)
		logger.Error("飞书API调用失败", zap.Error(err))
		result.ErrorMessages = append(result.ErrorMessages, errorMsg)
		return result
	}

	if !listResp.Success {
		errorMsg := fmt.Sprintf("飞书API返回失败: %s", listResp.ErrorMsg)
		logger.Error("飞书API响应失败", zap.String("error", listResp.ErrorMsg))
		result.ErrorMessages = append(result.ErrorMessages, errorMsg)
		return result
	}

	// 更新分页信息
	result.RecordCount = listResp.Total
	result.HasMore = listResp.HasMore
	result.NextPageToken = listResp.PageToken

	// 如果没有数据，直接返回
	if len(listResp.InstanceCodeList) == 0 {
		logger.Info("当前页没有合同数据", zap.String("page_token", pageToken))
		return result
	}

	logger.Info("成功获取合同列表",
		zap.Int("instance_count", len(listResp.InstanceCodeList)),
		zap.Bool("has_more", listResp.HasMore),
	)

	// 步骤2: 批量获取合同详情
	detailReq := insfinance.ContractDetailRequest{
		InstanceCodes: listResp.InstanceCodeList,
	}

	logger.Info("开始批量获取合同详情",
		zap.Int("instance_count", len(listResp.InstanceCodeList)),
	)

	detailResp, err := feishuService.GetContractDetails(ctx, detailReq)
	if err != nil {
		errorMsg := fmt.Sprintf("获取合同详情失败: %v", err)
		logger.Error("批量获取合同详情失败", zap.Error(err))
		result.ErrorMessages = append(result.ErrorMessages, errorMsg)
		result.FailedCount = len(listResp.InstanceCodeList)
		return result
	}

	logger.Info("批量获取合同详情完成",
		zap.Int("success_count", detailResp.Total),
		zap.Int("failed_count", detailResp.FailedCount),
	)

	// 记录详情获取的错误
	for _, detailError := range detailResp.Errors {
		errorMsg := fmt.Sprintf("合同 %s 详情获取失败: %s", detailError.InstanceCode, detailError.ErrorMsg)
		result.ErrorMessages = append(result.ErrorMessages, errorMsg)
	}

	// 步骤3: 批量保存合同数据
	if len(detailResp.Contracts) > 0 {
		logger.Info("开始批量保存合同数据",
			zap.Int("contract_count", len(detailResp.Contracts)),
		)

		newCount, updateCount, failCount, saveErrors := s.saveContractDataBatch(detailResp.Contracts)

		result.SuccessCount = newCount + updateCount
		result.FailedCount += failCount
		result.ErrorMessages = append(result.ErrorMessages, saveErrors...)

		logger.Info("批量保存合同数据完成",
			zap.Int("new_count", newCount),
			zap.Int("update_count", updateCount),
			zap.Int("fail_count", failCount),
		)
	}

	// 步骤4: 错误重试逻辑（如果有失败的记录且允许重试）
	if result.FailedCount > 0 && req.MaxRetries > 0 {
		logger.Warn("存在失败记录，但当前不支持自动重试",
			zap.Int("failed_count", result.FailedCount),
			zap.Int("max_retries", req.MaxRetries),
		)
		// TODO: 可以在这里实现重试逻辑
		// 例如：重新尝试保存失败的合同数据
	}

	return result
}

// saveContractDataBatch 批量保存合同数据
func (s *InsContractService) saveContractDataBatch(contracts []insfinance.ContractDetails) (newCount, updateCount, failCount int, errors []string) {
	ctx, logger := global.Log4Task(context.Background(), "saveContractDataBatch",
		zap.Int("contract_count", len(contracts)),
	)
	_ = ctx // 避免未使用变量警告

	logger.Info("开始批量保存合同数据")

	if len(contracts) == 0 {
		return 0, 0, 0, []string{}
	}

	errors = make([]string, 0)

	// 使用事务确保数据一致性
	err := global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		for i, contract := range contracts {
			logger.Debug("处理合同数据",
				zap.Int("index", i+1),
				zap.String("instance_code", contract.InstanceCode),
				zap.String("approval_name", contract.ApprovalName),
			)

			// 转换飞书合同数据为数据库模型
			dbContract, err := s.convertFeishuContractToModel(contract)
			if err != nil {
				errorMsg := fmt.Sprintf("转换合同数据失败 [%s]: %v", contract.InstanceCode, err)
				errors = append(errors, errorMsg)
				failCount++
				continue
			}

			// 检查合同是否已存在
			var existingContract insbuy.InsContract
			err = tx.Where("instance_code = ?", contract.InstanceCode).First(&existingContract).Error

			if err == gorm.ErrRecordNotFound {
				// 创建新合同
				err = tx.Create(dbContract).Error
				if err != nil {
					errorMsg := fmt.Sprintf("创建合同失败 [%s]: %v", contract.InstanceCode, err)
					errors = append(errors, errorMsg)
					failCount++
					continue
				}

				// 保存关联数据
				err = s.saveContractRelatedDataInTx(tx, dbContract.ID, contract)
				if err != nil {
					errorMsg := fmt.Sprintf("保存合同关联数据失败 [%s]: %v", contract.InstanceCode, err)
					errors = append(errors, errorMsg)
					// 关联数据失败不影响主数据，只记录错误
				}

				newCount++
				logger.Debug("成功创建新合同",
					zap.String("instance_code", contract.InstanceCode),
					zap.Uint("contract_id", dbContract.ID),
				)

			} else if err == nil {
				// 更新现有合同
				updates := s.buildContractUpdateMap(dbContract)
				err = tx.Model(&existingContract).Updates(updates).Error
				if err != nil {
					errorMsg := fmt.Sprintf("更新合同失败 [%s]: %v", contract.InstanceCode, err)
					errors = append(errors, errorMsg)
					failCount++
					continue
				}

				// 更新关联数据
				err = s.updateContractRelatedDataInTx(tx, existingContract.ID, contract)
				if err != nil {
					errorMsg := fmt.Sprintf("更新合同关联数据失败 [%s]: %v", contract.InstanceCode, err)
					errors = append(errors, errorMsg)
					// 关联数据失败不影响主数据，只记录错误
				}

				updateCount++
				logger.Debug("成功更新合同",
					zap.String("instance_code", contract.InstanceCode),
					zap.Uint("contract_id", existingContract.ID),
				)

			} else {
				errorMsg := fmt.Sprintf("查询合同失败 [%s]: %v", contract.InstanceCode, err)
				errors = append(errors, errorMsg)
				failCount++
				continue
			}
		}

		return nil // 事务成功
	})

	if err != nil {
		errorMsg := fmt.Sprintf("批量保存事务失败: %v", err)
		errors = append(errors, errorMsg)
		logger.Error("批量保存合同数据事务失败", zap.Error(err))
		// 事务失败，所有操作都会回滚
		return 0, 0, len(contracts), errors
	}

	logger.Info("批量保存合同数据完成",
		zap.Int("new_count", newCount),
		zap.Int("update_count", updateCount),
		zap.Int("fail_count", failCount),
		zap.Int("error_count", len(errors)),
	)

	return newCount, updateCount, failCount, errors
}

// convertFeishuContractToModel 转换飞书合同数据为数据库模型
func (s *InsContractService) convertFeishuContractToModel(contract insfinance.ContractDetails) (*insbuy.InsContract, error) {
	// 解析时间
	var startTime, endTime *time.Time
	if contract.StartTime != "" {
		if t, err := s.parseFeishuTime(contract.StartTime); err == nil {
			startTime = &t
		}
	}
	if contract.EndTime != "" {
		if t, err := s.parseFeishuTime(contract.EndTime); err == nil {
			endTime = &t
		}
	}

	// 解析表单数据
	var formData datatypes.JSON
	if contract.Form != "" {
		formData = datatypes.JSON(contract.Form)
	}

	// 创建数据库模型
	dbContract := &insbuy.InsContract{
		ApprovalCode: contract.ApprovalCode,
		ApprovalName: contract.ApprovalName,
		InstanceCode: contract.InstanceCode,
		SerialNumber: contract.SerialNumber,
		Uuid:         contract.Uuid,
		Status:       contract.Status,
		Reverted:     contract.Reverted,
		StartTime:    startTime,
		EndTime:      endTime,
		UserId:       contract.UserId,
		OpenId:       contract.OpenId,
		DepartmentId: contract.DepartmentId,
		Form:         formData,
	}

	// 从表单数据中提取业务字段
	s.extractBusinessFields(dbContract, contract.Form)

	return dbContract, nil
}

// buildContractUpdateMap 构建合同更新字段映射
func (s *InsContractService) buildContractUpdateMap(contract *insbuy.InsContract) map[string]interface{} {
	updates := map[string]interface{}{
		"approval_name":   contract.ApprovalName,
		"status":          contract.Status,
		"reverted":        contract.Reverted,
		"start_time":      contract.StartTime,
		"end_time":        contract.EndTime,
		"user_id":         contract.UserId,
		"open_id":         contract.OpenId,
		"department_id":   contract.DepartmentId,
		"form":            contract.Form,
		"contract_type":   contract.ContractType,
		"contract_title":  contract.ContractTitle,
		"contract_amount": contract.ContractAmount,
		"currency":        contract.Currency,
		"updated_at":      time.Now(),
	}

	return updates
}

// saveContractRelatedDataInTx 在事务中保存合同关联数据
func (s *InsContractService) saveContractRelatedDataInTx(tx *gorm.DB, contractId uint, contract insfinance.ContractDetails) error {
	// 批量保存评论
	if len(contract.CommentList) > 0 {
		contractComments := make([]*insbuy.InsContractComment, 0, len(contract.CommentList))

		for _, comment := range contract.CommentList {
			contractComment := &insbuy.InsContractComment{
				ContractId: contractId,
				CommentId:  comment.Id,
				Comment:    comment.Comment,
				CreateTime: comment.CreateTime,
				OpenId:     comment.OpenId,
				UserId:     comment.UserId,
			}
			contractComments = append(contractComments, contractComment)
		}

		// 批量创建评论
		err := tx.CreateInBatches(contractComments, 100).Error
		if err != nil {
			return fmt.Errorf("批量保存评论失败: %w", err)
		}

		// 批量保存评论相关的文件
		var contractFiles []*insbuy.InsContractFile
		for i, comment := range contract.CommentList {
			for _, file := range comment.Files {
				contractFile := &insbuy.InsContractFile{
					ContractId: contractId,
					CommentId:  contractComments[i].ID, // 使用批量创建后的ID
					FileSize:   file.FileSize,
					Title:      file.Title,
					Type:       file.Type,
					Url:        file.Url,
				}
				contractFiles = append(contractFiles, contractFile)
			}
		}

		if len(contractFiles) > 0 {
			err = tx.CreateInBatches(contractFiles, 100).Error
			if err != nil {
				return fmt.Errorf("批量保存评论文件失败: %w", err)
			}
		}
	}

	// 批量保存任务
	if len(contract.TaskList) > 0 {
		contractTasks := make([]*insbuy.InsContractTask, 0, len(contract.TaskList))

		for _, task := range contract.TaskList {
			contractTask := &insbuy.InsContractTask{
				ContractId: contractId,
				TaskId:     task.Id,
				NodeId:     task.NodeId,
				NodeName:   task.NodeName,
				Type:       task.Type,
				Status:     task.Status,
				StartTime:  task.StartTime,
				EndTime:    task.EndTime,
				OpenId:     task.OpenId,
				UserId:     task.UserId,
			}
			contractTasks = append(contractTasks, contractTask)
		}

		// 批量创建任务记录，提高性能
		err := tx.CreateInBatches(contractTasks, 100).Error
		if err != nil {
			return fmt.Errorf("批量保存任务失败: %w", err)
		}
	}

	// 批量保存时间线
	if len(contract.Timeline) > 0 {
		contractTimelines := make([]*insbuy.InsContractTimeline, 0, len(contract.Timeline))

		for _, timeline := range contract.Timeline {
			ccUserListJson, _ := json.Marshal(timeline.CcUserList)
			openIdListJson, _ := json.Marshal(timeline.OpenIdList)
			userIdListJson, _ := json.Marshal(timeline.UserIdList)

			contractTimeline := &insbuy.InsContractTimeline{
				ContractId: contractId,
				CreateTime: timeline.CreateTime,
				Ext:        timeline.Ext,
				NodeKey:    timeline.NodeKey,
				OpenId:     timeline.OpenId,
				Type:       timeline.Type,
				UserId:     timeline.UserId,
				TaskId:     timeline.TaskId,
				CcUserList: datatypes.JSON(ccUserListJson),
				OpenIdList: datatypes.JSON(openIdListJson),
				UserIdList: datatypes.JSON(userIdListJson),
			}

			contractTimelines = append(contractTimelines, contractTimeline)
		}

		// 批量创建时间线记录，提高性能
		err := tx.CreateInBatches(contractTimelines, 100).Error
		if err != nil {
			return fmt.Errorf("批量保存时间线失败: %w", err)
		}
	}

	return nil
}

// updateContractRelatedDataInTx 在事务中更新合同关联数据
func (s *InsContractService) updateContractRelatedDataInTx(tx *gorm.DB, contractId uint, contract insfinance.ContractDetails) error {
	// 删除现有关联数据
	err := tx.Where("contract_id = ?", contractId).Delete(&insbuy.InsContractComment{}).Error
	if err != nil {
		return fmt.Errorf("删除现有评论失败: %w", err)
	}

	err = tx.Where("contract_id = ?", contractId).Delete(&insbuy.InsContractTask{}).Error
	if err != nil {
		return fmt.Errorf("删除现有任务失败: %w", err)
	}

	err = tx.Where("contract_id = ?", contractId).Delete(&insbuy.InsContractTimeline{}).Error
	if err != nil {
		return fmt.Errorf("删除现有时间线失败: %w", err)
	}

	err = tx.Where("contract_id = ?", contractId).Delete(&insbuy.InsContractFile{}).Error
	if err != nil {
		return fmt.Errorf("删除现有文件失败: %w", err)
	}

	// 重新保存关联数据
	return s.saveContractRelatedDataInTx(tx, contractId, contract)
}

// 辅助方法

// getContractCommentCount 获取合同评论数量
func (s *InsContractService) getContractCommentCount(contractId uint) int {
	db := query.InsContractComment
	count, err := db.WithContext(context.Background()).Where(db.ContractId.Eq(contractId)).Count()
	if err != nil {
		global.GVA_LOG.Error("查询合同评论数量失败", zap.Error(err))
		return 0
	}
	return int(count)
}

// getContractTaskCount 获取合同任务数量
func (s *InsContractService) getContractTaskCount(contractId uint) int {
	db := query.InsContractTask
	count, err := db.WithContext(context.Background()).Where(db.ContractId.Eq(contractId)).Count()
	if err != nil {
		global.GVA_LOG.Error("查询合同任务数量失败", zap.Error(err))
		return 0
	}
	return int(count)
}

// getContractTimelineCount 获取合同时间线数量
func (s *InsContractService) getContractTimelineCount(contractId uint) int {
	db := query.InsContractTimeline
	count, err := db.WithContext(context.Background()).Where(db.ContractId.Eq(contractId)).Count()
	if err != nil {
		global.GVA_LOG.Error("查询合同时间线数量失败", zap.Error(err))
		return 0
	}
	return int(count)
}

// getContractFileCount 获取合同文件数量
func (s *InsContractService) getContractFileCount(contractId uint) int {
	db := query.InsContractFile
	count, err := db.WithContext(context.Background()).Where(db.ContractId.Eq(contractId)).Count()
	if err != nil {
		global.GVA_LOG.Error("查询合同文件数量失败", zap.Error(err))
		return 0
	}
	return int(count)
}

// getContractComments 获取合同评论列表
func (s *InsContractService) getContractComments(contractId uint) ([]response.ContractCommentItem, error) {
	db := query.InsContractComment
	comments, err := db.WithContext(context.Background()).
		Where(db.ContractId.Eq(contractId)).
		Order(db.CreatedAt.Desc()).
		Find()
	if err != nil {
		return nil, err
	}

	var items []response.ContractCommentItem
	for _, comment := range comments {
		item := response.ContractCommentItem{
			InsContractComment: *comment,
		}

		// 获取评论相关的文件
		files, _ := s.getCommentFiles(comment.ID)
		item.Files = files

		items = append(items, item)
	}

	return items, nil
}

// getContractTasks 获取合同任务列表
func (s *InsContractService) getContractTasks(contractId uint) ([]response.ContractTaskItem, error) {
	db := query.InsContractTask
	tasks, err := db.WithContext(context.Background()).
		Where(db.ContractId.Eq(contractId)).
		Order(db.CreatedAt.Desc()).
		Find()
	if err != nil {
		return nil, err
	}

	var items []response.ContractTaskItem
	for _, task := range tasks {
		item := response.ContractTaskItem{
			InsContractTask: *task,
		}
		items = append(items, item)
	}

	return items, nil
}

// getContractTimeline 获取合同时间线列表
func (s *InsContractService) getContractTimeline(contractId uint) ([]response.ContractTimelineItem, error) {
	db := query.InsContractTimeline
	timeline, err := db.WithContext(context.Background()).
		Where(db.ContractId.Eq(contractId)).
		Order(db.CreatedAt.Desc()).
		Find()
	if err != nil {
		return nil, err
	}

	var items []response.ContractTimelineItem
	for _, t := range timeline {
		item := response.ContractTimelineItem{
			InsContractTimeline: *t,
		}
		items = append(items, item)
	}

	return items, nil
}

// getContractFiles 获取合同文件列表
func (s *InsContractService) getContractFiles(contractId uint) ([]response.ContractFileItem, error) {
	db := query.InsContractFile
	files, err := db.WithContext(context.Background()).
		Where(db.ContractId.Eq(contractId)).
		Order(db.CreatedAt.Desc()).
		Find()
	if err != nil {
		return nil, err
	}

	var items []response.ContractFileItem
	for _, file := range files {
		item := response.ContractFileItem{
			InsContractFile: *file,
		}
		items = append(items, item)
	}

	return items, nil
}

// getCommentFiles 获取评论相关的文件
func (s *InsContractService) getCommentFiles(commentId uint) ([]response.ContractFileItem, error) {
	db := query.InsContractFile
	files, err := db.WithContext(context.Background()).
		Where(db.CommentId.Eq(commentId)).
		Order(db.CreatedAt.Desc()).
		Find()
	if err != nil {
		return nil, err
	}

	var items []response.ContractFileItem
	for _, file := range files {
		item := response.ContractFileItem{
			InsContractFile: *file,
		}
		items = append(items, item)
	}

	return items, nil
}

// ===== 高级GORM Gen查询示例 =====

// GetContractStatistics 获取合同统计信息 - 使用GORM Gen的聚合查询
func (s *InsContractService) GetContractStatistics(req request.ContractStatisticsRequest) (resp response.ContractStatisticsResponse, err error) {
	global.GVA_LOG.Info("开始查询合同统计信息", zap.Any("request", req))

	db := query.InsContract
	q := db.WithContext(context.Background())

	// 构建查询条件
	conditions := make([]gen.Condition, 0)

	if req.ApprovalCode != "" {
		conditions = append(conditions, db.ApprovalCode.Eq(req.ApprovalCode))
	}
	if req.DepartmentId != "" {
		conditions = append(conditions, db.DepartmentId.Eq(req.DepartmentId))
	}
	if req.ContractType != "" {
		conditions = append(conditions, db.ContractType.Eq(req.ContractType))
	}
	if req.StartTime != nil {
		conditions = append(conditions, db.StartTime.Gte(*req.StartTime))
	}
	if req.EndTime != nil {
		conditions = append(conditions, db.EndTime.Lte(*req.EndTime))
	}

	// 应用查询条件
	for _, condition := range conditions {
		q = q.Where(condition)
	}

	// 总数统计
	totalCount, err := q.Count()
	if err != nil {
		global.GVA_LOG.Error("查询合同总数失败", zap.Error(err))
		return
	}

	resp.TotalCount = int(totalCount)

	// 状态分组统计 - 使用GORM Gen的分组查询
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	err = q.Select(db.Status, db.Status.Count().As("count")).
		Group(db.Status).
		Scan(&statusStats)
	if err != nil {
		global.GVA_LOG.Error("查询状态统计失败", zap.Error(err))
		return
	}

	// 转换状态统计结果
	for _, stat := range statusStats {
		ratio := fmt.Sprintf("%.2f%%", float64(stat.Count)/float64(totalCount)*100)
		resp.StatusStats = append(resp.StatusStats, response.ContractStatusStat{
			Status: stat.Status,
			Count:  int(stat.Count),
			Ratio:  ratio,
		})
	}

	// 部门分组统计 - 包含金额聚合
	var deptStats []struct {
		DepartmentId string  `json:"department_id"`
		Count        int64   `json:"count"`
		TotalAmount  float64 `json:"total_amount"`
	}

	err = q.Select(
		db.DepartmentId,
		db.DepartmentId.Count().As("count"),
		db.ContractAmount.Sum().As("total_amount"),
	).Group(db.DepartmentId).
		Scan(&deptStats)
	if err != nil {
		global.GVA_LOG.Error("查询部门统计失败", zap.Error(err))
		return
	}

	// 转换部门统计结果
	for _, stat := range deptStats {
		ratio := fmt.Sprintf("%.2f%%", float64(stat.Count)/float64(totalCount)*100)
		resp.DepartmentStats = append(resp.DepartmentStats, response.ContractDepartmentStat{
			DepartmentId:   stat.DepartmentId,
			DepartmentName: stat.DepartmentId, // 实际应该查询部门名称
			Count:          int(stat.Count),
			Amount:         stat.TotalAmount,
			Ratio:          ratio,
		})
	}

	// 金额统计 - 使用聚合函数
	var amountStats struct {
		TotalAmount   float64 `json:"total_amount"`
		AverageAmount float64 `json:"average_amount"`
		MaxAmount     float64 `json:"max_amount"`
		MinAmount     float64 `json:"min_amount"`
	}

	err = q.Select(
		db.ContractAmount.Sum().As("total_amount"),
		db.ContractAmount.Avg().As("average_amount"),
		db.ContractAmount.Max().As("max_amount"),
		db.ContractAmount.Min().As("min_amount"),
	).Scan(&amountStats)
	if err != nil {
		global.GVA_LOG.Error("查询金额统计失败", zap.Error(err))
		return
	}

	resp.AmountStats = response.ContractAmountStat{
		TotalAmount:   amountStats.TotalAmount,
		AverageAmount: amountStats.AverageAmount,
		MaxAmount:     amountStats.MaxAmount,
		MinAmount:     amountStats.MinAmount,
	}

	global.GVA_LOG.Info("查询合同统计信息成功",
		zap.Int("total_count", resp.TotalCount),
		zap.Int("status_stats_count", len(resp.StatusStats)),
		zap.Int("dept_stats_count", len(resp.DepartmentStats)),
	)

	return
}

// GetContractListWithJoin 获取合同列表（带关联查询）- 展示GORM Gen的JOIN用法
func (s *InsContractService) GetContractListWithJoin(req request.ContractListRequest) (resp response.ContractListResponse, err error) {
	global.GVA_LOG.Info("开始查询合同列表（带关联）", zap.Any("request", req))

	// 使用多表关联查询
	db := query.InsContract
	commentDB := query.InsContractComment
	taskDB := query.InsContractTask

	q := db.WithContext(context.Background()).
		LeftJoin(commentDB, db.ID.EqCol(commentDB.ContractId)).
		LeftJoin(taskDB, db.ID.EqCol(taskDB.ContractId))

	// 构建查询条件
	conditions := make([]gen.Condition, 0)

	if req.ApprovalCode != "" {
		conditions = append(conditions, db.ApprovalCode.Eq(req.ApprovalCode))
	}
	if req.Status != "" {
		conditions = append(conditions, db.Status.Eq(req.Status))
	}

	// 应用查询条件
	for _, condition := range conditions {
		q = q.Where(condition)
	}

	// 分组和聚合 - 避免重复记录
	type ContractWithStats struct {
		insbuy.InsContract
		CommentCount int64 `json:"comment_count"`
		TaskCount    int64 `json:"task_count"`
	}

	var results []ContractWithStats

	err = q.Select(
		db.ALL,
		commentDB.ID.Count().As("comment_count"),
		taskDB.ID.Count().As("task_count"),
	).Group(db.ID).
		Scan(&results)
	if err != nil {
		global.GVA_LOG.Error("查询合同列表（带关联）失败", zap.Error(err))
		return
	}

	// 转换响应数据
	var items []response.ContractItem
	for _, result := range results {
		item := response.ContractItem{
			ID:             result.ID,
			ApprovalCode:   result.ApprovalCode,
			ApprovalName:   result.ApprovalName,
			InstanceCode:   result.InstanceCode,
			SerialNumber:   result.SerialNumber,
			Uuid:           result.Uuid,
			Status:         result.Status,
			Reverted:       result.Reverted,
			StartTime:      result.StartTime,
			EndTime:        result.EndTime,
			UserId:         result.UserId,
			OpenId:         result.OpenId,
			DepartmentId:   result.DepartmentId,
			ContractType:   result.ContractType,
			ContractTitle:  result.ContractTitle,
			ContractAmount: result.ContractAmount,
			Currency:       result.Currency,
			CreatedAt:      result.CreatedAt,
			UpdatedAt:      result.UpdatedAt,
			CommentCount:   int(result.CommentCount),
			TaskCount:      int(result.TaskCount),
		}
		items = append(items, item)
	}

	resp = response.ContractListResponse{
		List:     items,
		Total:    int64(len(items)),
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	global.GVA_LOG.Info("查询合同列表（带关联）成功",
		zap.Int("count", len(items)),
	)

	return
}

// BatchUpdateContractStatus 批量更新合同状态 - 展示GORM Gen的批量更新
func (s *InsContractService) BatchUpdateContractStatus(instanceCodes []string, newStatus string) error {
	global.GVA_LOG.Info("开始批量更新合同状态",
		zap.Strings("instance_codes", instanceCodes),
		zap.String("new_status", newStatus),
	)

	if len(instanceCodes) == 0 {
		return fmt.Errorf("实例代码列表不能为空")
	}

	db := query.InsContract

	// 使用GORM Gen的批量更新
	result, err := db.WithContext(context.Background()).
		Where(db.InstanceCode.In(instanceCodes...)).
		Updates(map[string]interface{}{
			"status":     newStatus,
			"updated_at": time.Now(),
		})

	if err != nil {
		global.GVA_LOG.Error("批量更新合同状态失败", zap.Error(err))
		return fmt.Errorf("批量更新失败: %w", err)
	}

	global.GVA_LOG.Info("批量更新合同状态成功",
		zap.Int64("affected_rows", result.RowsAffected),
	)

	return nil
}

// GetContractsByComplexConditions 复杂条件查询 - 展示GORM Gen的高级查询
func (s *InsContractService) GetContractsByComplexConditions(
	approvalCodes []string,
	statusList []string,
	amountRange [2]float64,
	timeRange [2]time.Time,
) ([]insbuy.InsContract, error) {

	global.GVA_LOG.Info("开始复杂条件查询合同")

	db := query.InsContract
	q := db.WithContext(context.Background())

	// 复杂的WHERE条件组合
	conditions := make([]gen.Condition, 0)

	// IN查询
	if len(approvalCodes) > 0 {
		conditions = append(conditions, db.ApprovalCode.In(approvalCodes...))
	}

	// 多值IN查询
	if len(statusList) > 0 {
		conditions = append(conditions, db.Status.In(statusList...))
	}

	// 范围查询
	if amountRange[0] > 0 {
		conditions = append(conditions, db.ContractAmount.Gte(amountRange[0]))
	}
	if amountRange[1] > 0 {
		conditions = append(conditions, db.ContractAmount.Lte(amountRange[1]))
	}

	// 时间范围查询
	if !timeRange[0].IsZero() {
		conditions = append(conditions, db.StartTime.Gte(timeRange[0]))
	}
	if !timeRange[1].IsZero() {
		conditions = append(conditions, db.EndTime.Lte(timeRange[1]))
	}

	// 复合条件示例：简化处理，避免复杂的条件组合
	// 实际项目中可以根据需要添加更复杂的条件逻辑
	if len(statusList) > 0 && len(amountRange) == 2 && amountRange[0] > 0 {
		// 状态在列表中且金额大于最小值
		statusCondition := db.Status.In(statusList...)
		amountCondition := db.ContractAmount.Gte(amountRange[0])
		conditions = append(conditions, statusCondition, amountCondition)
	}

	// 应用所有条件
	for _, condition := range conditions {
		q = q.Where(condition)
	}

	// 执行查询
	contracts, err := q.Order(db.CreatedAt.Desc()).Find()
	if err != nil {
		global.GVA_LOG.Error("复杂条件查询失败", zap.Error(err))
		return nil, err
	}

	// 转换指针切片为值切片
	result := make([]insbuy.InsContract, len(contracts))
	for i, contract := range contracts {
		result[i] = *contract
	}

	global.GVA_LOG.Info("复杂条件查询成功", zap.Int("count", len(result)))

	return result, nil
}

// parseContractForm 解析合同表单数据
func (s *InsContractService) parseContractForm(formData datatypes.JSON) ([]response.ContractFormField, error) {
	if len(formData) == 0 {
		return []response.ContractFormField{}, nil
	}

	var fields []response.ContractFormField
	var formMap map[string]interface{}

	err := json.Unmarshal(formData, &formMap)
	if err != nil {
		return nil, fmt.Errorf("解析表单JSON失败: %w", err)
	}

	// 这里可以根据实际的表单结构进行解析
	// 示例解析逻辑
	for key, value := range formMap {
		field := response.ContractFormField{
			ID:    key,
			Name:  key,
			Type:  "text", // 默认类型
			Value: value,
			Label: key,
		}

		// 根据值的类型推断字段类型
		switch value.(type) {
		case string:
			field.Type = "text"
		case float64:
			field.Type = "number"
		case bool:
			field.Type = "boolean"
		case map[string]interface{}:
			field.Type = "object"
		case []interface{}:
			field.Type = "array"
		default:
			field.Type = "unknown"
		}

		fields = append(fields, field)
	}

	return fields, nil
}

// updateSyncLog 更新同步日志
func (s *InsContractService) updateSyncLog(logId uint, status string, recordCount int, errorMsg string) {
	endTime := time.Now()

	// 使用GORM Gen更新同步日志
	db := query.InsContractSyncLog
	_, err := db.WithContext(context.Background()).
		Where(db.ID.Eq(logId)).
		Updates(map[string]interface{}{
			"status":       status,
			"end_time":     endTime,
			"record_count": recordCount,
			"error_msg":    errorMsg,
		})

	if err != nil {
		global.GVA_LOG.Error("更新同步日志失败", zap.Error(err))
	}
}

// saveContractData 保存合同数据
func (s *InsContractService) saveContractData(detail *utils.ContractDetail) error {

	// 解析时间
	var startTime, endTime *time.Time
	if detail.StartTime != "" {
		if t, err := s.parseFeishuTime(detail.StartTime); err == nil {
			startTime = &t
		}
	}
	if detail.EndTime != "" {
		if t, err := s.parseFeishuTime(detail.EndTime); err == nil {
			endTime = &t
		}
	}

	// 解析表单数据
	var formData datatypes.JSON
	if detail.Form != "" {
		formData = datatypes.JSON(detail.Form)
	}

	// 检查合同是否已存在 - 使用GORM Gen
	db := query.InsContract
	existingContract, err := db.WithContext(context.Background()).
		Where(db.InstanceCode.Eq(detail.InstanceCode)).
		First()

	if err == gorm.ErrRecordNotFound {
		// 创建新合同
		contract := &insbuy.InsContract{
			ApprovalCode: detail.ApprovalCode,
			ApprovalName: detail.ApprovalName,
			InstanceCode: detail.InstanceCode,
			SerialNumber: detail.SerialNumber,
			Uuid:         detail.Uuid,
			Status:       detail.Status,
			Reverted:     detail.Reverted,
			StartTime:    startTime,
			EndTime:      endTime,
			UserId:       detail.UserId,
			OpenId:       detail.OpenId,
			DepartmentId: detail.DepartmentId,
			Form:         formData,
		}

		// 从表单数据中提取业务字段
		s.extractBusinessFields(contract, detail.Form)

		// 使用GORM Gen创建记录
		err = db.WithContext(context.Background()).Create(contract)
		if err != nil {
			return fmt.Errorf("创建合同失败: %w", err)
		}

		// 保存关联数据
		err = s.saveContractRelatedData(contract.ID, detail)
		if err != nil {
			global.GVA_LOG.Error("保存合同关联数据失败", zap.Error(err))
			// 不返回错误，允许主数据保存成功
		}

	} else if err == nil {
		// 更新现有合同 - 使用GORM Gen
		updates := map[string]interface{}{
			"approval_name": detail.ApprovalName,
			"status":        detail.Status,
			"reverted":      detail.Reverted,
			"start_time":    startTime,
			"end_time":      endTime,
			"user_id":       detail.UserId,
			"open_id":       detail.OpenId,
			"department_id": detail.DepartmentId,
			"form":          formData,
		}

		// 从表单数据中提取业务字段
		businessFields := make(map[string]interface{})
		s.extractBusinessFieldsToMap(businessFields, detail.Form)
		for k, v := range businessFields {
			updates[k] = v
		}

		_, err = db.WithContext(context.Background()).
			Where(db.ID.Eq(existingContract.ID)).
			Updates(updates)
		if err != nil {
			return fmt.Errorf("更新合同失败: %w", err)
		}

		// 更新关联数据
		err = s.updateContractRelatedData(existingContract.ID, detail)
		if err != nil {
			global.GVA_LOG.Error("更新合同关联数据失败", zap.Error(err))
		}

	} else {
		return fmt.Errorf("查询合同失败: %w", err)
	}

	return nil
}

// parseFeishuTime 解析飞书时间格式
func (s *InsContractService) parseFeishuTime(timeStr string) (time.Time, error) {
	// 飞书时间格式通常是毫秒时间戳字符串
	if timeStr == "" {
		return time.Time{}, fmt.Errorf("时间字符串为空")
	}

	// 尝试解析为毫秒时间戳
	if timestamp, err := strconv.ParseInt(timeStr, 10, 64); err == nil {
		// 飞书使用的是毫秒时间戳
		return time.Unix(0, timestamp*int64(time.Millisecond)), nil
	}

	// 尝试解析为标准时间格式
	layouts := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		time.RFC3339,
	}

	for _, layout := range layouts {
		if t, err := time.Parse(layout, timeStr); err == nil {
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// extractBusinessFields 从表单数据中提取业务字段
func (s *InsContractService) extractBusinessFields(contract *insbuy.InsContract, formStr string) {
	if formStr == "" {
		return
	}

	var formMap map[string]interface{}
	if err := json.Unmarshal([]byte(formStr), &formMap); err != nil {
		return
	}

	// 根据实际业务需求提取字段
	// 这里是示例逻辑，需要根据实际表单结构调整
	if title, ok := formMap["contract_title"].(string); ok {
		contract.ContractTitle = title
	}
	if amount, ok := formMap["contract_amount"].(float64); ok {
		contract.ContractAmount = amount
	}
	if contractType, ok := formMap["contract_type"].(string); ok {
		contract.ContractType = contractType
	}
	if currency, ok := formMap["currency"].(string); ok {
		contract.Currency = currency
	}
}

// extractBusinessFieldsToMap 从表单数据中提取业务字段到map
func (s *InsContractService) extractBusinessFieldsToMap(fields map[string]interface{}, formStr string) {
	if formStr == "" {
		return
	}

	var formMap map[string]interface{}
	if err := json.Unmarshal([]byte(formStr), &formMap); err != nil {
		return
	}

	// 根据实际业务需求提取字段
	if title, ok := formMap["contract_title"].(string); ok {
		fields["contract_title"] = title
	}
	if amount, ok := formMap["contract_amount"].(float64); ok {
		fields["contract_amount"] = amount
	}
	if contractType, ok := formMap["contract_type"].(string); ok {
		fields["contract_type"] = contractType
	}
	if currency, ok := formMap["currency"].(string); ok {
		fields["currency"] = currency
	}
}

// saveContractRelatedData 保存合同关联数据
func (s *InsContractService) saveContractRelatedData(contractId uint, detail *utils.ContractDetail) error {
	ctx := context.Background()

	// 批量保存评论
	if len(detail.CommentList) > 0 {
		contractComments := make([]*insbuy.InsContractComment, 0, len(detail.CommentList))

		for _, comment := range detail.CommentList {
			contractComment := &insbuy.InsContractComment{
				ContractId: contractId,
				CommentId:  comment.Id,
				Comment:    comment.Comment,
				CreateTime: comment.CreateTime,
				OpenId:     comment.OpenId,
				UserId:     comment.UserId,
			}
			contractComments = append(contractComments, contractComment)
		}

		// 批量创建评论
		dbComment := query.InsContractComment
		err := dbComment.WithContext(ctx).CreateInBatches(contractComments, 100)
		if err != nil {
			global.GVA_LOG.Error("批量保存合同评论失败", zap.Error(err))
		} else {
			// 批量保存评论相关的文件
			var contractFiles []*insbuy.InsContractFile
			for i, comment := range detail.CommentList {
				for _, file := range comment.Files {
					contractFile := &insbuy.InsContractFile{
						ContractId: contractId,
						CommentId:  contractComments[i].ID, // 使用批量创建后的ID
						FileSize:   file.FileSize,
						Title:      file.Title,
						Type:       file.Type,
						Url:        file.Url,
					}
					contractFiles = append(contractFiles, contractFile)
				}
			}

			if len(contractFiles) > 0 {
				dbFile := query.InsContractFile
				err = dbFile.WithContext(ctx).CreateInBatches(contractFiles, 100)
				if err != nil {
					global.GVA_LOG.Error("批量保存合同文件失败", zap.Error(err))
				}
			}
		}
	}

	// 批量保存任务
	if len(detail.TaskList) > 0 {
		contractTasks := make([]*insbuy.InsContractTask, 0, len(detail.TaskList))

		for _, task := range detail.TaskList {
			contractTask := &insbuy.InsContractTask{
				ContractId: contractId,
				TaskId:     task.Id,
				NodeId:     task.NodeId,
				NodeName:   task.NodeName,
				Type:       task.Type,
				Status:     task.Status,
				StartTime:  task.StartTime,
				EndTime:    task.EndTime,
				OpenId:     task.OpenId,
				UserId:     task.UserId,
			}
			contractTasks = append(contractTasks, contractTask)
		}

		// 批量创建任务记录，提高性能
		dbTask := query.InsContractTask
		err := dbTask.WithContext(ctx).CreateInBatches(contractTasks, 100)
		if err != nil {
			global.GVA_LOG.Error("批量保存合同任务失败", zap.Error(err))
		}
	}

	// 批量保存时间线
	if len(detail.Timeline) > 0 {
		contractTimelines := make([]*insbuy.InsContractTimeline, 0, len(detail.Timeline))

		for _, timeline := range detail.Timeline {
			ccUserListJson, _ := json.Marshal(timeline.CcUserList)
			openIdListJson, _ := json.Marshal(timeline.OpenIdList)
			userIdListJson, _ := json.Marshal(timeline.UserIdList)

			contractTimeline := &insbuy.InsContractTimeline{
				ContractId: contractId,
				CreateTime: timeline.CreateTime,
				Ext:        timeline.Ext,
				NodeKey:    timeline.NodeKey,
				OpenId:     timeline.OpenId,
				Type:       timeline.Type,
				UserId:     timeline.UserId,
				TaskId:     timeline.TaskId,
				CcUserList: datatypes.JSON(ccUserListJson),
				OpenIdList: datatypes.JSON(openIdListJson),
				UserIdList: datatypes.JSON(userIdListJson),
			}

			contractTimelines = append(contractTimelines, contractTimeline)
		}

		// 使用GORM Gen批量创建时间线记录
		dbTimeline := query.InsContractTimeline
		err := dbTimeline.WithContext(ctx).CreateInBatches(contractTimelines, 100)
		if err != nil {
			global.GVA_LOG.Error("批量保存合同时间线失败", zap.Error(err))
		}
	}

	return nil
}

// updateContractRelatedData 更新合同关联数据
func (s *InsContractService) updateContractRelatedData(contractId uint, detail *utils.ContractDetail) error {
	ctx := context.Background()

	// 删除现有关联数据
	dbComment := query.InsContractComment
	dbComment.WithContext(ctx).Where(dbComment.ContractId.Eq(contractId)).Delete(&insbuy.InsContractComment{})

	dbTask := query.InsContractTask
	dbTask.WithContext(ctx).Where(dbTask.ContractId.Eq(contractId)).Delete(&insbuy.InsContractTask{})

	dbTimeline := query.InsContractTimeline
	dbTimeline.WithContext(ctx).Where(dbTimeline.ContractId.Eq(contractId)).Delete(&insbuy.InsContractTimeline{})

	dbFile := query.InsContractFile
	dbFile.WithContext(ctx).Where(dbFile.ContractId.Eq(contractId)).Delete(&insbuy.InsContractFile{})

	// 重新保存关联数据
	return s.saveContractRelatedData(contractId, detail)
}
