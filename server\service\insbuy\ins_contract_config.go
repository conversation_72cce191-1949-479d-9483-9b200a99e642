package insbuy

import (
	"context"
	"fmt"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// FeishuApprovalConfigService 飞书审批代码配置服务
type FeishuApprovalConfigService struct{}

// GetAllApprovalCodes 获取所有启用的审批代码
func (s *FeishuApprovalConfigService) GetAllApprovalCodes() []string {
	ctx, logger := global.Log4Task(context.Background(), "GetAllApprovalCodes")
	_ = ctx

	var allCodes []string
	feishuConfig := global.GVA_CONFIG.FeishuApp

	// 遍历所有配置的审批代码
	for _, approvalConfig := range feishuConfig.ApprovalCodes {
		if approvalConfig.Enabled && approvalConfig.Code != "" {
			allCodes = append(allCodes, approvalConfig.Code)
		}
	}

	logger.Info("获取所有审批代码完成",
		zap.Int("total_codes", len(allCodes)),
		zap.Strings("codes", allCodes),
	)

	return allCodes
}

// GetApprovalCodesByTag 根据标签获取审批代码
func (s *FeishuApprovalConfigService) GetApprovalCodesByTag(tag string) []string {
	ctx, logger := global.Log4Task(context.Background(), "GetApprovalCodesByTag",
		zap.String("tag", tag),
	)
	_ = ctx

	feishuConfig := global.GVA_CONFIG.FeishuApp
	var codes []string

	// 遍历所有配置的审批代码，查找包含指定标签的
	for _, approvalConfig := range feishuConfig.ApprovalCodes {
		if !approvalConfig.Enabled || approvalConfig.Code == "" {
			continue
		}

		// 检查是否包含指定标签
		for _, configTag := range approvalConfig.Tags {
			if strings.EqualFold(configTag, tag) {
				codes = append(codes, approvalConfig.Code)
				break
			}
		}
	}

	logger.Info("根据标签获取审批代码完成",
		zap.Int("code_count", len(codes)),
		zap.Strings("codes", codes),
	)

	return codes
}

// GetApprovalCodesByTags 根据多个标签获取审批代码（需要包含所有标签）
func (s *FeishuApprovalConfigService) GetApprovalCodesByTags(tags []string) []string {
	ctx, logger := global.Log4Task(context.Background(), "GetApprovalCodesByTags",
		zap.Strings("tags", tags),
	)
	_ = ctx

	if len(tags) == 0 {
		return s.GetAllApprovalCodes()
	}

	feishuConfig := global.GVA_CONFIG.FeishuApp
	var codes []string

	// 遍历所有配置的审批代码
	for _, approvalConfig := range feishuConfig.ApprovalCodes {
		if !approvalConfig.Enabled || approvalConfig.Code == "" {
			continue
		}

		// 检查是否包含所有指定标签
		hasAllTags := true
		for _, requiredTag := range tags {
			hasTag := false
			for _, configTag := range approvalConfig.Tags {
				if strings.EqualFold(configTag, requiredTag) {
					hasTag = true
					break
				}
			}
			if !hasTag {
				hasAllTags = false
				break
			}
		}

		if hasAllTags {
			codes = append(codes, approvalConfig.Code)
		}
	}

	logger.Info("根据多个标签获取审批代码完成",
		zap.Int("code_count", len(codes)),
		zap.Strings("codes", codes),
	)

	return codes
}

// GetApprovalConfigList 获取审批代码配置列表
func (s *FeishuApprovalConfigService) GetApprovalConfigList() []config.ApprovalCodeConfig {
	ctx, logger := global.Log4Task(context.Background(), "GetApprovalConfigList")
	_ = ctx

	feishuConfig := global.GVA_CONFIG.FeishuApp

	logger.Info("获取审批代码配置列表完成",
		zap.Int("config_count", len(feishuConfig.ApprovalCodes)),
	)

	return feishuConfig.ApprovalCodes
}

// GetEnabledApprovalConfigs 获取启用的审批代码配置
func (s *FeishuApprovalConfigService) GetEnabledApprovalConfigs() []config.ApprovalCodeConfig {
	ctx, logger := global.Log4Task(context.Background(), "GetEnabledApprovalConfigs")
	_ = ctx

	feishuConfig := global.GVA_CONFIG.FeishuApp
	var enabledConfigs []config.ApprovalCodeConfig

	for _, approvalConfig := range feishuConfig.ApprovalCodes {
		if approvalConfig.Enabled {
			enabledConfigs = append(enabledConfigs, approvalConfig)
		}
	}

	logger.Info("获取启用的审批代码配置完成",
		zap.Int("enabled_count", len(enabledConfigs)),
		zap.Int("total_count", len(feishuConfig.ApprovalCodes)),
	)

	return enabledConfigs
}

// ValidateApprovalCode 验证审批代码是否存在于配置中
func (s *FeishuApprovalConfigService) ValidateApprovalCode(approvalCode string) bool {
	allCodes := s.GetAllApprovalCodes()
	for _, code := range allCodes {
		if code == approvalCode {
			return true
		}
	}
	return false
}

// ValidateApprovalCodes 批量验证审批代码
func (s *FeishuApprovalConfigService) ValidateApprovalCodes(approvalCodes []string) (valid []string, invalid []string) {
	ctx, logger := global.Log4Task(context.Background(), "ValidateApprovalCodes",
		zap.Int("code_count", len(approvalCodes)),
	)
	_ = ctx

	allCodes := s.GetAllApprovalCodes()
	codeMap := make(map[string]bool)
	for _, code := range allCodes {
		codeMap[code] = true
	}

	for _, code := range approvalCodes {
		if codeMap[code] {
			valid = append(valid, code)
		} else {
			invalid = append(invalid, code)
		}
	}

	logger.Info("审批代码验证完成",
		zap.Int("valid_count", len(valid)),
		zap.Int("invalid_count", len(invalid)),
		zap.Strings("invalid_codes", invalid),
	)

	return valid, invalid
}

// GetApprovalCodeInfo 获取审批代码详细信息
func (s *FeishuApprovalConfigService) GetApprovalCodeInfo(approvalCode string) (config.ApprovalCodeConfig, error) {
	ctx, logger := global.Log4Task(context.Background(), "GetApprovalCodeInfo",
		zap.String("approval_code", approvalCode),
	)
	_ = ctx

	feishuConfig := global.GVA_CONFIG.FeishuApp

	// 查找指定的审批代码配置
	for _, approvalConfig := range feishuConfig.ApprovalCodes {
		if approvalConfig.Code == approvalCode {
			logger.Info("找到审批代码信息")
			return approvalConfig, nil
		}
	}

	logger.Warn("未找到审批代码信息")
	return config.ApprovalCodeConfig{}, fmt.Errorf("审批代码 %s 未在配置中找到", approvalCode)
}
