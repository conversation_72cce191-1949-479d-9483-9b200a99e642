package insbuy

import (
	"context"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/query"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy/request"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ContractSyncSchedulerService 合同同步定时任务服务
type ContractSyncSchedulerService struct {
	configService   *FeishuApprovalConfigService
	contractService *InsContractService
}

// NewContractSyncSchedulerService 创建合同同步定时任务服务实例
func NewContractSyncSchedulerService() *ContractSyncSchedulerService {
	return &ContractSyncSchedulerService{
		configService:   &FeishuApprovalConfigService{},
		contractService: &InsContractService{},
	}
}

// SyncAllContracts 同步所有启用的合同审批代码
func (s *ContractSyncSchedulerService) SyncAllContracts() error {
	ctx, logger := global.Log4Task(context.Background(), "SyncAllContracts")
	_ = ctx

	logger.Info("开始执行全量合同同步定时任务")

	// 获取所有启用的审批代码
	approvalCodes := s.configService.GetAllApprovalCodes()
	if len(approvalCodes) == 0 {
		logger.Warn("未找到启用的审批代码，跳过同步")
		return fmt.Errorf("未找到启用的审批代码")
	}

	// 构建同步请求
	req := request.ContractMultiSyncRequest{
		ApprovalCodes: approvalCodes,
		BatchSize:     20,
		PageSize:      100,
		MaxRetries:    3,
		RetryDelay:    5,
		ForceSync:     false, // 增量同步
	}

	// 设置时间范围（最近1天）
	endTime := time.Now()
	startTime := endTime.AddDate(0, 0, -10)
	req.StartTime = &startTime
	req.EndTime = &endTime

	logger.Info("准备同步合同数据",
		zap.Int("approval_codes_count", len(approvalCodes)),
		zap.Time("start_time", startTime),
		zap.Time("end_time", endTime),
	)

	// 执行同步
	resp, err := s.contractService.SyncMultipleContractData(req)
	if err != nil {
		logger.Error("全量合同同步失败", zap.Error(err))
		return fmt.Errorf("全量合同同步失败: %w", err)
	}

	// 记录同步结果
	logger.Info("全量合同同步完成",
		zap.Int("total_codes", resp.TotalCodes),
		zap.Int("success_codes", resp.SuccessCodes),
		zap.Int("failed_codes", resp.FailedCodes),
		zap.Int("total_records", resp.TotalRecords),
		zap.Int("new_records", resp.NewRecords),
		zap.Int("updated_records", resp.UpdatedRecords),
		zap.String("duration", resp.Duration),
		zap.Bool("success", resp.Success),
	)

	if !resp.Success {
		return fmt.Errorf("部分审批代码同步失败: %s", resp.ErrorMsg)
	}

	return nil
}

// DailyFullSyncTask 每日全量同步任务
// 适用于定时任务配置：每日凌晨2点执行
func DailyFullSyncTask() {
	ctx, logger := global.Log4Task(context.Background(), "DailyFullSyncTask")
	_ = ctx

	logger.Info("开始执行每日全量同步任务")

	scheduler := NewContractSyncSchedulerService()
	err := scheduler.SyncAllContracts()

	if err != nil {
		logger.Error("每日全量同步任务失败", zap.Error(err))
		// 可以在这里添加告警通知
		return
	}

	logger.Info("每日全量同步任务完成")
}

// SyncIncrementalContracts 根据已查询过记录查询增量数据
func (s *ContractSyncSchedulerService) SyncIncrementalContracts() error {
	ctx, logger := global.Log4Task(context.Background(), "SyncIncrementalContracts")
	_ = ctx

	logger.Info("开始执行增量合同同步任务")

	// 获取所有启用的审批代码
	approvalCodes := s.configService.GetAllApprovalCodes()
	if len(approvalCodes) == 0 {
		logger.Warn("未找到启用的审批代码，跳过增量同步")
		return fmt.Errorf("未找到启用的审批代码")
	}

	// 为每个审批代码执行增量同步
	var totalNewRecords, totalUpdatedRecords int
	var errors []string

	for _, approvalCode := range approvalCodes {
		logger.Info("开始同步审批代码", zap.String("approval_code", approvalCode))

		// 获取该审批代码的最后同步时间
		lastSyncTime, err := s.getLastSuccessfulSyncTime(approvalCode)
		if err != nil {
			logger.Error("获取最后同步时间失败",
				zap.String("approval_code", approvalCode),
				zap.Error(err),
			)
			errors = append(errors, fmt.Sprintf("审批代码 %s: 获取最后同步时间失败 - %v", approvalCode, err))
			continue
		}

		// 构建增量同步请求
		req := request.ContractMultiSyncRequest{
			ApprovalCodes: []string{approvalCode},
			BatchSize:     20,
			PageSize:      100,
			MaxRetries:    3,
			RetryDelay:    5,
			ForceSync:     false,
		}

		// 设置时间范围：从最后同步时间到现在
		endTime := time.Now()
		req.StartTime = &lastSyncTime
		req.EndTime = &endTime

		logger.Info("准备执行增量同步",
			zap.String("approval_code", approvalCode),
			zap.Time("last_sync_time", lastSyncTime),
			zap.Time("end_time", endTime),
		)

		// 执行同步
		resp, err := s.contractService.SyncMultipleContractData(req)
		if err != nil {
			logger.Error("增量同步失败",
				zap.String("approval_code", approvalCode),
				zap.Error(err),
			)
			errors = append(errors, fmt.Sprintf("审批代码 %s: 同步失败 - %v", approvalCode, err))
			continue
		}

		// 累计统计信息
		totalNewRecords += resp.NewRecords
		totalUpdatedRecords += resp.UpdatedRecords

		logger.Info("审批代码增量同步完成",
			zap.String("approval_code", approvalCode),
			zap.Int("new_records", resp.NewRecords),
			zap.Int("updated_records", resp.UpdatedRecords),
		)
	}

	// 记录总体同步结果
	logger.Info("增量合同同步任务完成",
		zap.Int("total_approval_codes", len(approvalCodes)),
		zap.Int("total_new_records", totalNewRecords),
		zap.Int("total_updated_records", totalUpdatedRecords),
		zap.Int("error_count", len(errors)),
		zap.Strings("errors", errors),
	)

	if len(errors) > 0 {
		return fmt.Errorf("部分审批代码增量同步失败: %v", errors)
	}

	return nil
}

// SyncIncrementalContractsByTag 根据标签进行增量同步
func (s *ContractSyncSchedulerService) SyncIncrementalContractsByTag(tag string) error {
	ctx, logger := global.Log4Task(context.Background(), "SyncIncrementalContractsByTag",
		zap.String("tag", tag),
	)
	_ = ctx

	logger.Info("开始执行标签增量合同同步任务")

	// 根据标签获取审批代码
	approvalCodes := s.configService.GetApprovalCodesByTag(tag)
	if len(approvalCodes) == 0 {
		logger.Warn("未找到指定标签的审批代码", zap.String("tag", tag))
		return fmt.Errorf("未找到标签 %s 的审批代码", tag)
	}

	// 为每个审批代码执行增量同步
	var totalNewRecords, totalUpdatedRecords int
	var errors []string

	for _, approvalCode := range approvalCodes {
		logger.Info("开始同步审批代码",
			zap.String("approval_code", approvalCode),
			zap.String("tag", tag),
		)

		// 获取该审批代码的最后同步时间
		lastSyncTime, err := s.getLastSuccessfulSyncTime(approvalCode)
		if err != nil {
			logger.Error("获取最后同步时间失败",
				zap.String("approval_code", approvalCode),
				zap.Error(err),
			)
			errors = append(errors, fmt.Sprintf("审批代码 %s: 获取最后同步时间失败 - %v", approvalCode, err))
			continue
		}

		// 构建增量同步请求
		req := request.ContractMultiSyncRequest{
			ApprovalCodes: []string{approvalCode},
			BatchSize:     20,
			PageSize:      100,
			MaxRetries:    3,
			RetryDelay:    5,
			ForceSync:     false,
		}

		// 设置时间范围：从最后同步时间到现在
		endTime := time.Now()
		req.StartTime = &lastSyncTime
		req.EndTime = &endTime

		logger.Info("准备执行标签增量同步",
			zap.String("approval_code", approvalCode),
			zap.String("tag", tag),
			zap.Time("last_sync_time", lastSyncTime),
			zap.Time("end_time", endTime),
		)

		// 执行同步
		resp, err := s.contractService.SyncMultipleContractData(req)
		if err != nil {
			logger.Error("标签增量同步失败",
				zap.String("approval_code", approvalCode),
				zap.String("tag", tag),
				zap.Error(err),
			)
			errors = append(errors, fmt.Sprintf("审批代码 %s: 同步失败 - %v", approvalCode, err))
			continue
		}

		// 累计统计信息
		totalNewRecords += resp.NewRecords
		totalUpdatedRecords += resp.UpdatedRecords

		logger.Info("审批代码标签增量同步完成",
			zap.String("approval_code", approvalCode),
			zap.String("tag", tag),
			zap.Int("new_records", resp.NewRecords),
			zap.Int("updated_records", resp.UpdatedRecords),
		)
	}

	// 记录总体同步结果
	logger.Info("标签增量合同同步任务完成",
		zap.String("tag", tag),
		zap.Int("total_approval_codes", len(approvalCodes)),
		zap.Int("total_new_records", totalNewRecords),
		zap.Int("total_updated_records", totalUpdatedRecords),
		zap.Int("error_count", len(errors)),
		zap.Strings("errors", errors),
	)

	if len(errors) > 0 {
		return fmt.Errorf("标签 %s 部分审批代码增量同步失败: %v", tag, errors)
	}

	return nil
}

// getLastSuccessfulSyncTime 获取指定审批代码的最后成功同步时间
func (s *ContractSyncSchedulerService) getLastSuccessfulSyncTime(approvalCode string) (time.Time, error) {
	ctx, logger := global.Log4Task(context.Background(), "getLastSuccessfulSyncTime",
		zap.String("approval_code", approvalCode),
	)
	_ = ctx

	// 查询最后一次成功同步的记录
	db := query.InsContractSyncLog

	syncLog, err := db.WithContext(context.Background()).
		Where(db.ApprovalCode.Eq(approvalCode)).
		Where(db.Status.Eq("success")).
		Order(db.EndTime.Desc()).
		First()

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果没有找到成功的同步记录，返回默认时间（30天前）
			defaultTime := time.Now().AddDate(0, 0, -30)
			logger.Info("未找到成功同步记录，使用默认时间",
				zap.Time("default_time", defaultTime),
			)
			return defaultTime, nil
		}
		logger.Error("查询最后同步时间失败", zap.Error(err))
		return time.Time{}, fmt.Errorf("查询最后同步时间失败: %w", err)
	}

	logger.Info("获取最后同步时间成功",
		zap.Time("last_sync_time", syncLog.EndTime),
		zap.String("sync_status", syncLog.Status),
	)

	return syncLog.EndTime, nil
}

// SyncContractsWithCustomTimeRange 根据自定义时间范围同步合同
func (s *ContractSyncSchedulerService) SyncContractsWithCustomTimeRange(approvalCodes []string, startTime, endTime time.Time) error {
	ctx, logger := global.Log4Task(context.Background(), "SyncContractsWithCustomTimeRange",
		zap.Strings("approval_codes", approvalCodes),
		zap.Time("start_time", startTime),
		zap.Time("end_time", endTime),
	)
	_ = ctx

	logger.Info("开始执行自定义时间范围合同同步任务")

	if len(approvalCodes) == 0 {
		logger.Warn("审批代码列表为空，跳过同步")
		return fmt.Errorf("审批代码列表为空")
	}

	// 验证时间范围
	if startTime.After(endTime) {
		return fmt.Errorf("开始时间不能晚于结束时间")
	}

	// 构建同步请求
	req := request.ContractMultiSyncRequest{
		ApprovalCodes: approvalCodes,
		StartTime:     &startTime,
		EndTime:       &endTime,
		BatchSize:     20,
		PageSize:      100,
		MaxRetries:    3,
		RetryDelay:    5,
		ForceSync:     true, // 自定义时间范围通常是强制同步
	}

	logger.Info("准备执行自定义时间范围同步",
		zap.Int("approval_codes_count", len(approvalCodes)),
		zap.Time("start_time", startTime),
		zap.Time("end_time", endTime),
	)

	// 执行同步
	resp, err := s.contractService.SyncMultipleContractData(req)
	if err != nil {
		logger.Error("自定义时间范围同步失败", zap.Error(err))
		return fmt.Errorf("自定义时间范围同步失败: %w", err)
	}

	// 记录同步结果
	logger.Info("自定义时间范围合同同步完成",
		zap.Int("total_codes", resp.TotalCodes),
		zap.Int("success_codes", resp.SuccessCodes),
		zap.Int("failed_codes", resp.FailedCodes),
		zap.Int("total_records", resp.TotalRecords),
		zap.Int("new_records", resp.NewRecords),
		zap.Int("updated_records", resp.UpdatedRecords),
		zap.String("duration", resp.Duration),
		zap.Bool("success", resp.Success),
	)

	if !resp.Success {
		return fmt.Errorf("部分审批代码同步失败: %s", resp.ErrorMsg)
	}

	return nil
}

// GetBasicSyncStatus 获取基本同步状态信息
func (s *ContractSyncSchedulerService) GetBasicSyncStatus() (map[string]interface{}, error) {
	ctx, logger := global.Log4Task(context.Background(), "GetBasicSyncStatus")
	_ = ctx

	logger.Info("开始获取基本同步状态")

	result := make(map[string]interface{})

	// 获取所有启用的审批代码
	approvalCodes := s.configService.GetAllApprovalCodes()

	// 基本统计信息
	result["total_enabled_codes"] = len(approvalCodes)
	result["check_time"] = time.Now().Format("2006-01-02 15:04:05")

	if len(approvalCodes) == 0 {
		result["message"] = "未找到启用的审批代码"
		return result, nil
	}

	// 统计最近同步情况
	db := query.InsContractSyncLog

	// 最近24小时的同步次数
	yesterday := time.Now().AddDate(0, 0, -1)
	recentSyncCount, _ := db.WithContext(context.Background()).
		Where(db.StartTime.Gte(yesterday)).
		Count()
	result["recent_sync_count"] = recentSyncCount

	// 最近成功同步次数
	recentSuccessCount, _ := db.WithContext(context.Background()).
		Where(db.StartTime.Gte(yesterday)).
		Where(db.Status.Eq("success")).
		Count()
	result["recent_success_count"] = recentSuccessCount

	logger.Info("获取基本同步状态完成",
		zap.Int("codes_count", len(approvalCodes)),
		zap.Int64("recent_sync_count", recentSyncCount),
		zap.Int64("recent_success_count", recentSuccessCount),
	)

	return result, nil
}

// ===== 定时任务调用方法 =====

// IncrementalSyncTask 增量同步定时任务
// 适用于定时任务配置：每30分钟执行
func IncrementalSyncTask() {
	ctx, logger := global.Log4Task(context.Background(), "IncrementalSyncTask")
	_ = ctx

	logger.Info("开始执行增量同步定时任务")

	scheduler := NewContractSyncSchedulerService()
	err := scheduler.SyncIncrementalContracts()

	if err != nil {
		logger.Error("增量同步定时任务失败", zap.Error(err))
		// 可以在这里添加告警通知
		return
	}

	logger.Info("增量同步定时任务完成")
}

// IncrementalSyncByTagTask 根据标签的增量同步定时任务
// 适用于定时任务配置：可配置不同标签和执行频率
func IncrementalSyncByTagTask(tag string) {
	ctx, logger := global.Log4Task(context.Background(), "IncrementalSyncByTagTask",
		zap.String("tag", tag),
	)
	_ = ctx

	logger.Info("开始执行标签增量同步定时任务", zap.String("tag", tag))

	scheduler := NewContractSyncSchedulerService()
	err := scheduler.SyncIncrementalContractsByTag(tag)

	if err != nil {
		logger.Error("标签增量同步定时任务失败",
			zap.String("tag", tag),
			zap.Error(err),
		)
		// 可以在这里添加告警通知
		return
	}

	logger.Info("标签增量同步定时任务完成", zap.String("tag", tag))
}

// ManualIncrementalSyncByTag 手动触发指定标签的增量同步
func ManualIncrementalSyncByTag(tag string) error {
	ctx, logger := global.Log4Task(context.Background(), "ManualIncrementalSyncByTag",
		zap.String("tag", tag),
	)
	_ = ctx

	logger.Info("开始手动增量同步", zap.String("tag", tag))

	scheduler := NewContractSyncSchedulerService()
	err := scheduler.SyncIncrementalContractsByTag(tag)

	if err != nil {
		logger.Error("手动增量同步失败",
			zap.String("tag", tag),
			zap.Error(err),
		)
		return err
	}

	logger.Info("手动增量同步完成", zap.String("tag", tag))
	return nil
}

// ManualIncrementalSyncWithTimeRange 手动触发指定时间范围的增量同步
func ManualIncrementalSyncWithTimeRange(approvalCodes []string, startTime, endTime time.Time) error {
	ctx, logger := global.Log4Task(context.Background(), "ManualIncrementalSyncWithTimeRange",
		zap.Strings("approval_codes", approvalCodes),
		zap.Time("start_time", startTime),
		zap.Time("end_time", endTime),
	)
	_ = ctx

	logger.Info("开始手动时间范围增量同步")

	scheduler := NewContractSyncSchedulerService()
	err := scheduler.SyncContractsWithCustomTimeRange(approvalCodes, startTime, endTime)

	if err != nil {
		logger.Error("手动时间范围增量同步失败",
			zap.Strings("approval_codes", approvalCodes),
			zap.Error(err),
		)
		return err
	}

	logger.Info("手动时间范围增量同步完成")
	return nil
}

// GetIncrementalSyncStatus 获取增量同步状态
func GetIncrementalSyncStatus() (map[string]interface{}, error) {
	ctx, logger := global.Log4Task(context.Background(), "GetIncrementalSyncStatus")
	_ = ctx

	logger.Info("开始获取增量同步状态")

	scheduler := NewContractSyncSchedulerService()

	// 获取所有启用的审批代码
	approvalCodes := scheduler.configService.GetAllApprovalCodes()
	if len(approvalCodes) == 0 {
		return map[string]interface{}{
			"message":     "未找到启用的审批代码",
			"codes_count": 0,
		}, nil
	}

	// 获取基本同步状态
	status, err := scheduler.GetBasicSyncStatus()
	if err != nil {
		logger.Error("获取增量同步状态失败", zap.Error(err))
		return nil, err
	}

	logger.Info("获取增量同步状态完成",
		zap.Int("codes_count", len(approvalCodes)),
	)

	return status, nil
}
