package insbuy

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"github.com/flipped-aurora/gin-vue-admin/server/service/insbuy/insfinance"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ContractTransformService struct {
	transformer *ContractTransformer
	configMgr   *ConfigManager
}

// NewContractTransformService 创建合同转换服务
func NewContractTransformService() *ContractTransformService {
	service := &ContractTransformService{
		transformer: NewContractTransformer(),
		configMgr:   NewConfigManager(),
	}

	// 加载配置
	if err := service.loadConfigurations(); err != nil {
		global.GVA_LOG.Error("加载转换配置失败", zap.Error(err))
	}

	return service
}

// StagingTransformResult 中间表转换结果
type StagingTransformResult struct {
	Success       bool                                  `json:"success"`
	MainRecord    *insbuy.InsContractTransformStaging   `json:"main_record"`
	DetailRecords []*insbuy.InsContractTransformStaging `json:"detail_records"`
	Errors        []string                              `json:"errors"`
	Warnings      []string                              `json:"warnings"`
}

// loadConfigurations 加载转换配置
func (s *ContractTransformService) loadConfigurations() error {
	// 加载所有配置文件
	if err := s.configMgr.LoadAllConfigs(); err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	// 设置映射规则到转换器
	s.transformer.SetMappingRules(s.configMgr.GetAllRules())

	return nil
}

// TransformContract 转换单个合同数据
func (s *ContractTransformService) TransformContract(contractId uint) (*StagingTransformResult, error) {
	ctx := context.Background()

	// 1. 查询源合同数据
	contract, err := s.getContractById(contractId)
	if err != nil {
		return nil, fmt.Errorf("查询源合同失败: %w", err)
	}

	// 2. 转换为ContractDetails格式
	contractDetails := s.convertToContractDetails(contract)

	// 3. 使用现有的转换器进行转换
	transformResult, err := s.transformer.Transform(ctx, contractDetails)
	if err != nil {
		return &StagingTransformResult{
			Success: false,
			Errors:  []string{fmt.Sprintf("数据转换失败: %v", err)},
		}, nil
	}

	// 4. 转换结果为中间表格式
	return s.convertTransformResult(transformResult, contract)
}

// convertToContractDetails 将数据库模型转换为ContractDetails格式
func (s *ContractTransformService) convertToContractDetails(contract *insbuy.InsContract) *insfinance.ContractDetails {
	// 解析表单数据
	var formData []map[string]interface{}
	if contract.Form != nil {
		json.Unmarshal(contract.Form, &formData)
	}

	// 转换表单数据为widget格式的map
	formMap := make(map[string]interface{})
	for _, field := range formData {
		if id, exists := field["id"]; exists {
			formMap[fmt.Sprintf("%v", id)] = field
		}
	}

	// 将formMap转换为JSON字符串
	formJson, _ := json.Marshal(formMap)

	return &insfinance.ContractDetails{
		ApprovalCode: contract.ApprovalCode,
		ApprovalName: contract.ApprovalName,
		InstanceCode: contract.InstanceCode,
		DepartmentId: contract.DepartmentId,
		EndTime:      contract.EndTime,
		OpenId:       contract.OpenId,
		Reverted:     contract.Reverted,
		SerialNumber: contract.SerialNumber,
		StartTime:    contract.StartTime,
		Status:       contract.Status,
		UserId:       contract.UserId,
		Uuid:         contract.Uuid,
		Form:         string(formJson),
		Timeline:     contract.Timeline,
		CommentList:  contract.CommentList,
		TaskList:     contract.TaskList,
	}
}

// convertTransformResult 将转换器结果转换为中间表格式
func (s *ContractTransformService) convertTransformResult(transformResult *TransformResult, contract *insbuy.InsContract) (*StagingTransformResult, error) {
	result := &StagingTransformResult{
		Success:       transformResult.Success,
		Errors:        make([]string, 0),
		Warnings:      transformResult.Warnings,
		DetailRecords: make([]*insbuy.InsContractTransformStaging, 0),
	}

	// 转换验证错误为字符串
	for _, validationError := range transformResult.Errors {
		result.Errors = append(result.Errors, fmt.Sprintf("%s: %s", validationError.Field, validationError.Message))
	}

	if !transformResult.Success || transformResult.Data == nil {
		return result, nil
	}

	standardData := transformResult.Data

	// 检查是否有费用明细
	if len(standardData.ExpenseDetails) > 0 {
		// 有明细，拆分处理
		return s.convertWithExpenseDetails(standardData, contract, result)
	} else {
		// 无明细，单条记录
		return s.convertSingleRecord(standardData, contract, result)
	}
}

// convertSingleRecord 转换单条记录（无明细）
func (s *ContractTransformService) convertSingleRecord(standardData *StandardContractData, contract *insbuy.InsContract, result *StagingTransformResult) (*StagingTransformResult, error) {
	staging := s.createStagingRecord(standardData, contract, 0, 1)
	result.MainRecord = staging
	return result, nil
}

// convertWithExpenseDetails 转换包含费用明细的记录
func (s *ContractTransformService) convertWithExpenseDetails(standardData *StandardContractData, contract *insbuy.InsContract, result *StagingTransformResult) (*StagingTransformResult, error) {
	totalDetails := len(standardData.ExpenseDetails)

	// 创建主记录
	mainRecord := s.createStagingRecord(standardData, contract, 0, totalDetails+1)
	result.MainRecord = mainRecord

	// 创建明细记录
	for i, expenseDetail := range standardData.ExpenseDetails {
		detailRecord := s.createStagingRecord(standardData, contract, i+1, totalDetails+1)

		// 设置明细特有字段
		detailRecord.ExpenseType = expenseDetail.ExpenseType
		detailRecord.ExpenseLocation = expenseDetail.Location
		detailRecord.ExpenseDateRange = expenseDetail.DateRange
		detailRecord.InvoiceType = expenseDetail.VatInvoiceType
		detailRecord.CurrentRequestAmount = fmt.Sprintf("%.2f", expenseDetail.Amount)

		// 设置日期
		if !expenseDetail.StartDate.IsZero() {
			detailRecord.ExpenseStartDate = &expenseDetail.StartDate
		}
		if !expenseDetail.EndDate.IsZero() {
			detailRecord.ExpenseEndDate = &expenseDetail.EndDate
		}

		result.DetailRecords = append(result.DetailRecords, detailRecord)
	}

	return result, nil
}

// createStagingRecord 创建中间表记录
func (s *ContractTransformService) createStagingRecord(standardData *StandardContractData, contract *insbuy.InsContract, detailIndex, detailTotal int) *insbuy.InsContractTransformStaging {
	now := time.Now()

	staging := &insbuy.InsContractTransformStaging{
		SourceContractId:   contract.ID,
		SourceInstanceCode: contract.InstanceCode,
		SourceApprovalCode: contract.ApprovalCode,
		DetailIndex:        detailIndex,
		DetailTotal:        detailTotal,
		TransformStatus:    "transformed",
		TransformTime:      &now,
		OriginalFormData:   contract.Form,
	}

	// 映射标准化字段
	staging.SerialNumber = s.parseIntFromString(standardData.SerialNumber)
	staging.CompletionTime = standardData.CompleteTime.Format("2006-01-02 15:04:05")
	staging.ReportMonth = standardData.CompleteTime.Format("2006-01")
	staging.Title = standardData.Title
	staging.PaymentEntity = standardData.PaymentEntity
	staging.PaymentReason = standardData.PaymentReason
	staging.BusinessType = standardData.BusinessType
	staging.ContractAmount = fmt.Sprintf("%.2f", standardData.ContractSignAmount)
	staging.ContractPaidAmount = fmt.Sprintf("%.2f", standardData.ContractPaidAmount)
	staging.CurrentRequestAmount = fmt.Sprintf("%.2f", standardData.CurrentRequestAmount)
	staging.TaxRate = fmt.Sprintf("%.2f", standardData.TaxRate*100) // 转换为百分比
	staging.ExpenseCategory = s.getExpenseCategory(standardData.BusinessType)
	staging.IncludeInReport = "是" // 默认纳入管报
	staging.AccountName = standardData.AccountHolder
	staging.ReportEntity = s.getReportEntity(standardData.PaymentEntity)
	staging.ReportEntityDetail = standardData.PaymentEntity
	staging.ReportRegion = standardData.BankRegion
	staging.InitiatorName = standardData.InitiatorName
	staging.InitiatorDepartment = standardData.InitiatorDepartment
	staging.Department = standardData.InitiatorDepartment

	// 计算衍生字段
	s.calculateDerivedFields(staging)

	return staging
}

// 辅助方法

// parseIntFromString 从字符串解析整数
func (s *ContractTransformService) parseIntFromString(str string) int {
	if str == "" {
		return 0
	}
	if val, err := strconv.Atoi(str); err == nil {
		return val
	}
	return 0
}

// getExpenseCategory 根据业务类型获取费用类别
func (s *ContractTransformService) getExpenseCategory(businessType string) string {
	categoryMap := map[string]string{
		"付款申请": "合同付款",
		"费用报销": "费用报销",
		"采购申请": "采购费用",
		"差旅申请": "差旅费用",
		"销售合同": "销售费用",
		"服务合同": "服务费用",
	}

	if category, exists := categoryMap[businessType]; exists {
		return category
	}
	return "其他费用"
}

// getReportEntity 根据付款主体获取管报主体
func (s *ContractTransformService) getReportEntity(paymentEntity string) string {
	// 这里可以根据实际业务规则进行映射
	// 暂时返回付款主体本身
	return paymentEntity
}

// calculateDerivedFields 计算衍生字段
func (s *ContractTransformService) calculateDerivedFields(staging *insbuy.InsContractTransformStaging) {
	// 计算待付款金额
	contractAmount, _ := strconv.ParseFloat(staging.ContractAmount, 64)
	paidAmount, _ := strconv.ParseFloat(staging.ContractPaidAmount, 64)
	staging.PendingAmount = fmt.Sprintf("%.2f", contractAmount-paidAmount)

	// 设置管报确认金额（默认等于本次请款金额）
	if staging.ReportConfirmAmount == "" {
		staging.ReportConfirmAmount = staging.CurrentRequestAmount
	}

	// 计算不含税金额
	if staging.TaxRate != "" && staging.CurrentRequestAmount != "" {
		taxRateStr := strings.TrimSuffix(staging.TaxRate, "%")
		taxRate, _ := strconv.ParseFloat(taxRateStr, 64)
		amount, _ := strconv.ParseFloat(staging.CurrentRequestAmount, 64)
		if taxRate > 0 {
			excludeTaxAmount := amount / (1 + taxRate/100)
			staging.AmountExcludeTax = fmt.Sprintf("%.2f", excludeTaxAmount)
		} else {
			staging.AmountExcludeTax = staging.CurrentRequestAmount
		}
	}
}

// ToRegionalExpenseDetailItem 转换为RegionalExpenseDetailItem格式
func (s *ContractTransformService) ToRegionalExpenseDetailItem(staging *insbuy.InsContractTransformStaging) RegionalExpenseDetailItem {
	return RegionalExpenseDetailItem{
		SerialNumber:         staging.SerialNumber,
		CompletionTime:       staging.CompletionTime,
		ReportMonth:          staging.ReportMonth,
		Title:                staging.Title,
		PaymentEntity:        staging.PaymentEntity,
		PaymentReason:        staging.PaymentReason,
		BusinessType:         staging.BusinessType,
		ContractAmount:       staging.ContractAmount,
		ContractPaidAmount:   staging.ContractPaidAmount,
		CurrentRequestAmount: staging.CurrentRequestAmount,
		PendingAmount:        staging.PendingAmount,
		ReportConfirmAmount:  staging.ReportConfirmAmount,
		TaxRate:              staging.TaxRate,
		AmountExcludeTax:     staging.AmountExcludeTax,
		ExpenseCategory:      staging.ExpenseCategory,
		IncludeInReport:      staging.IncludeInReport,
		AccountName:          staging.AccountName,
		ReportEntity:         staging.ReportEntity,
		ReportEntityDetail:   staging.ReportEntityDetail,
		ReportRegion:         staging.ReportRegion,
		InitiatorName:        staging.InitiatorName,
		InitiatorDepartment:  staging.InitiatorDepartment,
		Department:           staging.Department,
		StoreId:              0, // 统一设为总部ID
	}
}

// BatchTransformContracts 批量转换合同数据
func (s *ContractTransformService) BatchTransformContracts(approvalCode string, operatorId uint, operatorName string) (string, error) {
	// 1. 生成批次号
	batchNo := s.generateBatchNo()

	// 2. 查询待转换的合同数据
	contracts, err := s.getContractsForTransform(approvalCode)
	if err != nil {
		return "", fmt.Errorf("查询待转换合同失败: %w", err)
	}

	// 3. 创建批次记录
	batch := &insbuy.InsContractTransformBatch{
		BatchNo:       batchNo,
		BatchName:     fmt.Sprintf("批量转换-%s", time.Now().Format("********-150405")),
		ApprovalCode:  approvalCode,
		OperationType: "transform",
		TotalCount:    len(contracts),
		Status:        "processing",
		StartTime:     &time.Time{},
		OperatorId:    operatorId,
		OperatorName:  operatorName,
	}
	*batch.StartTime = time.Now()

	if err := global.GVA_DB.Create(batch).Error; err != nil {
		return "", fmt.Errorf("创建批次记录失败: %w", err)
	}

	// 4. 异步处理转换任务
	go s.processBatchTransform(batch, contracts)

	return batchNo, nil
}

// BatchAuditRecords 批量审核记录
func (s *ContractTransformService) BatchAuditRecords(stagingIds []uint, auditStatus string, auditorId uint, auditNote string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 批量更新审核状态
		err := tx.Model(&insbuy.InsContractTransformStaging{}).
			Where("id IN ?", stagingIds).
			Updates(map[string]interface{}{
				"audit_status": auditStatus,
				"auditor_id":   auditorId,
				"audit_time":   now,
				"audit_note":   auditNote,
				"updated_at":   now,
			}).Error

		if err != nil {
			return err
		}

		// 记录审核日志
		for _, stagingId := range stagingIds {
			log := &insbuy.InsContractTransformLog{
				StagingId:       stagingId,
				OperationType:   "audit",
				OperationStatus: "success",
				OperatorId:      &auditorId,
				OperatorName:    "审核员", // 可以从用户表查询
			}
			if err := tx.Create(log).Error; err != nil {
				global.GVA_LOG.Error("创建审核日志失败", zap.Error(err))
			}
		}

		return nil
	})
}

// StoreToSourceStorage 存储到SourceStorageData（与现有Excel导入保持一致）
func (s *ContractTransformService) StoreToSourceStorage(stagingRecords []*insbuy.InsContractTransformStaging) error {
	// 1. 转换为RegionalExpenseDetailItem格式
	var items []RegionalExpenseDetailItem
	for _, staging := range stagingRecords {
		if staging.AuditStatus != "approved" {
			continue // 只处理审核通过的记录
		}

		item := s.ToRegionalExpenseDetailItem(staging)
		items = append(items, item)
	}

	if len(items) == 0 {
		return fmt.Errorf("没有审核通过的记录可以存储")
	}

	// 2. 使用现有的存储机制
	// 创建RegionalExpenseDetailImport实例并调用其存储方法
	importService := &RegionalExpenseDetailImport{
		Data: items,
	}

	// 调用保存到数据库的方法
	err := importService.saveToDatabase(context.Background(), 0) // 使用总部ID
	if err != nil {
		return fmt.Errorf("存储到SourceStorageData失败: %w", err)
	}

	// 3. 更新staging记录的存储状态
	for _, staging := range stagingRecords {
		if staging.AuditStatus == "approved" {
			staging.StorageStatus = "stored"
			staging.StorageTime = &time.Time{}
			*staging.StorageTime = time.Now()

			// 更新数据库
			if err := global.GVA_DB.Save(staging).Error; err != nil {
				global.GVA_LOG.Error("更新存储状态失败", zap.Error(err), zap.Uint("staging_id", staging.ID))
			}
		}
	}

	return nil
}

// 辅助方法

// getContractById 根据ID查询合同
func (s *ContractTransformService) getContractById(contractId uint) (*insbuy.InsContract, error) {
	var contract insbuy.InsContract
	err := global.GVA_DB.Where("id = ?", contractId).First(&contract).Error
	if err != nil {
		return nil, err
	}
	return &contract, nil
}

// parseFormData 解析表单数据
func (s *ContractTransformService) parseFormData(formJson interface{}) (map[string]interface{}, error) {
	var formData map[string]interface{}

	// 如果是字符串，先解析JSON
	if jsonStr, ok := formJson.(string); ok {
		if err := json.Unmarshal([]byte(jsonStr), &formData); err != nil {
			return nil, err
		}
	} else if data, ok := formJson.(map[string]interface{}); ok {
		formData = data
	} else {
		// 尝试JSON序列化再反序列化
		jsonBytes, err := json.Marshal(formJson)
		if err != nil {
			return nil, err
		}
		if err := json.Unmarshal(jsonBytes, &formData); err != nil {
			return nil, err
		}
	}

	return formData, nil
}

// extractExpenseDetails 提取费用明细数据
func (s *ContractTransformService) extractExpenseDetails(formData map[string]interface{}) []map[string]interface{} {
	var details []map[string]interface{}

	// 查找费用明细相关的字段
	if expenseDetails, ok := formData["widget_expense_details"]; ok {
		if detailsArray, ok := expenseDetails.([]interface{}); ok {
			for _, detail := range detailsArray {
				if detailMap, ok := detail.(map[string]interface{}); ok {
					details = append(details, detailMap)
				}
			}
		}
	}

	return details
}

// getFormValue 获取表单字段值
func (s *ContractTransformService) getFormValue(formData map[string]interface{}, fieldName string) string {
	if value, ok := formData[fieldName]; ok {
		if str, ok := value.(string); ok {
			return str
		}
		// 尝试转换其他类型为字符串
		return fmt.Sprintf("%v", value)
	}
	return ""
}

// copyBaseInfo 复制基础信息
func (s *ContractTransformService) copyBaseInfo(target, source *insbuy.InsContractTransformStaging) {
	target.Title = source.Title
	target.InitiatorName = source.InitiatorName
	target.CompletionTime = source.CompletionTime
	target.ReportMonth = source.ReportMonth
	target.PaymentEntity = source.PaymentEntity
	target.PaymentReason = source.PaymentReason
	target.BusinessType = source.BusinessType
	target.ReportEntity = source.ReportEntity
	target.ReportEntityDetail = source.ReportEntityDetail
	target.ReportRegion = source.ReportRegion
	target.InitiatorDepartment = source.InitiatorDepartment
	target.Department = source.Department
}

// mapDetailFields 映射明细字段
func (s *ContractTransformService) mapDetailFields(staging *insbuy.InsContractTransformStaging, detail map[string]interface{}) {
	// 费用类型
	staging.ExpenseType = s.getFormValue(detail, "expense_type")

	// 费用地点
	staging.ExpenseLocation = s.getFormValue(detail, "location")

	// 费用日期区间
	staging.ExpenseDateRange = s.getFormValue(detail, "date_range")

	// 发票类型
	staging.InvoiceType = s.getFormValue(detail, "vat_invoice_type")

	// 明细金额
	staging.CurrentRequestAmount = s.getFormValue(detail, "amount")
}

// calculateDerivedFields 计算衍生字段
func (s *ContractTransformService) calculateDerivedFields(staging *insbuy.InsContractTransformStaging) {
	// 计算待付款金额
	contractAmount, _ := strconv.ParseFloat(staging.ContractAmount, 64)
	paidAmount, _ := strconv.ParseFloat(staging.ContractPaidAmount, 64)
	staging.PendingAmount = fmt.Sprintf("%.2f", contractAmount-paidAmount)

	// 设置管报确认金额（默认等于本次请款金额）
	if staging.ReportConfirmAmount == "" {
		staging.ReportConfirmAmount = staging.CurrentRequestAmount
	}

	// 计算不含税金额
	if staging.TaxRate != "" && staging.CurrentRequestAmount != "" {
		taxRate, _ := strconv.ParseFloat(staging.TaxRate, 64)
		amount, _ := strconv.ParseFloat(staging.CurrentRequestAmount, 64)
		if taxRate > 0 {
			excludeTaxAmount := amount / (1 + taxRate/100)
			staging.AmountExcludeTax = fmt.Sprintf("%.2f", excludeTaxAmount)
		} else {
			staging.AmountExcludeTax = staging.CurrentRequestAmount
		}
	}
}

// generateBatchNo 生成批次号
func (s *ContractTransformService) generateBatchNo() string {
	return fmt.Sprintf("CT%s%04d", time.Now().Format("********150405"), time.Now().Nanosecond()%10000)
}

// getContractsForTransform 获取待转换的合同列表
func (s *ContractTransformService) getContractsForTransform(approvalCode string) ([]*insbuy.InsContract, error) {
	var contracts []*insbuy.InsContract
	query := global.GVA_DB.Model(&insbuy.InsContract{})

	if approvalCode != "" {
		query = query.Where("approval_code = ?", approvalCode)
	}

	// 排除已经转换过的合同
	query = query.Where("id NOT IN (SELECT source_contract_id FROM ins_contract_transform_staging)")

	err := query.Find(&contracts).Error
	return contracts, err
}

// processBatchTransform 处理批量转换（异步执行）
func (s *ContractTransformService) processBatchTransform(batch *insbuy.InsContractTransformBatch, contracts []*insbuy.InsContract) {
	defer func() {
		if r := recover(); r != nil {
			global.GVA_LOG.Error("批量转换异常", zap.Any("error", r), zap.String("batch_no", batch.BatchNo))
			// 更新批次状态为失败
			batch.Status = "failed"
			batch.EndTime = &time.Time{}
			*batch.EndTime = time.Now()
			global.GVA_DB.Save(batch)
		}
	}()

	successCount := 0
	failedCount := 0
	detailRecordsCreated := 0

	for _, contract := range contracts {
		result, err := s.TransformContract(contract.ID)
		if err != nil {
			failedCount++
			s.logTransformError(batch.BatchNo, contract.ID, err.Error())
			continue
		}

		if !result.Success {
			failedCount++
			s.logTransformError(batch.BatchNo, contract.ID, strings.Join(result.Errors, "; "))
			continue
		}

		// 保存转换结果
		if result.MainRecord != nil {
			if err := global.GVA_DB.Create(result.MainRecord).Error; err != nil {
				failedCount++
				s.logTransformError(batch.BatchNo, contract.ID, err.Error())
				continue
			}
		}

		for _, detail := range result.DetailRecords {
			if err := global.GVA_DB.Create(detail).Error; err != nil {
				global.GVA_LOG.Error("保存明细记录失败", zap.Error(err))
			} else {
				detailRecordsCreated++
			}
		}

		successCount++
		batch.ProcessedCount++

		// 定期更新进度
		if batch.ProcessedCount%10 == 0 {
			global.GVA_DB.Save(batch)
		}
	}

	// 更新最终状态
	batch.SuccessCount = successCount
	batch.FailedCount = failedCount
	batch.DetailRecordsCreated = detailRecordsCreated
	batch.ProcessedCount = len(contracts)
	batch.Status = "completed"
	batch.EndTime = &time.Time{}
	*batch.EndTime = time.Now()

	global.GVA_DB.Save(batch)
}

// logTransformError 记录转换错误
func (s *ContractTransformService) logTransformError(batchNo string, contractId uint, errorMsg string) {
	log := &insbuy.InsContractTransformLog{
		BatchNo:          batchNo,
		SourceContractId: contractId,
		OperationType:    "transform",
		OperationStatus:  "failed",
		ErrorMessage:     errorMsg,
	}
	global.GVA_DB.Create(log)
}
