package insbuy

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/insbuy"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ContractTransformService struct{}

// TransformResult 转换结果
type TransformResult struct {
	Success       bool                                  `json:"success"`
	MainRecord    *insbuy.InsContractTransformStaging   `json:"main_record"`
	DetailRecords []*insbuy.InsContractTransformStaging `json:"detail_records"`
	Errors        []string                              `json:"errors"`
	Warnings      []string                              `json:"warnings"`
}

// TransformContract 转换单个合同数据
func (s *ContractTransformService) TransformContract(contractId uint) (*TransformResult, error) {
	startTime := time.Now()

	// 1. 查询源合同数据
	contract, err := s.getContractById(contractId)
	if err != nil {
		return nil, fmt.Errorf("查询源合同失败: %w", err)
	}

	// 2. 解析表单数据
	formData, err := s.parseFormData(contract.Form)
	if err != nil {
		return &TransformResult{
			Success: false,
			Errors:  []string{fmt.Sprintf("解析表单数据失败: %v", err)},
		}, nil
	}

	// 3. 检查是否有明细数据
	detailData := s.extractExpenseDetails(formData)

	if len(detailData) == 0 {
		// 无明细，处理单条记录
		return s.transformSingleRecord(contract, formData, startTime)
	} else {
		// 有明细，拆分处理
		return s.transformWithDetails(contract, formData, detailData, startTime)
	}
}

// transformSingleRecord 转换单条记录（无明细）
func (s *ContractTransformService) transformSingleRecord(contract *insbuy.InsContract, formData map[string]interface{}, startTime time.Time) (*TransformResult, error) {
	result := &TransformResult{
		Success:  true,
		Errors:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// 创建转换记录
	staging := &insbuy.InsContractTransformStaging{
		SourceContractId:   contract.ID,
		SourceInstanceCode: contract.InstanceCode,
		SourceApprovalCode: contract.ApprovalCode,
		DetailIndex:        0,
		DetailTotal:        1,
		TransformStatus:    "transformed",
		TransformTime:      &startTime,
		OriginalFormData:   contract.Form,
	}

	// 映射基础字段
	s.mapBaseFields(staging, contract, formData)

	// 映射业务字段
	s.mapBusinessFields(staging, formData)

	// 计算衍生字段
	s.calculateDerivedFields(staging)

	result.MainRecord = staging
	return result, nil
}

// transformWithDetails 转换包含明细的记录
func (s *ContractTransformService) transformWithDetails(contract *insbuy.InsContract, formData map[string]interface{}, detailData []map[string]interface{}, startTime time.Time) (*TransformResult, error) {
	result := &TransformResult{
		Success:       true,
		Errors:        make([]string, 0),
		Warnings:      make([]string, 0),
		DetailRecords: make([]*insbuy.InsContractTransformStaging, 0),
	}

	// 1. 创建主记录
	mainRecord := &insbuy.InsContractTransformStaging{
		SourceContractId:   contract.ID,
		SourceInstanceCode: contract.InstanceCode,
		SourceApprovalCode: contract.ApprovalCode,
		DetailIndex:        0,
		DetailTotal:        len(detailData) + 1,
		TransformStatus:    "transformed",
		TransformTime:      &startTime,
		OriginalFormData:   contract.Form,
	}

	// 映射主记录字段
	s.mapBaseFields(mainRecord, contract, formData)
	s.mapBusinessFields(mainRecord, formData)

	// 2. 创建明细记录
	totalAmount := 0.0
	for i, detail := range detailData {
		detailRecord := &insbuy.InsContractTransformStaging{
			SourceContractId:   contract.ID,
			SourceInstanceCode: contract.InstanceCode,
			SourceApprovalCode: contract.ApprovalCode,
			DetailIndex:        i + 1,
			DetailTotal:        len(detailData) + 1,
			TransformStatus:    "transformed",
			TransformTime:      &startTime,
		}

		// 复制主记录的基础信息
		s.copyBaseInfo(detailRecord, mainRecord)

		// 映射明细字段
		s.mapDetailFields(detailRecord, detail)

		// 计算衍生字段
		s.calculateDerivedFields(detailRecord)

		result.DetailRecords = append(result.DetailRecords, detailRecord)

		// 累计金额
		if amount, err := strconv.ParseFloat(detailRecord.CurrentRequestAmount, 64); err == nil {
			totalAmount += amount
		}
	}

	// 3. 更新主记录汇总信息
	mainRecord.CurrentRequestAmount = fmt.Sprintf("%.2f", totalAmount)
	s.calculateDerivedFields(mainRecord)

	result.MainRecord = mainRecord
	return result, nil
}

// mapBaseFields 映射基础字段
func (s *ContractTransformService) mapBaseFields(staging *insbuy.InsContractTransformStaging, contract *insbuy.InsContract, formData map[string]interface{}) {
	// 基础信息
	staging.Title = contract.ApprovalName
	staging.InitiatorName = "todo 用户" //todo contract.InitiatorName

	// 完成时间格式化
	if contract.EndTime != nil {
		staging.CompletionTime = contract.EndTime.Format(time.DateTime)
		// 管报月份（从完成时间计算）
		staging.ReportMonth = contract.EndTime.Format("2006-01")
	}

	// 序号（可以从表单中提取或自动生成）
	if serialNum := s.getFormValue(formData, "widget_serial_number"); serialNum != "" {
		if num, err := strconv.Atoi(serialNum); err == nil {
			staging.SerialNumber = num
		}
	}

	// 发起人部门
	if dept := s.getFormValue(formData, "widget_department"); dept != "" {
		staging.InitiatorDepartment = dept
		staging.Department = dept
	}
}

// mapBusinessFields 映射业务字段
func (s *ContractTransformService) mapBusinessFields(staging *insbuy.InsContractTransformStaging, formData map[string]interface{}) {
	// 付款相关
	staging.PaymentEntity = s.getFormValue(formData, "widget_payment_entity")
	staging.PaymentReason = s.getFormValue(formData, "widget_payment_reason")
	staging.BusinessType = s.getFormValue(formData, "widget_business_type")

	// 金额相关（保持字符串格式）
	staging.ContractAmount = s.getFormValue(formData, "widget_contract_amount")
	staging.ContractPaidAmount = s.getFormValue(formData, "widget_paid_amount")
	staging.CurrentRequestAmount = s.getFormValue(formData, "widget_request_amount")

	// 税率
	staging.TaxRate = s.getFormValue(formData, "widget_tax_rate")

	// 费用类别
	staging.ExpenseCategory = s.getFormValue(formData, "widget_expense_category")

	// 银行信息
	staging.AccountName = s.getFormValue(formData, "widget_account_name")

	// 管报相关
	staging.ReportEntity = s.getFormValue(formData, "widget_report_entity")
	staging.ReportEntityDetail = s.getFormValue(formData, "widget_report_entity_detail")
	staging.ReportRegion = s.getFormValue(formData, "widget_report_region")

	// 是否纳入管报
	if includeReport := s.getFormValue(formData, "widget_include_report"); includeReport == "是" || includeReport == "true" {
		staging.IncludeInReport = "是"
	} else {
		staging.IncludeInReport = "否"
	}
}

// ToRegionalExpenseDetailItem 转换为RegionalExpenseDetailItem格式
func (s *ContractTransformService) ToRegionalExpenseDetailItem(staging *insbuy.InsContractTransformStaging) RegionalExpenseDetailItem {
	return RegionalExpenseDetailItem{
		SerialNumber:         staging.SerialNumber,
		CompletionTime:       staging.CompletionTime,
		ReportMonth:          staging.ReportMonth,
		Title:                staging.Title,
		PaymentEntity:        staging.PaymentEntity,
		PaymentReason:        staging.PaymentReason,
		BusinessType:         staging.BusinessType,
		ContractAmount:       staging.ContractAmount,
		ContractPaidAmount:   staging.ContractPaidAmount,
		CurrentRequestAmount: staging.CurrentRequestAmount,
		PendingAmount:        staging.PendingAmount,
		ReportConfirmAmount:  staging.ReportConfirmAmount,
		TaxRate:              staging.TaxRate,
		AmountExcludeTax:     staging.AmountExcludeTax,
		ExpenseCategory:      staging.ExpenseCategory,
		IncludeInReport:      staging.IncludeInReport,
		AccountName:          staging.AccountName,
		ReportEntity:         staging.ReportEntity,
		ReportEntityDetail:   staging.ReportEntityDetail,
		ReportRegion:         staging.ReportRegion,
		InitiatorName:        staging.InitiatorName,
		InitiatorDepartment:  staging.InitiatorDepartment,
		Department:           staging.Department,
		StoreId:              0, // 统一设为总部ID
	}
}

// BatchTransformContracts 批量转换合同数据
func (s *ContractTransformService) BatchTransformContracts(approvalCode string, operatorId uint, operatorName string) (string, error) {
	// 1. 生成批次号
	batchNo := s.generateBatchNo()

	// 2. 查询待转换的合同数据
	contracts, err := s.getContractsForTransform(approvalCode)
	if err != nil {
		return "", fmt.Errorf("查询待转换合同失败: %w", err)
	}

	// 3. 创建批次记录
	batch := &insbuy.InsContractTransformBatch{
		BatchNo:       batchNo,
		BatchName:     fmt.Sprintf("批量转换-%s", time.Now().Format("********-150405")),
		ApprovalCode:  approvalCode,
		OperationType: "transform",
		TotalCount:    len(contracts),
		Status:        "processing",
		StartTime:     &time.Time{},
		OperatorId:    operatorId,
		OperatorName:  operatorName,
	}
	*batch.StartTime = time.Now()

	if err := global.GVA_DB.Create(batch).Error; err != nil {
		return "", fmt.Errorf("创建批次记录失败: %w", err)
	}

	// 4. 异步处理转换任务
	go s.processBatchTransform(batch, contracts)

	return batchNo, nil
}

// BatchAuditRecords 批量审核记录
func (s *ContractTransformService) BatchAuditRecords(stagingIds []uint, auditStatus string, auditorId uint, auditNote string) error {
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		now := time.Now()

		// 批量更新审核状态
		err := tx.Model(&insbuy.InsContractTransformStaging{}).
			Where("id IN ?", stagingIds).
			Updates(map[string]interface{}{
				"audit_status": auditStatus,
				"auditor_id":   auditorId,
				"audit_time":   now,
				"audit_note":   auditNote,
				"updated_at":   now,
			}).Error

		if err != nil {
			return err
		}

		// 记录审核日志
		for _, stagingId := range stagingIds {
			log := &insbuy.InsContractTransformLog{
				StagingId:       stagingId,
				OperationType:   "audit",
				OperationStatus: "success",
				OperatorId:      &auditorId,
				OperatorName:    "审核员", // 可以从用户表查询
			}
			if err := tx.Create(log).Error; err != nil {
				global.GVA_LOG.Error("创建审核日志失败", zap.Error(err))
			}
		}

		return nil
	})
}

// StoreToSourceStorage 存储到SourceStorageData（与现有Excel导入保持一致）
func (s *ContractTransformService) StoreToSourceStorage(stagingRecords []*insbuy.InsContractTransformStaging) error {
	// 1. 转换为RegionalExpenseDetailItem格式
	var items []RegionalExpenseDetailItem
	for _, staging := range stagingRecords {
		if staging.AuditStatus != "approved" {
			continue // 只处理审核通过的记录
		}

		item := s.ToRegionalExpenseDetailItem(staging)
		items = append(items, item)
	}

	if len(items) == 0 {
		return fmt.Errorf("没有审核通过的记录可以存储")
	}

	// 2. 使用现有的存储机制
	// 创建RegionalExpenseDetailImport实例并调用其存储方法
	importService := &RegionalExpenseDetailImport{
		Data: items,
	}

	// 调用保存到数据库的方法
	err := importService.saveToDatabase(context.Background(), 0) // 使用总部ID
	if err != nil {
		return fmt.Errorf("存储到SourceStorageData失败: %w", err)
	}

	// 3. 更新staging记录的存储状态
	for _, staging := range stagingRecords {
		if staging.AuditStatus == "approved" {
			staging.StorageStatus = "stored"
			staging.StorageTime = &time.Time{}
			*staging.StorageTime = time.Now()

			// 更新数据库
			if err := global.GVA_DB.Save(staging).Error; err != nil {
				global.GVA_LOG.Error("更新存储状态失败", zap.Error(err), zap.Uint("staging_id", staging.ID))
			}
		}
	}

	return nil
}

// 辅助方法

// getContractById 根据ID查询合同
func (s *ContractTransformService) getContractById(contractId uint) (*insbuy.InsContract, error) {
	var contract insbuy.InsContract
	err := global.GVA_DB.Where("id = ?", contractId).First(&contract).Error
	if err != nil {
		return nil, err
	}
	return &contract, nil
}

// parseFormData 解析表单数据
func (s *ContractTransformService) parseFormData(formJson interface{}) (map[string]interface{}, error) {
	var formData map[string]interface{}

	// 如果是字符串，先解析JSON
	if jsonStr, ok := formJson.(string); ok {
		if err := json.Unmarshal([]byte(jsonStr), &formData); err != nil {
			return nil, err
		}
	} else if data, ok := formJson.(map[string]interface{}); ok {
		formData = data
	} else {
		// 尝试JSON序列化再反序列化
		jsonBytes, err := json.Marshal(formJson)
		if err != nil {
			return nil, err
		}
		if err := json.Unmarshal(jsonBytes, &formData); err != nil {
			return nil, err
		}
	}

	return formData, nil
}

// extractExpenseDetails 提取费用明细数据
func (s *ContractTransformService) extractExpenseDetails(formData map[string]interface{}) []map[string]interface{} {
	var details []map[string]interface{}

	// 查找费用明细相关的字段
	if expenseDetails, ok := formData["widget_expense_details"]; ok {
		if detailsArray, ok := expenseDetails.([]interface{}); ok {
			for _, detail := range detailsArray {
				if detailMap, ok := detail.(map[string]interface{}); ok {
					details = append(details, detailMap)
				}
			}
		}
	}

	return details
}

// getFormValue 获取表单字段值
func (s *ContractTransformService) getFormValue(formData map[string]interface{}, fieldName string) string {
	if value, ok := formData[fieldName]; ok {
		if str, ok := value.(string); ok {
			return str
		}
		// 尝试转换其他类型为字符串
		return fmt.Sprintf("%v", value)
	}
	return ""
}

// copyBaseInfo 复制基础信息
func (s *ContractTransformService) copyBaseInfo(target, source *insbuy.InsContractTransformStaging) {
	target.Title = source.Title
	target.InitiatorName = source.InitiatorName
	target.CompletionTime = source.CompletionTime
	target.ReportMonth = source.ReportMonth
	target.PaymentEntity = source.PaymentEntity
	target.PaymentReason = source.PaymentReason
	target.BusinessType = source.BusinessType
	target.ReportEntity = source.ReportEntity
	target.ReportEntityDetail = source.ReportEntityDetail
	target.ReportRegion = source.ReportRegion
	target.InitiatorDepartment = source.InitiatorDepartment
	target.Department = source.Department
}

// mapDetailFields 映射明细字段
func (s *ContractTransformService) mapDetailFields(staging *insbuy.InsContractTransformStaging, detail map[string]interface{}) {
	// 费用类型
	staging.ExpenseType = s.getFormValue(detail, "expense_type")

	// 费用地点
	staging.ExpenseLocation = s.getFormValue(detail, "location")

	// 费用日期区间
	staging.ExpenseDateRange = s.getFormValue(detail, "date_range")

	// 发票类型
	staging.InvoiceType = s.getFormValue(detail, "vat_invoice_type")

	// 明细金额
	staging.CurrentRequestAmount = s.getFormValue(detail, "amount")
}

// calculateDerivedFields 计算衍生字段
func (s *ContractTransformService) calculateDerivedFields(staging *insbuy.InsContractTransformStaging) {
	// 计算待付款金额
	contractAmount, _ := strconv.ParseFloat(staging.ContractAmount, 64)
	paidAmount, _ := strconv.ParseFloat(staging.ContractPaidAmount, 64)
	staging.PendingAmount = fmt.Sprintf("%.2f", contractAmount-paidAmount)

	// 设置管报确认金额（默认等于本次请款金额）
	if staging.ReportConfirmAmount == "" {
		staging.ReportConfirmAmount = staging.CurrentRequestAmount
	}

	// 计算不含税金额
	if staging.TaxRate != "" && staging.CurrentRequestAmount != "" {
		taxRate, _ := strconv.ParseFloat(staging.TaxRate, 64)
		amount, _ := strconv.ParseFloat(staging.CurrentRequestAmount, 64)
		if taxRate > 0 {
			excludeTaxAmount := amount / (1 + taxRate/100)
			staging.AmountExcludeTax = fmt.Sprintf("%.2f", excludeTaxAmount)
		} else {
			staging.AmountExcludeTax = staging.CurrentRequestAmount
		}
	}
}

// generateBatchNo 生成批次号
func (s *ContractTransformService) generateBatchNo() string {
	return fmt.Sprintf("CT%s%04d", time.Now().Format("********150405"), time.Now().Nanosecond()%10000)
}

// getContractsForTransform 获取待转换的合同列表
func (s *ContractTransformService) getContractsForTransform(approvalCode string) ([]*insbuy.InsContract, error) {
	var contracts []*insbuy.InsContract
	query := global.GVA_DB.Model(&insbuy.InsContract{})

	if approvalCode != "" {
		query = query.Where("approval_code = ?", approvalCode)
	}

	// 排除已经转换过的合同
	query = query.Where("id NOT IN (SELECT source_contract_id FROM ins_contract_transform_staging)")

	err := query.Find(&contracts).Error
	return contracts, err
}

// processBatchTransform 处理批量转换（异步执行）
func (s *ContractTransformService) processBatchTransform(batch *insbuy.InsContractTransformBatch, contracts []*insbuy.InsContract) {
	defer func() {
		if r := recover(); r != nil {
			global.GVA_LOG.Error("批量转换异常", zap.Any("error", r), zap.String("batch_no", batch.BatchNo))
			// 更新批次状态为失败
			batch.Status = "failed"
			batch.EndTime = &time.Time{}
			*batch.EndTime = time.Now()
			global.GVA_DB.Save(batch)
		}
	}()

	successCount := 0
	failedCount := 0
	detailRecordsCreated := 0

	for _, contract := range contracts {
		result, err := s.TransformContract(contract.ID)
		if err != nil {
			failedCount++
			s.logTransformError(batch.BatchNo, contract.ID, err.Error())
			continue
		}

		if !result.Success {
			failedCount++
			s.logTransformError(batch.BatchNo, contract.ID, strings.Join(result.Errors, "; "))
			continue
		}

		// 保存转换结果
		if result.MainRecord != nil {
			if err := global.GVA_DB.Create(result.MainRecord).Error; err != nil {
				failedCount++
				s.logTransformError(batch.BatchNo, contract.ID, err.Error())
				continue
			}
		}

		for _, detail := range result.DetailRecords {
			if err := global.GVA_DB.Create(detail).Error; err != nil {
				global.GVA_LOG.Error("保存明细记录失败", zap.Error(err))
			} else {
				detailRecordsCreated++
			}
		}

		successCount++
		batch.ProcessedCount++

		// 定期更新进度
		if batch.ProcessedCount%10 == 0 {
			global.GVA_DB.Save(batch)
		}
	}

	// 更新最终状态
	batch.SuccessCount = successCount
	batch.FailedCount = failedCount
	batch.DetailRecordsCreated = detailRecordsCreated
	batch.ProcessedCount = len(contracts)
	batch.Status = "completed"
	batch.EndTime = &time.Time{}
	*batch.EndTime = time.Now()

	global.GVA_DB.Save(batch)
}

// logTransformError 记录转换错误
func (s *ContractTransformService) logTransformError(batchNo string, contractId uint, errorMsg string) {
	log := &insbuy.InsContractTransformLog{
		BatchNo:          batchNo,
		SourceContractId: contractId,
		OperationType:    "transform",
		OperationStatus:  "failed",
		ErrorMessage:     errorMsg,
	}
	global.GVA_DB.Create(log)
}
