package insfinance

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	"go.uber.org/zap"
)

// 飞书合同审批相关数据结构

// FeishuContractResponse 飞书API响应结构
type FeishuContractResponse struct {
	Code int             `json:"code"`
	Data ContractDetails `json:"data"`
	Msg  string          `json:"msg"`
}

// ContractDetails 合同详情
type ContractDetails struct {
	ApprovalCode string             `json:"approval_code"` // 审批定义Code
	ApprovalName string             `json:"approval_name"` // 审批定义名称
	CommentList  []ContractComment  `json:"comment_list"`  // 评论列表
	DepartmentId string             `json:"department_id"` // 发起审批的部门
	EndTime      string             `json:"end_time"`      // 审批结束时间
	Form         string             `json:"form"`          // 审批表单，JSON字符串
	InstanceCode string             `json:"instance_code"` // 审批实例Code
	OpenId       string             `json:"open_id"`       // 发起人OpenId
	Reverted     bool               `json:"reverted"`      // 审批是否被撤销
	SerialNumber string             `json:"serial_number"` // 审批序列号
	StartTime    string             `json:"start_time"`    // 审批开始时间
	Status       string             `json:"status"`        // 审批状态
	TaskList     []ContractTask     `json:"task_list"`     // 审批任务列表
	Timeline     []ContractTimeline `json:"timeline"`      // 审批时间线
	UserId       string             `json:"user_id"`       // 发起人UserId
	Uuid         string             `json:"uuid"`          // 审批实例UUID
}

// ContractComment 合同评论
type ContractComment struct {
	Comment    string         `json:"comment"`         // 评论内容
	CreateTime string         `json:"create_time"`     // 评论时间
	Id         string         `json:"id"`              // 评论ID
	OpenId     string         `json:"open_id"`         // 评论人OpenId
	UserId     string         `json:"user_id"`         // 评论人UserId
	Files      []ContractFile `json:"files,omitempty"` // 附件列表
}

// ContractFile 合同附件
type ContractFile struct {
	FileSize int    `json:"file_size"` // 文件大小
	Title    string `json:"title"`     // 文件标题
	Type     string `json:"type"`      // 文件类型
	Url      string `json:"url"`       // 文件URL
}

// ContractTask 合同审批任务
type ContractTask struct {
	EndTime   string `json:"end_time"`   // 任务结束时间
	Id        string `json:"id"`         // 任务ID
	NodeId    string `json:"node_id"`    // 节点ID
	NodeName  string `json:"node_name"`  // 节点名称
	OpenId    string `json:"open_id"`    // 处理人OpenId
	StartTime string `json:"start_time"` // 任务开始时间
	Status    string `json:"status"`     // 任务状态
	Type      string `json:"type"`       // 任务类型
	UserId    string `json:"user_id"`    // 处理人UserId
}

// ContractTimeline 合同审批时间线
type ContractTimeline struct {
	CreateTime string           `json:"create_time"`            // 创建时间
	Ext        string           `json:"ext"`                    // 扩展信息
	NodeKey    string           `json:"node_key"`               // 节点Key
	OpenId     string           `json:"open_id"`                // 操作人OpenId
	Type       string           `json:"type"`                   // 操作类型
	UserId     string           `json:"user_id,omitempty"`      // 操作人UserId
	CcUserList []ContractCcUser `json:"cc_user_list,omitempty"` // 抄送人列表
	OpenIdList []string         `json:"open_id_list,omitempty"` // OpenId列表
	UserIdList []string         `json:"user_id_list,omitempty"` // UserId列表
	TaskId     string           `json:"task_id,omitempty"`      // 任务ID
}

// ContractCcUser 合同抄送人
type ContractCcUser struct {
	CcId   string `json:"cc_id"`   // 抄送ID
	OpenId string `json:"open_id"` // 抄送人OpenId
	UserId string `json:"user_id"` // 抄送人UserId
}

// ContractFormField 合同表单字段
type ContractFormField struct {
	Id     string      `json:"id"`    // 字段ID
	Name   string      `json:"name"`  // 字段名称
	Type   string      `json:"type"`  // 字段类型
	Ext    interface{} `json:"ext"`   // 扩展信息
	Value  interface{} `json:"value"` // 字段值
	Option struct {
		Key  string `json:"key"`  // 选项键
		Text string `json:"text"` // 选项文本
	} `json:"option,omitempty"` // 选项信息
	TimezoneOffset int `json:"timezoneOffset,omitempty"` // 时区偏移
}

// FeishuContractListResponse 飞书合同列表API响应结构
type FeishuContractListResponse struct {
	Code int              `json:"code"`
	Data ContractListData `json:"data"`
	Msg  string           `json:"msg"`
}

// ContractListData 合同列表数据
type ContractListData struct {
	HasMore          bool     `json:"has_more"`           // 是否还有更多数据
	InstanceCodeList []string `json:"instance_code_list"` // 实例代码列表
	PageToken        string   `json:"page_token"`         // 分页标记
}

// ===== 新增的请求和响应结构体 =====

// FeishuContractService 飞书合同服务
type FeishuContractService struct {
	client *lark.Client
}

// NewFeishuContractService 创建飞书合同服务实例
func NewFeishuContractService() *FeishuContractService {
	app := global.GVA_CONFIG.FeishuApp
	client := lark.NewClient(app.Appid, app.AppSecret)

	return &FeishuContractService{
		client: client,
	}
}

// ContractListRequest 合同列表请求参数
type ContractListRequest struct {
	ApprovalCode string    `json:"approval_code" validate:"required"` // 审批定义Code，必填
	StartTime    time.Time `json:"start_time"`                        // 开始时间
	EndTime      time.Time `json:"end_time"`                          // 结束时间
	PageSize     int       `json:"page_size"`                         // 页面大小，默认100，最大200
	PageToken    string    `json:"page_token"`                        // 分页标记
}

// ContractListResponse 合同列表响应
type ContractListResponse struct {
	Success          bool     `json:"success"`            // 请求是否成功
	HasMore          bool     `json:"has_more"`           // 是否还有更多数据
	InstanceCodeList []string `json:"instance_code_list"` // 实例代码列表
	PageToken        string   `json:"page_token"`         // 下一页分页标记
	RequestId        string   `json:"request_id"`         // 请求ID
	Total            int      `json:"total"`              // 当前页返回的记录数
	ErrorMsg         string   `json:"error_msg"`          // 错误信息
}

// ContractDetailRequest 合同详情请求参数
type ContractDetailRequest struct {
	InstanceCodes []string `json:"instance_codes" validate:"required,min=1"` // 实例代码列表，必填，至少一个
}

// ContractDetailResponse 合同详情响应
type ContractDetailResponse struct {
	Success     bool              `json:"success"`      // 请求是否成功
	Contracts   []ContractDetails `json:"contracts"`    // 合同详情列表
	RequestId   string            `json:"request_id"`   // 请求ID
	Total       int               `json:"total"`        // 成功获取的合同数量
	FailedCount int               `json:"failed_count"` // 失败的合同数量
	ErrorMsg    string            `json:"error_msg"`    // 错误信息
	Errors      []ContractError   `json:"errors"`       // 详细错误信息
}

// ContractError 合同错误信息
type ContractError struct {
	InstanceCode string `json:"instance_code"` // 实例代码
	ErrorMsg     string `json:"error_msg"`     // 错误信息
}

// ===== 新的结构化方法 =====

// GetContractList 获取合同列表（新的结构化接口）
func (s *FeishuContractService) GetContractList(ctx context.Context, req ContractListRequest) (*ContractListResponse, error) {
	global.GVA_LOG.Info("开始获取飞书合同列表",
		zap.String("approval_code", req.ApprovalCode),
		zap.Time("start_time", req.StartTime),
		zap.Time("end_time", req.EndTime),
		zap.Int("page_size", req.PageSize),
	)

	// 参数验证和默认值设置
	if err := s.validateListRequest(&req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 构建飞书API请求
	builder := larkapproval.NewListInstanceReqBuilder().
		ApprovalCode(req.ApprovalCode).
		PageSize(req.PageSize)

	// 设置时间范围（转换为毫秒时间戳）
	if !req.StartTime.IsZero() {
		startTimeMs := req.StartTime.UnixMilli()
		builder = builder.StartTime(strconv.FormatInt(startTimeMs, 10))
	}
	if !req.EndTime.IsZero() {
		endTimeMs := req.EndTime.UnixMilli()
		builder = builder.EndTime(strconv.FormatInt(endTimeMs, 10))
	}

	// 设置分页标记
	if req.PageToken != "" {
		builder = builder.PageToken(req.PageToken)
	}

	// 发起请求
	resp, err := s.client.Approval.V4.Instance.List(ctx, builder.Build())
	if err != nil {
		global.GVA_LOG.Error("调用飞书API失败", zap.Error(err))
		return &ContractListResponse{
			Success:  false,
			ErrorMsg: fmt.Sprintf("调用飞书API失败: %v", err),
		}, err
	}

	// 构建响应
	response := &ContractListResponse{
		Success:   resp.Success(),
		RequestId: resp.RequestId(),
	}

	if !resp.Success() {
		response.ErrorMsg = fmt.Sprintf("飞书API返回错误: %s", larkcore.Prettify(resp.CodeError))
		global.GVA_LOG.Error("飞书API响应错误",
			zap.String("request_id", response.RequestId),
			zap.String("error", response.ErrorMsg),
		)
		return response, fmt.Errorf(response.ErrorMsg)
	}

	// 解析成功响应
	if resp.Data != nil {
		response.HasMore = *resp.Data.HasMore
		response.InstanceCodeList = resp.Data.InstanceCodeList
		response.PageToken = *resp.Data.PageToken
		response.Total = len(resp.Data.InstanceCodeList)
	}

	global.GVA_LOG.Info("成功获取飞书合同列表",
		zap.String("request_id", response.RequestId),
		zap.Int("total", response.Total),
		zap.Bool("has_more", response.HasMore),
	)

	return response, nil
}

// GetContractDetails 获取合同详情（新的结构化接口）
func (s *FeishuContractService) GetContractDetails(ctx context.Context, req ContractDetailRequest) (*ContractDetailResponse, error) {
	global.GVA_LOG.Info("开始获取飞书合同详情",
		zap.Strings("instance_codes", req.InstanceCodes),
		zap.Int("count", len(req.InstanceCodes)),
	)

	// 参数验证
	if err := s.validateDetailRequest(&req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	response := &ContractDetailResponse{
		Success:   true,
		Contracts: make([]ContractDetails, 0, len(req.InstanceCodes)),
		Errors:    make([]ContractError, 0),
	}

	// 批量获取合同详情
	for _, instanceCode := range req.InstanceCodes {
		contract, err := s.getSingleContractDetail(ctx, instanceCode)
		if err != nil {
			response.FailedCount++
			response.Errors = append(response.Errors, ContractError{
				InstanceCode: instanceCode,
				ErrorMsg:     err.Error(),
			})
			global.GVA_LOG.Error("获取单个合同详情失败",
				zap.String("instance_code", instanceCode),
				zap.Error(err),
			)
			continue
		}

		response.Contracts = append(response.Contracts, *contract)
		response.Total++

		// 添加延迟避免API限流
		time.Sleep(100 * time.Millisecond)
	}

	// 设置整体状态
	if response.FailedCount > 0 && response.Total == 0 {
		response.Success = false
		response.ErrorMsg = "所有合同详情获取失败"
	} else if response.FailedCount > 0 {
		response.ErrorMsg = fmt.Sprintf("部分合同详情获取失败，成功: %d，失败: %d", response.Total, response.FailedCount)
	}

	global.GVA_LOG.Info("批量获取飞书合同详情完成",
		zap.Int("total_success", response.Total),
		zap.Int("total_failed", response.FailedCount),
		zap.Bool("overall_success", response.Success),
	)

	return response, nil
}

// ===== 辅助方法 =====

// validateListRequest 验证合同列表请求参数
func (s *FeishuContractService) validateListRequest(req *ContractListRequest) error {
	if req.ApprovalCode == "" {
		return fmt.Errorf("审批定义Code不能为空")
	}

	// 设置默认值
	if req.PageSize <= 0 {
		req.PageSize = 100 // 默认页面大小
	}
	if req.PageSize > 200 {
		req.PageSize = 200 // 飞书API限制最大200
	}

	// 设置默认时间范围（如果未指定）
	if req.StartTime.IsZero() && req.EndTime.IsZero() {
		// 默认查询最近30天的数据
		req.EndTime = time.Now()
		req.StartTime = req.EndTime.AddDate(0, 0, -30)
	}

	return nil
}

// validateDetailRequest 验证合同详情请求参数
func (s *FeishuContractService) validateDetailRequest(req *ContractDetailRequest) error {
	if len(req.InstanceCodes) == 0 {
		return fmt.Errorf("实例代码列表不能为空")
	}

	// 限制批量查询的数量，避免API限流
	if len(req.InstanceCodes) > 100 {
		return fmt.Errorf("单次查询的实例代码数量不能超过100个")
	}

	// 验证实例代码格式
	for _, code := range req.InstanceCodes {
		if code == "" {
			return fmt.Errorf("实例代码不能为空")
		}
	}

	return nil
}

// getSingleContractDetail 获取单个合同详情
func (s *FeishuContractService) getSingleContractDetail(ctx context.Context, instanceCode string) (*ContractDetails, error) {
	// 构建请求
	req := larkapproval.NewGetInstanceReqBuilder().
		InstanceId(instanceCode).
		Build()

	// 发起请求
	resp, err := s.client.Approval.V4.Instance.Get(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("调用飞书API失败: %w", err)
	}

	// 检查响应状态
	if !resp.Success() {
		return nil, fmt.Errorf("飞书API返回错误: %s", larkcore.Prettify(resp.CodeError))
	}

	// 转换响应数据
	data := resp.Data
	if data == nil {
		return nil, fmt.Errorf("飞书API返回空数据")
	}

	contract := &ContractDetails{
		ApprovalCode: s.safeString(data.ApprovalCode),
		ApprovalName: s.safeString(data.ApprovalName),
		DepartmentId: s.safeString(data.DepartmentId),
		EndTime:      s.safeString(data.EndTime),
		Form:         s.safeString(data.Form),
		InstanceCode: s.safeString(data.InstanceCode),
		OpenId:       s.safeString(data.OpenId),
		Reverted:     s.safeBool(data.Reverted),
		SerialNumber: s.safeString(data.SerialNumber),
		StartTime:    s.safeString(data.StartTime),
		Status:       s.safeString(data.Status),
		UserId:       s.safeString(data.UserId),
		Uuid:         s.safeString(data.Uuid),
	}

	// 转换评论列表
	if data.CommentList != nil {
		for _, comment := range data.CommentList {
			c := ContractComment{
				Comment:    s.safeString(comment.Comment),
				CreateTime: s.safeString(comment.CreateTime),
				Id:         s.safeString(comment.Id),
				OpenId:     s.safeString(comment.OpenId),
				UserId:     s.safeString(comment.UserId),
			}

			// 转换文件列表
			if comment.Files != nil {
				for _, file := range comment.Files {
					f := ContractFile{
						FileSize: s.safeInt(file.FileSize),
						Title:    s.safeString(file.Title),
						Type:     s.safeString(file.Type),
						Url:      s.safeString(file.Url),
					}
					c.Files = append(c.Files, f)
				}
			}

			contract.CommentList = append(contract.CommentList, c)
		}
	}

	// 转换任务列表
	if data.TaskList != nil {
		for _, task := range data.TaskList {
			t := ContractTask{
				EndTime:   s.safeString(task.EndTime),
				Id:        s.safeString(task.Id),
				NodeId:    s.safeString(task.NodeId),
				NodeName:  s.safeString(task.NodeName),
				OpenId:    s.safeString(task.OpenId),
				StartTime: s.safeString(task.StartTime),
				Status:    s.safeString(task.Status),
				Type:      s.safeString(task.Type),
				UserId:    s.safeString(task.UserId),
			}
			contract.TaskList = append(contract.TaskList, t)
		}
	}

	// 转换时间线
	if data.Timeline != nil {
		for _, timeline := range data.Timeline {
			t := ContractTimeline{
				CreateTime: s.safeString(timeline.CreateTime),
				Ext:        s.safeString(timeline.Ext),
				NodeKey:    s.safeString(timeline.NodeKey),
				OpenId:     s.safeString(timeline.OpenId),
				Type:       s.safeString(timeline.Type),
			}

			if timeline.UserId != nil {
				t.UserId = *timeline.UserId
			}
			if timeline.TaskId != nil {
				t.TaskId = *timeline.TaskId
			}

			// 转换抄送用户列表
			if timeline.CcUserList != nil {
				for _, ccUser := range timeline.CcUserList {
					cc := ContractCcUser{
						CcId:   s.safeString(ccUser.CcId),
						OpenId: s.safeString(ccUser.OpenId),
						UserId: s.safeString(ccUser.UserId),
					}
					t.CcUserList = append(t.CcUserList, cc)
				}
			}

			// 转换ID列表
			if timeline.OpenIdList != nil {
				t.OpenIdList = timeline.OpenIdList
			}
			if timeline.UserIdList != nil {
				t.UserIdList = timeline.UserIdList
			}

			contract.Timeline = append(contract.Timeline, t)
		}
	}

	return contract, nil
}

// ===== 安全转换方法 =====

// safeString 安全地从指针获取字符串值
func (s *FeishuContractService) safeString(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

// safeBool 安全地从指针获取布尔值
func (s *FeishuContractService) safeBool(ptr *bool) bool {
	if ptr == nil {
		return false
	}
	return *ptr
}

// safeInt 安全地从指针获取整数值
func (s *FeishuContractService) safeInt(ptr *int) int {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// ===== 向后兼容的包装函数 =====

// InstanceCodeList 获取合同实例代码列表（向后兼容）
// 保留原有函数签名，内部调用新的结构化接口
func InstanceCodeList() {
	global.GVA_LOG.Info("调用向后兼容的InstanceCodeList函数")

	// 创建服务实例
	service := NewFeishuContractService()

	// 使用默认参数调用新接口
	req := ContractListRequest{
		ApprovalCode: "7C468A54-8745-2245-9675-08002B2077BF", // 默认审批代码，应该从配置获取
		PageSize:     100,
	}

	ctx := context.Background()
	resp, err := service.GetContractList(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("获取合同列表失败", zap.Error(err))
		return
	}

	// 输出结果（保持原有行为）
	global.GVA_LOG.Info("获取合同列表成功",
		zap.Int("total", resp.Total),
		zap.Bool("has_more", resp.HasMore),
		zap.Strings("instance_codes", resp.InstanceCodeList),
	)
}

// InstancesDetails 获取合同详情（向后兼容）
// 保留原有函数签名，内部调用新的结构化接口
func InstancesDetails() {
	global.GVA_LOG.Info("调用向后兼容的InstancesDetails函数")

	// 首先获取合同列表
	service := NewFeishuContractService()

	listReq := ContractListRequest{
		ApprovalCode: "7C468A54-8745-2245-9675-08002B2077BF", // 默认审批代码，应该从配置获取
		PageSize:     10,                                     // 限制数量避免过多调用
	}

	ctx := context.Background()
	listResp, err := service.GetContractList(ctx, listReq)
	if err != nil {
		global.GVA_LOG.Error("获取合同列表失败", zap.Error(err))
		return
	}

	if len(listResp.InstanceCodeList) == 0 {
		global.GVA_LOG.Info("没有找到合同数据")
		return
	}

	// 获取合同详情
	detailReq := ContractDetailRequest{
		InstanceCodes: listResp.InstanceCodeList,
	}

	detailResp, err := service.GetContractDetails(ctx, detailReq)
	if err != nil {
		global.GVA_LOG.Error("获取合同详情失败", zap.Error(err))
		return
	}

	// 输出结果（保持原有行为）
	global.GVA_LOG.Info("获取合同详情成功",
		zap.Int("total_success", detailResp.Total),
		zap.Int("total_failed", detailResp.FailedCount),
	)

	for _, contract := range detailResp.Contracts {
		global.GVA_LOG.Info("合同详情",
			zap.String("instance_code", contract.InstanceCode),
			zap.String("approval_name", contract.ApprovalName),
			zap.String("status", contract.Status),
		)
	}
}

// ===== 便捷方法 =====

// GetContractListWithDefaults 使用默认参数获取合同列表
func GetContractListWithDefaults(approvalCode string) (*ContractListResponse, error) {
	service := NewFeishuContractService()

	req := ContractListRequest{
		ApprovalCode: approvalCode,
		PageSize:     100,
		StartTime:    time.Now().AddDate(0, 0, -30), // 最近30天
		EndTime:      time.Now(),
	}

	return service.GetContractList(context.Background(), req)
}

// GetAllContractPages 获取所有分页的合同列表
func GetAllContractPages(approvalCode string, startTime, endTime time.Time) ([]string, error) {
	service := NewFeishuContractService()

	var allInstanceCodes []string
	pageToken := ""

	for {
		req := ContractListRequest{
			ApprovalCode: approvalCode,
			StartTime:    startTime,
			EndTime:      endTime,
			PageSize:     200, // 使用最大页面大小
			PageToken:    pageToken,
		}

		resp, err := service.GetContractList(context.Background(), req)
		if err != nil {
			return nil, err
		}

		allInstanceCodes = append(allInstanceCodes, resp.InstanceCodeList...)

		if !resp.HasMore {
			break
		}

		pageToken = resp.PageToken

		// 添加延迟避免API限流
		time.Sleep(200 * time.Millisecond)
	}

	return allInstanceCodes, nil
}

// GetContractDetailsBatch 批量获取合同详情（自动分批处理）
func GetContractDetailsBatch(instanceCodes []string, batchSize int) (*ContractDetailResponse, error) {
	if batchSize <= 0 {
		batchSize = 20 // 默认批次大小
	}

	service := NewFeishuContractService()

	var allContracts []ContractDetails
	var allErrors []ContractError
	totalSuccess := 0
	totalFailed := 0

	// 分批处理
	for i := 0; i < len(instanceCodes); i += batchSize {
		end := i + batchSize
		if end > len(instanceCodes) {
			end = len(instanceCodes)
		}

		batch := instanceCodes[i:end]
		req := ContractDetailRequest{
			InstanceCodes: batch,
		}

		resp, err := service.GetContractDetails(context.Background(), req)
		if err != nil {
			// 记录批次错误，但继续处理其他批次
			for _, code := range batch {
				allErrors = append(allErrors, ContractError{
					InstanceCode: code,
					ErrorMsg:     err.Error(),
				})
				totalFailed++
			}
			continue
		}

		allContracts = append(allContracts, resp.Contracts...)
		allErrors = append(allErrors, resp.Errors...)
		totalSuccess += resp.Total
		totalFailed += resp.FailedCount

		// 批次间延迟
		if i+batchSize < len(instanceCodes) {
			time.Sleep(500 * time.Millisecond)
		}
	}

	return &ContractDetailResponse{
		Success:     totalSuccess > 0,
		Contracts:   allContracts,
		Total:       totalSuccess,
		FailedCount: totalFailed,
		Errors:      allErrors,
		ErrorMsg:    fmt.Sprintf("批量处理完成，成功: %d，失败: %d", totalSuccess, totalFailed),
	}, nil
}
