package insreport

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/utils/jtypes"
)

// ValidationError 验证错误结构
type ValidationError struct {
	Type    string `json:"type"`
	Message string `json:"message"`
	Field   string `json:"field"`
}

// SimpleJSONProcessor 简化的JSON格式公式处理器
type SimpleJSONProcessor struct {
	builder *SimpleFormulaBuilder
}

// NewSimpleJSONProcessor 创建简化JSON公式处理器
func NewSimpleJSONProcessor(configs []CostTypeConfigItem) *SimpleJSONProcessor {
	return &SimpleJSONProcessor{
		builder: NewSimpleFormulaBuilder(configs),
	}
}

// ProcessCalculatedCategoriesJSON 处理JSON格式的计算型分类
func (sjp *SimpleJSONProcessor) ProcessCalculatedCategoriesJSON(items []FinancialSummaryItem, configs []CostTypeConfigItem, timeColumns []string) []FinancialSummaryItem {
	// 创建ID到数据的映射
	dataMap := createDataMap(items)

	// 获取计算型配置
	calculatedConfigs := sjp.getCalculatedConfigs(configs)
	if len(calculatedConfigs) == 0 {
		return items
	}

	// 按依赖关系排序计算型配置
	sortedConfigs := sjp.sortConfigsByDependency(calculatedConfigs)

	// 处理每个计算型配置
	for _, config := range sortedConfigs {
		calculatedItem := sjp.executeSimpleFormulaCalculation(config, dataMap, timeColumns)
		if calculatedItem != nil {
			// 设置计算项的基本信息
			calculatedItem.CategoryId = config.Id
			calculatedItem.CategoryName = config.CategoryName
			calculatedItem.CategoryCode = config.CategoryCode
			calculatedItem.Level = config.Level
			calculatedItem.ParentId = config.ParentId
			calculatedItem.SortOrder = config.SortOrder
			calculatedItem.Children = []FinancialSummaryItem{}

			// 将计算结果添加到数据映射中，供后续计算使用
			dataMap[config.Id] = calculatedItem
			// 将计算项插入到适当的位置
			items = insertCalculatedItem(items, calculatedItem, config)
		}
	}

	return items
}

// getCalculatedConfigs 获取计算型配置
func (sjp *SimpleJSONProcessor) getCalculatedConfigs(configs []CostTypeConfigItem) []CostTypeConfigItem {
	var calculatedConfigs []CostTypeConfigItem
	for _, config := range configs {
		if config.IsCalculated && config.CalculationFormula != "" {
			calculatedConfigs = append(calculatedConfigs, config)
		}
	}
	return calculatedConfigs
}

// sortConfigsByDependency 按依赖关系排序配置（拓扑排序）
func (sjp *SimpleJSONProcessor) sortConfigsByDependency(configs []CostTypeConfigItem) []CostTypeConfigItem {
	// 构建依赖关系图
	dependencyMap := make(map[uint][]uint)
	inDegree := make(map[uint]int)
	configMap := make(map[uint]CostTypeConfigItem)

	// 初始化
	for _, config := range configs {
		configMap[config.Id] = config
		inDegree[config.Id] = 0
		dependencies := sjp.extractDependencies(config.CalculationFormula)
		dependencyMap[config.Id] = dependencies
	}

	// 计算入度
	for _, dependencies := range dependencyMap {
		for _, depId := range dependencies {
			if _, exists := inDegree[depId]; exists {
				inDegree[depId]++
			}
		}
	}

	// 拓扑排序
	var result []CostTypeConfigItem
	var queue []uint

	// 找到所有入度为0的节点
	for id, degree := range inDegree {
		if degree == 0 {
			queue = append(queue, id)
		}
	}

	// 处理队列
	for len(queue) > 0 {
		currentId := queue[0]
		queue = queue[1:]

		if config, exists := configMap[currentId]; exists {
			result = append(result, config)
		}

		// 减少依赖此节点的其他节点的入度
		for id, dependencies := range dependencyMap {
			for _, depId := range dependencies {
				if depId == currentId {
					inDegree[id]--
					if inDegree[id] == 0 {
						queue = append(queue, id)
					}
				}
			}
		}
	}

	// 检查是否有循环依赖
	if len(result) != len(configs) {
		fmt.Printf("警告：检测到循环依赖，部分计算型分类可能无法正确计算\n")
		// 将剩余的配置添加到结果中
		for _, config := range configs {
			found := false
			for _, resultConfig := range result {
				if resultConfig.Id == config.Id {
					found = true
					break
				}
			}
			if !found {
				result = append(result, config)
				fmt.Printf("循环依赖的分类: %s (ID: %d)\n", config.CategoryName, config.Id)
			}
		}
	}

	return result
}

// executeSimpleFormulaCalculation 执行简化公式计算
func (sjp *SimpleJSONProcessor) executeSimpleFormulaCalculation(config CostTypeConfigItem, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) *FinancialSummaryItem {
	if config.CalculationFormula == "" {
		fmt.Printf("警告：分类 [%s] 的计算公式为空\n", config.CategoryName)
		return nil
	}

	var expression string
	var dependencies []uint

	// 尝试解析为JSON格式
	formulaJSON, err := sjp.builder.BuildFromJSON(config.CalculationFormula)
	if err != nil {
		// 如果不是JSON格式，直接作为表达式处理
		fmt.Printf("信息：分类 [%s] 使用直接表达式格式: %s\n", config.CategoryName, config.CalculationFormula)
		expression = config.CalculationFormula
		dependencies = sjp.extractDependenciesFromExpression(expression)
	} else {
		// 使用JSON中的表达式
		expression = formulaJSON.Expression
		dependencies = formulaJSON.GetReferencedIDs()
		fmt.Printf("信息：分类 [%s] 使用JSON格式，表达式: %s\n", config.CategoryName, expression)
	}

	// 验证所有依赖的分类数据是否存在
	for _, depId := range dependencies {
		if _, exists := dataMap[depId]; !exists {
			fmt.Printf("错误：分类 [%s] 依赖的分类ID %d 在数据中不存在\n", config.CategoryName, depId)
			return nil
		}
	}

	// 执行计算
	result := sjp.executeDirectFormulaWithValidation(expression, dataMap, timeColumns, config.CategoryName)

	// 验证计算结果的合理性
	if result != nil {
		if err := sjp.validateCalculationResult(result, config.CategoryName); err != nil {
			fmt.Printf("警告：分类 [%s] 计算结果验证失败: %v\n", config.CategoryName, err)
		}
	}

	return result
}

// executeDirectFormula 直接执行表达式
func (sjp *SimpleJSONProcessor) executeDirectFormula(expression string, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) *FinancialSummaryItem {
	return sjp.executeDirectFormulaWithValidation(expression, dataMap, timeColumns, "")
}

// executeDirectFormulaWithValidation 带验证的直接执行表达式
func (sjp *SimpleJSONProcessor) executeDirectFormulaWithValidation(expression string, dataMap map[uint]*FinancialSummaryItem, timeColumns []string, categoryName string) *FinancialSummaryItem {
	// 解析表达式
	expr, err := sjp.parseExpressionSafely(expression)
	if err != nil {
		if categoryName != "" {
			fmt.Printf("错误：分类 [%s] 表达式解析失败 [%s]: %v\n", categoryName, expression, err)
		} else {
			fmt.Printf("错误：表达式解析失败 [%s]: %v\n", expression, err)
		}
		return nil
	}

	// 计算结果
	result, err := sjp.calculateExpressionSafely(expr, dataMap, timeColumns)
	if err != nil {
		if categoryName != "" {
			fmt.Printf("错误：分类 [%s] 表达式计算失败 [%s]: %v\n", categoryName, expression, err)
		} else {
			fmt.Printf("错误：表达式计算失败 [%s]: %v\n", expression, err)
		}
		return nil
	}

	return result
}

// parseExpressionSafely 安全解析表达式
func (sjp *SimpleJSONProcessor) parseExpressionSafely(expression string) (interface{}, error) {
	// 这里需要根据实际的表达式解析器来实现
	// 由于SimpleFormulaProcessor可能不存在，我们使用基础的解析逻辑
	if expression == "" {
		return nil, fmt.Errorf("表达式为空")
	}

	// 简单的表达式验证
	if !sjp.isValidExpression(expression) {
		return nil, fmt.Errorf("表达式格式无效")
	}

	return expression, nil
}

// calculateExpressionSafely 安全计算表达式
func (sjp *SimpleJSONProcessor) calculateExpressionSafely(expr interface{}, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) (*FinancialSummaryItem, error) {
	expression, ok := expr.(string)
	if !ok {
		return nil, fmt.Errorf("表达式类型错误")
	}

	// 基础的表达式计算逻辑
	return sjp.calculateBasicExpression(expression, dataMap, timeColumns)
}

// isValidExpression 验证表达式格式
func (sjp *SimpleJSONProcessor) isValidExpression(expression string) bool {
	hasValidReferences := false

	// 1. 检查并验证 [分类名称] 格式的引用
	namePattern := regexp.MustCompile(`\[([^\]]+)\]`)
	nameMatches := namePattern.FindAllStringSubmatch(expression, -1)

	for _, match := range nameMatches {
		if len(match) >= 2 {
			hasValidReferences = true
			categoryName := match[1]
			found := false
			for _, config := range sjp.builder.configs {
				if config.CategoryName == categoryName {
					found = true
					break
				}
			}
			if !found {
				fmt.Printf("警告：表达式中引用的分类名称不存在: [%s]\n", categoryName)
				return false
			}
		}
	}

	// 2. 检查并验证 #数字 格式的ID引用
	idPattern := regexp.MustCompile(`#(\d+)`)
	idMatches := idPattern.FindAllStringSubmatch(expression, -1)

	for _, match := range idMatches {
		if len(match) >= 2 {
			hasValidReferences = true
			if id, err := strconv.ParseUint(match[1], 10, 32); err == nil {
				categoryId := uint(id)
				found := false
				for _, config := range sjp.builder.configs {
					if config.Id == categoryId {
						found = true
						break
					}
				}
				if !found {
					fmt.Printf("警告：表达式中引用的分类ID不存在: #%d\n", categoryId)
					return false
				}
			} else {
				fmt.Printf("警告：表达式中ID格式无效: #%s\n", match[1])
				return false
			}
		}
	}

	// 表达式必须至少包含一个有效的引用
	if !hasValidReferences {
		fmt.Printf("警告：表达式中没有找到有效的分类引用\n")
		return false
	}

	return true
}

// calculateBasicExpression 计算基础表达式
func (sjp *SimpleJSONProcessor) calculateBasicExpression(expression string, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) (*FinancialSummaryItem, error) {
	// 简化的计算逻辑，支持基本的二元运算
	expression = strings.TrimSpace(expression)
	fmt.Printf("调试：计算基础表达式: '%s'\n", expression)

	// 处理百分比表达式
	if strings.Contains(expression, "%") {
		fmt.Printf("调试：检测到百分比表达式\n")
		return sjp.calculatePercentageExpression(expression, dataMap, timeColumns)
	}

	// 处理二元运算表达式
	fmt.Printf("调试：处理二元运算表达式\n")
	return sjp.calculateBinaryExpression(expression, dataMap, timeColumns)
}

// validateCalculationResult 验证计算结果
func (sjp *SimpleJSONProcessor) validateCalculationResult(result *FinancialSummaryItem, categoryName string) error {
	if result == nil {
		return fmt.Errorf("计算结果为空")
	}

	// 检查总计是否与月度数据一致
	var monthlySum jtypes.JPrice
	for _, amount := range result.MonthlyAmounts {
		monthlySum += amount
	}

	// 允许小的精度差异
	diff := result.TotalAmount - monthlySum
	if diff < 0 {
		diff = -diff
	}

	if diff > 1 { // 允许1分的差异
		return fmt.Errorf("总计 %.2f 与月度数据总和 %.2f 不一致，差异: %.2f",
			float64(result.TotalAmount)/100, float64(monthlySum)/100, float64(diff)/100)
	}

	// 检查是否有异常大的数值（超过1万亿）
	maxValue := jtypes.JPrice(100000000000000) // 1万亿分
	if result.TotalAmount > maxValue || result.TotalAmount < -maxValue {
		return fmt.Errorf("总计数值异常大: %.2f", float64(result.TotalAmount)/100)
	}

	for month, amount := range result.MonthlyAmounts {
		if amount > maxValue || amount < -maxValue {
			return fmt.Errorf("月度数值异常大 [%s]: %.2f", month, float64(amount)/100)
		}
	}

	return nil
}

// calculatePercentageExpression 计算百分比表达式
func (sjp *SimpleJSONProcessor) calculatePercentageExpression(expression string, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) (*FinancialSummaryItem, error) {
	// 移除百分号并解析
	expr := strings.Replace(expression, "%", "", -1)
	expr = strings.TrimSpace(expr)

	// 查找除法运算符
	parts := strings.Split(expr, "/")
	if len(parts) != 2 {
		return nil, fmt.Errorf("百分比表达式格式错误，应为：分子 / 分母 %%")
	}

	// 计算分子
	numerator, err := sjp.calculateSingleOperand(strings.TrimSpace(parts[0]), dataMap, timeColumns)
	if err != nil {
		return nil, fmt.Errorf("分子计算失败: %v", err)
	}

	// 计算分母
	denominator, err := sjp.calculateSingleOperand(strings.TrimSpace(parts[1]), dataMap, timeColumns)
	if err != nil {
		return nil, fmt.Errorf("分母计算失败: %v", err)
	}

	// 计算百分比
	result := &FinancialSummaryItem{
		MonthlyAmounts: make(map[string]jtypes.JPrice),
	}

	// 计算每个月的百分比
	for _, month := range timeColumns {
		numeratorAmount := numerator.MonthlyAmounts[month]
		denominatorAmount := denominator.MonthlyAmounts[month]

		if denominatorAmount != 0 {
			result.MonthlyAmounts[month] = (numeratorAmount * 10000) / denominatorAmount // 乘以100转换为百分比，再乘以100保持精度
		} else {
			result.MonthlyAmounts[month] = 0
		}
	}

	// 计算总计百分比
	if denominator.TotalAmount != 0 {
		result.TotalAmount = (numerator.TotalAmount * 10000) / denominator.TotalAmount
	} else {
		result.TotalAmount = 0
	}

	return result, nil
}

// calculateBinaryExpression 计算二元运算表达式
// 修复版本：正确处理运算符优先级和括号
func (sjp *SimpleJSONProcessor) calculateBinaryExpression(expression string, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) (*FinancialSummaryItem, error) {
	expression = strings.TrimSpace(expression)

	// 首先尝试找到最低优先级的运算符（从右到左扫描）
	// 优先级：+ - (低) > * / (高)

	// 1. 先处理加减法（优先级最低）
	if pos := sjp.findOperatorOutsideParentheses(expression, []string{"+", "-"}); pos != -1 {
		op := string(expression[pos])
		left := strings.TrimSpace(expression[:pos])
		right := strings.TrimSpace(expression[pos+1:])

		// 计算左操作数
		leftResult, err := sjp.calculateBasicExpression(left, dataMap, timeColumns)
		if err != nil {
			return nil, fmt.Errorf("左操作数计算失败: %v", err)
		}

		// 计算右操作数
		rightResult, err := sjp.calculateBasicExpression(right, dataMap, timeColumns)
		if err != nil {
			return nil, fmt.Errorf("右操作数计算失败: %v", err)
		}

		// 执行运算
		return sjp.performBinaryOperation(leftResult, rightResult, op, timeColumns)
	}

	// 2. 再处理乘除法（优先级较高）
	if pos := sjp.findOperatorOutsideParentheses(expression, []string{"*", "/"}); pos != -1 {
		op := string(expression[pos])
		left := strings.TrimSpace(expression[:pos])
		right := strings.TrimSpace(expression[pos+1:])

		// 计算左操作数
		leftResult, err := sjp.calculateBasicExpression(left, dataMap, timeColumns)
		if err != nil {
			return nil, fmt.Errorf("左操作数计算失败: %v", err)
		}

		// 计算右操作数
		rightResult, err := sjp.calculateBasicExpression(right, dataMap, timeColumns)
		if err != nil {
			return nil, fmt.Errorf("右操作数计算失败: %v", err)
		}

		// 执行运算
		return sjp.performBinaryOperation(leftResult, rightResult, op, timeColumns)
	}

	// 如果没有运算符，作为单个操作数处理
	return sjp.calculateSingleOperand(expression, dataMap, timeColumns)
}

// calculateSingleOperand 计算单个操作数
func (sjp *SimpleJSONProcessor) calculateSingleOperand(operand string, dataMap map[uint]*FinancialSummaryItem, timeColumns []string) (*FinancialSummaryItem, error) {
	operand = strings.TrimSpace(operand)

	// 添加调试信息
	fmt.Printf("调试：正在解析操作数: '%s'\n", operand)

	// 处理括号
	if strings.HasPrefix(operand, "(") && strings.HasSuffix(operand, ")") {
		// 检查括号是否平衡
		if !sjp.isBalancedParentheses(operand) {
			return nil, fmt.Errorf("括号不平衡: %s", operand)
		}
		innerExpr := operand[1 : len(operand)-1]
		fmt.Printf("调试：处理括号表达式，内部表达式: '%s'\n", innerExpr)
		return sjp.calculateBasicExpression(innerExpr, dataMap, timeColumns)
	}

	// 1. 处理分类名称引用 [分类名称]
	if strings.HasPrefix(operand, "[") && strings.HasSuffix(operand, "]") {
		categoryName := operand[1 : len(operand)-1]

		// 查找对应的ID
		var categoryId uint
		found := false
		for _, config := range sjp.builder.configs {
			if config.CategoryName == categoryName {
				categoryId = config.Id
				found = true
				break
			}
		}

		if !found {
			return nil, fmt.Errorf("找不到分类: %s", categoryName)
		}

		// 从数据映射中获取数据
		if item, exists := dataMap[categoryId]; exists {
			return item, nil
		} else {
			return nil, fmt.Errorf("找不到分类数据: %s (ID: %d)", categoryName, categoryId)
		}
	}

	// 2. 处理ID引用 #数字
	if strings.HasPrefix(operand, "#") {
		idStr := operand[1:] // 去掉 # 号

		if id, err := strconv.ParseUint(idStr, 10, 32); err == nil {
			categoryId := uint(id)

			// 验证ID是否存在于配置中
			var categoryName string
			found := false
			for _, config := range sjp.builder.configs {
				if config.Id == categoryId {
					categoryName = config.CategoryName
					found = true
					break
				}
			}

			if !found {
				fmt.Printf("错误：找不到ID为 %d 的分类配置\n", categoryId)
				// 打印所有可用的配置ID
				fmt.Printf("可用的配置ID: ")
				for _, config := range sjp.builder.configs {
					fmt.Printf("%d(%s) ", config.Id, config.CategoryName)
				}
				fmt.Println()
				return nil, fmt.Errorf("找不到ID为 %d 的分类配置", categoryId)
			}

			fmt.Printf("调试：找到分类配置: %s (ID: %d)\n", categoryName, categoryId)

			// 从数据映射中获取数据
			if item, exists := dataMap[categoryId]; exists {
				fmt.Printf("调试：成功获取分类数据: %s (ID: %d)\n", categoryName, categoryId)
				return item, nil
			} else {
				fmt.Printf("错误：找不到分类数据: %s (ID: %d)\n", categoryName, categoryId)
				// 打印所有可用的数据ID
				fmt.Printf("可用的数据ID: ")
				for id := range dataMap {
					fmt.Printf("%d ", id)
				}
				fmt.Println()
				return nil, fmt.Errorf("找不到分类数据: %s (ID: %d)", categoryName, categoryId)
			}
		} else {
			fmt.Printf("错误：无效的ID格式: %s, 解析错误: %v\n", operand, err)
			return nil, fmt.Errorf("无效的ID格式: %s", operand)
		}
	}

	return nil, fmt.Errorf("无法解析操作数: %s", operand)
}

// performBinaryOperation 执行二元运算
func (sjp *SimpleJSONProcessor) performBinaryOperation(left, right *FinancialSummaryItem, operator string, timeColumns []string) (*FinancialSummaryItem, error) {
	result := &FinancialSummaryItem{
		MonthlyAmounts: make(map[string]jtypes.JPrice),
	}

	// 计算每个月的数据
	for _, month := range timeColumns {
		leftAmount := left.MonthlyAmounts[month]
		rightAmount := right.MonthlyAmounts[month]

		switch operator {
		case "+":
			result.MonthlyAmounts[month] = leftAmount + rightAmount
		case "-":
			result.MonthlyAmounts[month] = leftAmount - rightAmount
		case "*":
			result.MonthlyAmounts[month] = leftAmount * rightAmount
		case "/":
			if rightAmount != 0 {
				result.MonthlyAmounts[month] = leftAmount / rightAmount
			} else {
				result.MonthlyAmounts[month] = 0
			}
		default:
			return nil, fmt.Errorf("不支持的运算符: %s", operator)
		}
	}

	// 计算总计
	switch operator {
	case "+":
		result.TotalAmount = left.TotalAmount + right.TotalAmount
	case "-":
		result.TotalAmount = left.TotalAmount - right.TotalAmount
	case "*":
		result.TotalAmount = left.TotalAmount * right.TotalAmount
	case "/":
		if right.TotalAmount != 0 {
			result.TotalAmount = left.TotalAmount / right.TotalAmount
		} else {
			result.TotalAmount = 0
		}
	}

	return result, nil
}

// extractDependencies 提取依赖关系
func (sjp *SimpleJSONProcessor) extractDependencies(formulaStr string) []uint {
	// 尝试解析为JSON格式
	formulaJSON, err := sjp.builder.BuildFromJSON(formulaStr)
	if err != nil {
		// 如果不是JSON格式，直接从表达式中提取
		return sjp.extractDependenciesFromExpression(formulaStr)
	}

	// 从JSON中获取依赖关系
	return formulaJSON.GetReferencedIDs()
}

// extractDependenciesFromExpression 从表达式中提取依赖关系
func (sjp *SimpleJSONProcessor) extractDependenciesFromExpression(expression string) []uint {
	var dependencies []uint

	// 1. 匹配 [分类名称] 格式
	namePattern := regexp.MustCompile(`\[([^\]]+)\]`)
	nameMatches := namePattern.FindAllStringSubmatch(expression, -1)

	for _, match := range nameMatches {
		if len(match) >= 2 {
			categoryName := match[1]
			// 查找对应的ID
			for _, config := range sjp.builder.configs {
				if config.CategoryName == categoryName {
					dependencies = append(dependencies, config.Id)
					break
				}
			}
		}
	}

	// 2. 匹配 #数字 格式的ID引用
	idPattern := regexp.MustCompile(`#(\d+)`)
	idMatches := idPattern.FindAllStringSubmatch(expression, -1)

	for _, match := range idMatches {
		if len(match) >= 2 {
			if id, err := strconv.ParseUint(match[1], 10, 32); err == nil {
				dependencies = append(dependencies, uint(id))
			}
		}
	}

	return removeDuplicateUints(dependencies)
}

// CreateFormulaJSON 创建JSON格式的公式
func (sjp *SimpleJSONProcessor) CreateFormulaJSON(displayExpr, description string) (string, error) {
	// 使用构建器创建公式JSON
	formulaJSON, err := sjp.builder.BuildFromExpression(displayExpr, description)
	if err != nil {
		return "", err
	}

	// 转换为JSON字符串
	return formulaJSON.ToJSON()
}

// ValidateFormulaJSON 验证JSON格式的公式
func (sjp *SimpleJSONProcessor) ValidateFormulaJSON(formulaJSON string) []ValidationError {
	var errors []ValidationError

	if formulaJSON == "" {
		return errors
	}

	// 解析JSON
	formula, err := sjp.builder.BuildFromJSON(formulaJSON)
	if err != nil {
		errors = append(errors, ValidationError{
			Type:    "JSON_PARSE_ERROR",
			Message: fmt.Sprintf("JSON解析失败: %v", err),
			Field:   "calculation_formula",
		})
		return errors
	}

	// 验证表达式语法
	if formula.Expression != "" {
		if !sjp.isValidExpression(formula.Expression) {
			errors = append(errors, ValidationError{
				Type:    "SYNTAX_ERROR",
				Message: "表达式语法错误或引用的分类不存在",
				Field:   "calculation_formula",
			})
		}
	}

	return errors
}

// GetDisplayExpression 获取显示表达式
func (sjp *SimpleJSONProcessor) GetDisplayExpression(formulaJSON string) (string, error) {
	if formulaJSON == "" {
		return "", nil
	}

	formula, err := sjp.builder.BuildFromJSON(formulaJSON)
	if err != nil {
		return "", err
	}

	return formula.Expression, nil
}

// findOperatorOutsideParentheses 在括号外查找运算符
// 从右到左扫描，找到最后一个不在括号内的指定运算符
func (sjp *SimpleJSONProcessor) findOperatorOutsideParentheses(expression string, operators []string) int {
	parenLevel := 0

	// 从右到左扫描，确保正确的运算符优先级
	for i := len(expression) - 1; i >= 0; i-- {
		char := expression[i]

		// 处理括号层级
		if char == ')' {
			parenLevel++
		} else if char == '(' {
			parenLevel--
		} else if parenLevel == 0 {
			// 只在括号外查找运算符
			for _, op := range operators {
				if string(char) == op {
					return i
				}
			}
		}
	}

	return -1 // 未找到
}

// isBalancedParentheses 检查括号是否平衡
func (sjp *SimpleJSONProcessor) isBalancedParentheses(expression string) bool {
	level := 0
	for _, char := range expression {
		if char == '(' {
			level++
		} else if char == ')' {
			level--
			if level < 0 {
				return false
			}
		}
	}
	return level == 0
}

// 辅助函数
func removeDuplicateUints(slice []uint) []uint {
	seen := make(map[uint]bool)
	var result []uint

	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}
