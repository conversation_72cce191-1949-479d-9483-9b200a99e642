package test

import (
	"encoding/json"
	"testing"
	
	srv "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
)

// TestExpenseDetailsProcessing 测试费用明细处理
func TestExpenseDetailsProcessing(t *testing.T) {
	// 模拟实际的费用明细数据结构
	expenseDetailsJSON := `[
		[
			{
				"ext": null,
				"id": "widget16710060735460001",
				"name": "费用类型",
				"option": {
					"key": "ldx0r1uc-otiiug1095g-1",
					"text": "办公费用"
				},
				"type": "radioV2",
				"value": "办公费用"
			},
			{
				"ext": null,
				"id": "widget17455770530670001",
				"name": "增值税发票类型",
				"option": {
					"key": "m9wniaa7-cnls8f6jdzd-0",
					"text": "普通发票"
				},
				"type": "radioV2",
				"value": "普通发票"
			},
			{
				"ext": {
					"capitalValue": "壹万伍仟元整",
					"currency": "CNY",
					"currencyRange": ["CNY"],
					"maxValue": "",
					"minValue": ""
				},
				"id": "widget16710062249940001",
				"name": "金额",
				"type": "amount",
				"value": 15000
			}
		],
		[
			{
				"ext": null,
				"id": "widget16710060735460001",
				"name": "费用类型",
				"option": {
					"key": "ldx0r1uc-otiiug1095g-2",
					"text": "交通费用"
				},
				"type": "radioV2",
				"value": "交通费用"
			},
			{
				"ext": null,
				"id": "widget17455770530670001",
				"name": "增值税发票类型",
				"option": {
					"key": "m9wniaa7-cnls8f6jdzd-1",
					"text": "专用发票"
				},
				"type": "radioV2",
				"value": "专用发票"
			},
			{
				"ext": {
					"capitalValue": "伍仟元整",
					"currency": "CNY",
					"currencyRange": ["CNY"],
					"maxValue": "",
					"minValue": ""
				},
				"id": "widget16710062249940001",
				"name": "金额",
				"type": "amount",
				"value": 5000
			}
		]
	]`

	// 解析 JSON 数据
	var listData []interface{}
	err := json.Unmarshal([]byte(expenseDetailsJSON), &listData)
	if err != nil {
		t.Fatalf("解析 JSON 失败: %v", err)
	}

	// 创建字段配置
	fieldConfig := srv.FieldConfig{
		FieldListConfig: map[string]srv.FieldConfig{
			"expense_type": {
				SourcePath:  "widget16710060735460001.value",
				TargetField: "expense_type",
				DataType:    "string",
				Required:    true,
				Description: "费用类型",
			},
			"vat_invoice_type": {
				SourcePath:  "widget17455770530670001.value",
				TargetField: "vat_invoice_type",
				DataType:    "string",
				Required:    false,
				Description: "增值税发票类型",
			},
			"amount": {
				SourcePath:  "widget16710062249940001.value",
				TargetField: "amount",
				DataType:    "float",
				Required:    true,
				Description: "费用金额",
			},
			"amount_capital": {
				SourcePath:  "widget16710062249940001.ext.capitalValue",
				TargetField: "amount_capital",
				DataType:    "string",
				Required:    false,
				Description: "金额大写",
			},
		},
	}

	// 创建转换器
	transformer := &srv.ContractTransformer{}
	
	// 创建标准数据结构
	data := &srv.StandardContractData{}

	// 处理费用明细列表
	err = transformer.ProcessExpenseDetailsList(data, listData, fieldConfig)
	if err != nil {
		t.Fatalf("处理费用明细失败: %v", err)
	}

	// 验证结果
	if len(data.ExpenseDetails) != 2 {
		t.Errorf("期望 2 个费用明细，实际得到 %d 个", len(data.ExpenseDetails))
	}

	// 验证第一个明细
	if len(data.ExpenseDetails) > 0 {
		detail1 := data.ExpenseDetails[0]
		if detail1.ExpenseType != "办公费用" {
			t.Errorf("第一个明细费用类型错误，期望 '办公费用'，实际 '%s'", detail1.ExpenseType)
		}
		if detail1.VatInvoiceType != "普通发票" {
			t.Errorf("第一个明细发票类型错误，期望 '普通发票'，实际 '%s'", detail1.VatInvoiceType)
		}
		if detail1.Amount != 15000 {
			t.Errorf("第一个明细金额错误，期望 15000，实际 %f", detail1.Amount)
		}
		if detail1.AmountCapital != "壹万伍仟元整" {
			t.Errorf("第一个明细金额大写错误，期望 '壹万伍仟元整'，实际 '%s'", detail1.AmountCapital)
		}
	}

	// 验证第二个明细
	if len(data.ExpenseDetails) > 1 {
		detail2 := data.ExpenseDetails[1]
		if detail2.ExpenseType != "交通费用" {
			t.Errorf("第二个明细费用类型错误，期望 '交通费用'，实际 '%s'", detail2.ExpenseType)
		}
		if detail2.VatInvoiceType != "专用发票" {
			t.Errorf("第二个明细发票类型错误，期望 '专用发票'，实际 '%s'", detail2.VatInvoiceType)
		}
		if detail2.Amount != 5000 {
			t.Errorf("第二个明细金额错误，期望 5000，实际 %f", detail2.Amount)
		}
		if detail2.AmountCapital != "伍仟元整" {
			t.Errorf("第二个明细金额大写错误，期望 '伍仟元整'，实际 '%s'", detail2.AmountCapital)
		}
	}

	t.Logf("费用明细处理测试通过，成功处理 %d 个明细项", len(data.ExpenseDetails))
}

// TestExpenseDetailsWithMissingFields 测试缺少字段的情况
func TestExpenseDetailsWithMissingFields(t *testing.T) {
	// 模拟缺少某些字段的数据
	expenseDetailsJSON := `[
		[
			{
				"ext": null,
				"id": "widget16710060735460001",
				"name": "费用类型",
				"type": "radioV2",
				"value": "办公费用"
			}
		]
	]`

	var listData []interface{}
	err := json.Unmarshal([]byte(expenseDetailsJSON), &listData)
	if err != nil {
		t.Fatalf("解析 JSON 失败: %v", err)
	}

	fieldConfig := srv.FieldConfig{
		FieldListConfig: map[string]srv.FieldConfig{
			"expense_type": {
				SourcePath:  "widget16710060735460001.value",
				TargetField: "expense_type",
				DataType:    "string",
				Required:    true,
				Description: "费用类型",
			},
			"amount": {
				SourcePath:  "widget16710062249940001.value",
				TargetField: "amount",
				DataType:    "float",
				Required:    true,
				Description: "费用金额",
			},
		},
	}

	transformer := &srv.ContractTransformer{}
	data := &srv.StandardContractData{}

	// 处理费用明细列表，应该因为缺少必填字段而失败
	err = transformer.ProcessExpenseDetailsList(data, listData, fieldConfig)
	if err == nil {
		t.Error("期望处理失败（因为缺少必填字段），但实际成功了")
	}

	t.Logf("缺少必填字段测试通过，错误信息: %v", err)
}

// TestExpenseDetailsEmptyList 测试空列表的情况
func TestExpenseDetailsEmptyList(t *testing.T) {
	var listData []interface{}
	
	fieldConfig := srv.FieldConfig{
		FieldListConfig: map[string]srv.FieldConfig{},
	}

	transformer := &srv.ContractTransformer{}
	data := &srv.StandardContractData{}

	err := transformer.ProcessExpenseDetailsList(data, listData, fieldConfig)
	if err != nil {
		t.Errorf("处理空列表失败: %v", err)
	}

	if len(data.ExpenseDetails) != 0 {
		t.Errorf("期望 0 个费用明细，实际得到 %d 个", len(data.ExpenseDetails))
	}

	t.Log("空列表测试通过")
}
