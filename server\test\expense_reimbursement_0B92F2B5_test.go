package test

import (
	"testing"
	"gopkg.in/yaml.v3"
	"os"
	"path/filepath"
)

// ExpenseReimbursementConfig 费用报销配置结构
type ExpenseReimbursementConfig struct {
	ApprovalCode string `yaml:"approval_code"`
	ApprovalName string `yaml:"approval_name"`
	Description  string `yaml:"description"`
	FieldMappings map[string]FieldMapping `yaml:"field_mappings"`
	DefaultValues map[string]interface{} `yaml:"default_values"`
	RequiredFields []string `yaml:"required_fields"`
	ValidationRules []ValidationRule `yaml:"validation_rules"`
	FieldListProcessing FieldListProcessing `yaml:"field_list_processing"`
}

type FieldMapping struct {
	SourcePath      string                 `yaml:"source_path"`
	TargetField     string                 `yaml:"target_field"`
	DataType        string                 `yaml:"data_type"`
	Required        bool                   `yaml:"required"`
	Description     string                 `yaml:"description"`
	Transform       string                 `yaml:"transform,omitempty"`
	FieldListConfig map[string]FieldMapping `yaml:"field_list_config,omitempty"`
}

type ValidationRule struct {
	Field     string `yaml:"field"`
	Rule      string `yaml:"rule"`
	Parameter string `yaml:"parameter"`
	Message   string `yaml:"message"`
}

type FieldListProcessing struct {
	Enabled            bool        `yaml:"enabled"`
	SplitRecords       bool        `yaml:"split_records"`
	PreserveParentData bool        `yaml:"preserve_parent_data"`
	RecordPrefix       string      `yaml:"record_prefix"`
	SplitRules         []SplitRule `yaml:"split_rules"`
}

type SplitRule struct {
	SourceField        string   `yaml:"source_field"`
	TargetRecordType   string   `yaml:"target_record_type"`
	MergeParentFields  []string `yaml:"merge_parent_fields"`
}

// TestExpenseReimbursementConfigValidation 测试费用报销配置文件的有效性
func TestExpenseReimbursementConfigValidation(t *testing.T) {
	// 获取配置文件路径
	configPath := filepath.Join("..", "config", "contract_mappings", "expense_reimbursement_0B92F2B5.yaml")
	
	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		t.Fatalf("无法读取配置文件: %v", err)
	}
	
	// 解析YAML
	var configMap map[string]ExpenseReimbursementConfig
	err = yaml.Unmarshal(data, &configMap)
	if err != nil {
		t.Fatalf("YAML解析失败: %v", err)
	}
	
	// 验证配置存在
	approvalCode := "0B92F2B5-922F-4570-8A83-489E476FF811"
	config, exists := configMap[approvalCode]
	if !exists {
		t.Fatalf("配置文件中未找到审批代码: %s", approvalCode)
	}
	
	// 验证基本信息
	if config.ApprovalCode != approvalCode {
		t.Errorf("审批代码不匹配: 期望 %s, 实际 %s", approvalCode, config.ApprovalCode)
	}
	
	if config.ApprovalName == "" {
		t.Error("审批名称不能为空")
	}
	
	if config.Description == "" {
		t.Error("描述不能为空")
	}
	
	// 验证必填字段映射存在
	requiredMappings := []string{
		"application_number",
		"title", 
		"reimbursement_reason",
		"expense_department",
		"payment_company",
		"total_amount",
		"account_holder",
		"account_number",
		"bank_name",
		"initiator_user_id",
		"expense_details",
	}
	
	for _, field := range requiredMappings {
		if _, exists := config.FieldMappings[field]; !exists {
			t.Errorf("缺少必填字段映射: %s", field)
		}
	}
	
	// 验证费用明细字段配置
	expenseDetails, exists := config.FieldMappings["expense_details"]
	if !exists {
		t.Fatal("缺少费用明细字段配置")
	}
	
	if expenseDetails.DataType != "fieldList" {
		t.Errorf("费用明细字段类型错误: 期望 fieldList, 实际 %s", expenseDetails.DataType)
	}
	
	if expenseDetails.FieldListConfig == nil {
		t.Error("费用明细缺少子字段配置")
	} else {
		// 验证费用明细子字段
		expectedSubFields := []string{"expense_type", "vat_invoice_type", "amount", "amount_currency", "amount_capital"}
		for _, subField := range expectedSubFields {
			if _, exists := expenseDetails.FieldListConfig[subField]; !exists {
				t.Errorf("费用明细缺少子字段: %s", subField)
			}
		}
	}
	
	// 验证账户信息字段
	accountFields := []string{"account_holder", "account_number", "bank_name"}
	for _, field := range accountFields {
		if mapping, exists := config.FieldMappings[field]; exists {
			if mapping.SourcePath == "" {
				t.Errorf("字段 %s 缺少源路径配置", field)
			}
			if mapping.TargetField == "" {
				t.Errorf("字段 %s 缺少目标字段配置", field)
			}
		}
	}
	
	// 验证验证规则
	if len(config.ValidationRules) == 0 {
		t.Error("缺少验证规则配置")
	}
	
	// 验证fieldList处理配置
	if !config.FieldListProcessing.Enabled {
		t.Error("fieldList处理未启用")
	}
	
	if len(config.FieldListProcessing.SplitRules) == 0 {
		t.Error("缺少fieldList拆分规则")
	}
	
	t.Logf("配置文件验证通过: %s", config.ApprovalName)
}

// TestExpenseReimbursementFieldMappings 测试字段映射的完整性
func TestExpenseReimbursementFieldMappings(t *testing.T) {
	configPath := filepath.Join("..", "config", "contract_mappings", "expense_reimbursement_0B92F2B5.yaml")
	
	data, err := os.ReadFile(configPath)
	if err != nil {
		t.Fatalf("无法读取配置文件: %v", err)
	}
	
	var configMap map[string]ExpenseReimbursementConfig
	err = yaml.Unmarshal(data, &configMap)
	if err != nil {
		t.Fatalf("YAML解析失败: %v", err)
	}
	
	config := configMap["0B92F2B5-922F-4570-8A83-489E476FF811"]
	
	// 验证每个字段映射的完整性
	for fieldName, mapping := range config.FieldMappings {
		if mapping.SourcePath == "" {
			t.Errorf("字段 %s 缺少源路径", fieldName)
		}
		
		if mapping.TargetField == "" {
			t.Errorf("字段 %s 缺少目标字段", fieldName)
		}
		
		if mapping.DataType == "" {
			t.Errorf("字段 %s 缺少数据类型", fieldName)
		}
		
		if mapping.Description == "" {
			t.Errorf("字段 %s 缺少描述", fieldName)
		}
		
		// 验证数据类型的有效性
		validDataTypes := []string{"string", "float", "int", "time", "array", "fieldList", "boolean"}
		isValidType := false
		for _, validType := range validDataTypes {
			if mapping.DataType == validType {
				isValidType = true
				break
			}
		}
		if !isValidType {
			t.Errorf("字段 %s 的数据类型无效: %s", fieldName, mapping.DataType)
		}
	}
	
	t.Logf("字段映射验证通过，共 %d 个字段", len(config.FieldMappings))
}
