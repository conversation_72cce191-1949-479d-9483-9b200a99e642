package test

import (
	"testing"
	"time"
	srv "github.com/flipped-aurora/gin-vue-admin/server/service/insbuy"
)

// TestExportToExcelWithExpenseDetails 测试费用报销数据的Excel导出
func TestExportToExcelWithExpenseDetails(t *testing.T) {
	// 创建测试数据 - 费用报销
	expenseData := &srv.StandardContractData{
		ApplicationNumber: "ER202501010001",
		Title:             "差旅费报销申请",
		ApplicationStatus: "已完成",
		InitiateTime:      time.Now().Add(-24 * time.Hour),
		CompleteTime:      time.Now(),
		InitiatorEmployeeId: "EMP001",
		InitiatorUserId:     "USER001",
		InitiatorName:       "张三",
		InitiatorDepartmentId: "DEPT001",
		
		// 费用报销特有字段
		ReimbursementReason: "北京出差费用报销",
		ReimbursementEntity: "北京分公司",
		TotalAmount:         2500.00,
		BusinessType:        "费用报销",
		
		// 银行账户信息
		AccountHolder: "张三",
		AccountNumber: "6222021234567890123",
		AccountType:   "储蓄卡",
		BankName:      "中国工商银行",
		BankBranch:    "北京朝阳支行",
		BankRegion:    "北京市朝阳区",
		
		ExpectedPaymentDate: time.Now().Add(7 * 24 * time.Hour),
		Remarks:             "差旅费报销，包含交通费和住宿费",
		
		// 费用明细
		ExpenseDetails: []srv.ExpenseDetailItem{
			{
				ExpenseType:    "交通费",
				Location:       "北京",
				DateRange:      "2025-01-01至2025-01-03",
				StartDate:      time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local),
				EndDate:        time.Date(2025, 1, 3, 0, 0, 0, 0, time.Local),
				VatInvoiceType: "普通发票",
				Amount:         800.00,
				AmountCurrency: "人民币",
				AmountCapital:  "捌佰元整",
				RowIndex:       0,
			},
			{
				ExpenseType:    "住宿费",
				Location:       "北京",
				DateRange:      "2025-01-01至2025-01-03",
				StartDate:      time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local),
				EndDate:        time.Date(2025, 1, 3, 0, 0, 0, 0, time.Local),
				VatInvoiceType: "专用发票",
				Amount:         1200.00,
				AmountCurrency: "人民币",
				AmountCapital:  "壹仟贰佰元整",
				RowIndex:       1,
			},
			{
				ExpenseType:    "餐费",
				Location:       "北京",
				DateRange:      "2025-01-01至2025-01-03",
				StartDate:      time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local),
				EndDate:        time.Date(2025, 1, 3, 0, 0, 0, 0, time.Local),
				VatInvoiceType: "普通发票",
				Amount:         500.00,
				AmountCurrency: "人民币",
				AmountCapital:  "伍佰元整",
				RowIndex:       2,
			},
		},
		
		// 附件信息
		Attachments: []srv.AttachmentInfo{
			{
				FileName: "交通费发票.pdf",
				FileURL:  "https://example.com/files/traffic_invoice.pdf",
			},
			{
				FileName: "住宿费发票.pdf",
				FileURL:  "https://example.com/files/hotel_invoice.pdf",
			},
		},
		
		// 元数据
		SourceInstanceCode: "0B92F2B5-922F-4570-8A83-489E476FF811",
		ApprovalCode:       "0B92F2B5-922F-4570-8A83-489E476FF811",
		TransformTime:      time.Now(),
		DataVersion:        "1.0",
	}
	
	// 创建测试器（这里需要根据实际的测试器结构进行调整）
	// tester := NewContractTransformerTester()
	
	// 导出到Excel
	filename := "test_expense_reimbursement_export.xlsx"
	// err := tester.ExportToExcel(expenseData, filename)
	// if err != nil {
	//     t.Fatalf("导出Excel失败: %v", err)
	// }
	
	t.Logf("费用报销数据导出测试准备完成")
	t.Logf("预期输出文件: %s", filename)
	t.Logf("费用明细数量: %d", len(expenseData.ExpenseDetails))
	t.Logf("预期Excel行数: %d (表头1行 + 明细%d行)", 1+len(expenseData.ExpenseDetails), len(expenseData.ExpenseDetails))
}

// TestExportToExcelWithPaymentApplication 测试付款申请数据的Excel导出
func TestExportToExcelWithPaymentApplication(t *testing.T) {
	// 创建测试数据 - 付款申请
	paymentData := &srv.StandardContractData{
		ApplicationNumber: "PA202501010001",
		Title:             "设备采购付款申请",
		ApplicationStatus: "已完成",
		InitiateTime:      time.Now().Add(-24 * time.Hour),
		CompleteTime:      time.Now(),
		InitiatorEmployeeId: "EMP002",
		InitiatorUserId:     "USER002",
		InitiatorName:       "李四",
		InitiatorDepartmentId: "DEPT002",
		
		// 付款申请特有字段
		PaymentReason:        "设备采购款项支付",
		PaymentEntity:        "ABC科技有限公司",
		BusinessType:         "付款申请",
		PaymentCurrency:      "人民币",
		ContractSignAmount:   100000.00,
		ContractPaidAmount:   50000.00,
		CurrentRequestAmount: 50000.00,
		TaxRate:              0.13,
		VatInvoiceType:       "专用发票",
		
		// 银行账户信息
		AccountHolder: "ABC科技有限公司",
		AccountNumber: "1234567890123456789",
		AccountType:   "对公账户",
		BankName:      "中国建设银行",
		BankBranch:    "上海浦东支行",
		BankRegion:    "上海市浦东新区",
		
		ExpectedPaymentDate: time.Now().Add(3 * 24 * time.Hour),
		Remarks:             "设备采购合同尾款支付",
		
		// 付款申请没有费用明细
		ExpenseDetails: []srv.ExpenseDetailItem{},
		
		// 附件信息
		Attachments: []srv.AttachmentInfo{
			{
				FileName: "采购合同.pdf",
				FileURL:  "https://example.com/files/purchase_contract.pdf",
			},
		},
		
		// 元数据
		SourceInstanceCode: "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
		ApprovalCode:       "F523F053-7AC6-4280-A4E7-B35E0C0431B5",
		TransformTime:      time.Now(),
		DataVersion:        "1.0",
	}
	
	// 创建测试器（这里需要根据实际的测试器结构进行调整）
	// tester := NewContractTransformerTester()
	
	// 导出到Excel
	filename := "test_payment_application_export.xlsx"
	// err := tester.ExportToExcel(paymentData, filename)
	// if err != nil {
	//     t.Fatalf("导出Excel失败: %v", err)
	// }
	
	t.Logf("付款申请数据导出测试准备完成")
	t.Logf("预期输出文件: %s", filename)
	t.Logf("费用明细数量: %d", len(paymentData.ExpenseDetails))
	t.Logf("预期Excel行数: %d (表头1行 + 数据1行)", 2)
}

// TestUnifiedExcelHeaders 测试统一的Excel表头
func TestUnifiedExcelHeaders(t *testing.T) {
	expectedHeaders := []string{
		"申请编号", "标题", "申请状态", "发起时间", "完成时间",
		"发起人工号", "发起人用户ID", "发起人姓名", "发起人部门ID",
		"付款事由", "付款主体", "报销事由", "报销主体", "业务类型", "付款币种",
		"合同签约金额", "合同已付金额", "本次请款金额", "费用总金额", "税率", "增值税发票类型",
		"收款方户名", "账户号码", "账户类型", "银行名称", "银行支行", "银行地区",
		"期望付款日期", "备注", "附件数量",
		// 费用明细字段
		"明细序号", "费用类型", "地点", "日期区间", "开始日期", "结束日期", 
		"明细增值税发票类型", "明细金额", "金额币种", "金额大写",
		"源实例代码", "审批代码", "转换时间", "数据版本",
	}
	
	t.Logf("统一表头字段数量: %d", len(expectedHeaders))
	t.Logf("表头字段列表:")
	for i, header := range expectedHeaders {
		t.Logf("  %d. %s", i+1, header)
	}
	
	// 验证表头的完整性
	requiredFields := map[string]bool{
		"申请编号": false, "标题": false, "申请状态": false,
		"付款事由": false, "报销事由": false, "业务类型": false,
		"费用总金额": false, "本次请款金额": false,
		"明细序号": false, "费用类型": false, "明细金额": false,
	}
	
	for _, header := range expectedHeaders {
		if _, exists := requiredFields[header]; exists {
			requiredFields[header] = true
		}
	}
	
	for field, found := range requiredFields {
		if !found {
			t.Errorf("缺少必要字段: %s", field)
		}
	}
	
	t.Logf("表头验证完成")
}
