package utils

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcore "github.com/larksuite/oapi-sdk-go/v3/core"
	larkapproval "github.com/larksuite/oapi-sdk-go/v3/service/approval/v4"
	"go.uber.org/zap"
)

// FeishuClient 飞书客户端封装
type FeishuClient struct {
	client *lark.Client
	appId  string
	secret string
}

// NewFeishuClient 创建飞书客户端
func NewFeishuClient() *FeishuClient {
	app := global.GVA_CONFIG.FeishuApp
	client := lark.NewClient(app.Appid, app.AppSecret)

	return &FeishuClient{
		client: client,
		appId:  app.Appid,
		secret: app.AppSecret,
	}
}

// ContractListRequest 合同列表请求参数
type ContractListRequest struct {
	ApprovalCode string    // 审批定义Code
	StartTime    time.Time // 开始时间
	EndTime      time.Time // 结束时间
	PageSize     int       // 页面大小
	PageToken    string    // 分页标记
}

// ContractListResponse 合同列表响应
type ContractListResponse struct {
	HasMore          bool     `json:"has_more"`
	InstanceCodeList []string `json:"instance_code_list"`
	PageToken        string   `json:"page_token"`
}

// GetContractList 获取合同列表
func (fc *FeishuClient) GetContractList(ctx context.Context, req ContractListRequest) (*ContractListResponse, error) {
	global.GVA_LOG.Info("开始获取飞书合同列表",
		zap.String("approval_code", req.ApprovalCode),
		zap.Time("start_time", req.StartTime),
		zap.Time("end_time", req.EndTime),
		zap.Int("page_size", req.PageSize),
	)

	// 设置默认值
	if req.PageSize <= 0 {
		req.PageSize = 100
	}
	if req.PageSize > 200 {
		req.PageSize = 200 // 飞书API限制
	}

	// 构建请求
	builder := larkapproval.NewListInstanceReqBuilder().
		ApprovalCode(req.ApprovalCode).
		PageSize(req.PageSize)

	// 时间转换为毫秒时间戳
	if !req.StartTime.IsZero() {
		startTimeMs := req.StartTime.UnixMilli()
		builder = builder.StartTime(strconv.FormatInt(startTimeMs, 10))
	}

	if !req.EndTime.IsZero() {
		endTimeMs := req.EndTime.UnixMilli()
		builder = builder.EndTime(strconv.FormatInt(endTimeMs, 10))
	}

	if req.PageToken != "" {
		builder = builder.PageToken(req.PageToken)
	}

	request := builder.Build()

	// 发起请求
	resp, err := fc.client.Approval.V4.Instance.List(ctx, request)
	if err != nil {
		global.GVA_LOG.Error("获取飞书合同列表失败", zap.Error(err))
		return nil, fmt.Errorf("调用飞书API失败: %w", err)
	}

	// 检查响应状态
	if !resp.Success() {
		errMsg := fmt.Sprintf("飞书API返回错误: %s", larkcore.Prettify(resp.CodeError))
		global.GVA_LOG.Error("飞书API响应错误",
			zap.String("request_id", resp.RequestId()),
			zap.String("error", errMsg),
		)
		return nil, fmt.Errorf(errMsg)
	}

	// 解析响应数据
	result := &ContractListResponse{
		HasMore:          *resp.Data.HasMore,
		InstanceCodeList: resp.Data.InstanceCodeList,
		PageToken:        *resp.Data.PageToken,
	}

	global.GVA_LOG.Info("成功获取飞书合同列表",
		zap.Int("count", len(result.InstanceCodeList)),
		zap.Bool("has_more", result.HasMore),
		zap.String("page_token", result.PageToken),
	)

	return result, nil
}

// ContractDetail 合同详情结构
type ContractDetail struct {
	ApprovalCode string             `json:"approval_code"`
	ApprovalName string             `json:"approval_name"`
	CommentList  []ContractComment  `json:"comment_list"`
	DepartmentId string             `json:"department_id"`
	EndTime      string             `json:"end_time"`
	Form         string             `json:"form"`
	InstanceCode string             `json:"instance_code"`
	OpenId       string             `json:"open_id"`
	Reverted     bool               `json:"reverted"`
	SerialNumber string             `json:"serial_number"`
	StartTime    string             `json:"start_time"`
	Status       string             `json:"status"`
	TaskList     []ContractTask     `json:"task_list"`
	Timeline     []ContractTimeline `json:"timeline"`
	UserId       string             `json:"user_id"`
	Uuid         string             `json:"uuid"`
}

// ContractComment 合同评论
type ContractComment struct {
	Comment    string         `json:"comment"`
	CreateTime string         `json:"create_time"`
	Id         string         `json:"id"`
	OpenId     string         `json:"open_id"`
	UserId     string         `json:"user_id"`
	Files      []ContractFile `json:"files,omitempty"`
}

// ContractFile 合同文件
type ContractFile struct {
	FileSize int    `json:"file_size"`
	Title    string `json:"title"`
	Type     string `json:"type"`
	Url      string `json:"url"`
}

// ContractTask 合同任务
type ContractTask struct {
	EndTime   string `json:"end_time"`
	Id        string `json:"id"`
	NodeId    string `json:"node_id"`
	NodeName  string `json:"node_name"`
	OpenId    string `json:"open_id"`
	StartTime string `json:"start_time"`
	Status    string `json:"status"`
	Type      string `json:"type"`
	UserId    string `json:"user_id"`
}

// ContractTimeline 合同时间线
type ContractTimeline struct {
	CreateTime string           `json:"create_time"`
	Ext        string           `json:"ext"`
	NodeKey    string           `json:"node_key"`
	OpenId     string           `json:"open_id"`
	Type       string           `json:"type"`
	UserId     string           `json:"user_id,omitempty"`
	CcUserList []ContractCcUser `json:"cc_user_list,omitempty"`
	OpenIdList []string         `json:"open_id_list,omitempty"`
	UserIdList []string         `json:"user_id_list,omitempty"`
	TaskId     string           `json:"task_id,omitempty"`
}

// ContractCcUser 合同抄送用户
type ContractCcUser struct {
	CcId   string `json:"cc_id"`
	OpenId string `json:"open_id"`
	UserId string `json:"user_id"`
}

// GetContractDetail 获取合同详情
func (fc *FeishuClient) GetContractDetail(ctx context.Context, instanceCode string) (*ContractDetail, error) {
	global.GVA_LOG.Info("开始获取飞书合同详情", zap.String("instance_code", instanceCode))

	// 构建请求
	req := larkapproval.NewGetInstanceReqBuilder().
		InstanceId(instanceCode).
		Build()

	// 发起请求
	resp, err := fc.client.Approval.V4.Instance.Get(ctx, req)
	if err != nil {
		global.GVA_LOG.Error("获取飞书合同详情失败",
			zap.String("instance_code", instanceCode),
			zap.Error(err),
		)
		return nil, fmt.Errorf("调用飞书API失败: %w", err)
	}

	// 检查响应状态
	if !resp.Success() {
		errMsg := fmt.Sprintf("飞书API返回错误: %s", larkcore.Prettify(resp.CodeError))
		global.GVA_LOG.Error("飞书API响应错误",
			zap.String("instance_code", instanceCode),
			zap.String("request_id", resp.RequestId()),
			zap.String("error", errMsg),
		)
		return nil, fmt.Errorf(errMsg)
	}

	// 转换响应数据
	data := resp.Data
	result := &ContractDetail{
		ApprovalCode: *data.ApprovalCode,
		ApprovalName: *data.ApprovalName,
		DepartmentId: *data.DepartmentId,
		EndTime:      *data.EndTime,
		Form:         *data.Form,
		InstanceCode: *data.InstanceCode,
		OpenId:       *data.OpenId,
		Reverted:     *data.Reverted,
		SerialNumber: *data.SerialNumber,
		StartTime:    *data.StartTime,
		Status:       *data.Status,
		UserId:       *data.UserId,
		Uuid:         *data.Uuid,
	}

	// 转换评论列表
	if data.CommentList != nil {
		for _, comment := range data.CommentList {
			c := ContractComment{
				Comment:    *comment.Comment,
				CreateTime: *comment.CreateTime,
				Id:         *comment.Id,
				OpenId:     *comment.OpenId,
				UserId:     *comment.UserId,
			}

			// 转换文件列表
			if comment.Files != nil {
				for _, file := range comment.Files {
					f := ContractFile{
						FileSize: *file.FileSize,
						Title:    *file.Title,
						Type:     *file.Type,
						Url:      *file.Url,
					}
					c.Files = append(c.Files, f)
				}
			}

			result.CommentList = append(result.CommentList, c)
		}
	}

	// 转换任务列表
	if data.TaskList != nil {
		for _, task := range data.TaskList {
			t := ContractTask{
				EndTime:   *task.EndTime,
				Id:        *task.Id,
				NodeId:    *task.NodeId,
				NodeName:  *task.NodeName,
				OpenId:    *task.OpenId,
				StartTime: *task.StartTime,
				Status:    *task.Status,
				Type:      *task.Type,
				UserId:    *task.UserId,
			}
			result.TaskList = append(result.TaskList, t)
		}
	}

	// 转换时间线
	if data.Timeline != nil {
		for _, timeline := range data.Timeline {
			t := ContractTimeline{
				CreateTime: *timeline.CreateTime,
				Ext:        *timeline.Ext,
				NodeKey:    *timeline.NodeKey,
				OpenId:     *timeline.OpenId,
				Type:       *timeline.Type,
			}

			if timeline.UserId != nil {
				t.UserId = *timeline.UserId
			}
			if timeline.TaskId != nil {
				t.TaskId = *timeline.TaskId
			}

			// 转换抄送用户列表
			if timeline.CcUserList != nil {
				for _, ccUser := range timeline.CcUserList {
					cc := ContractCcUser{
						CcId:   *ccUser.CcId,
						OpenId: *ccUser.OpenId,
						UserId: *ccUser.UserId,
					}
					t.CcUserList = append(t.CcUserList, cc)
				}
			}

			// 转换OpenId列表
			if timeline.OpenIdList != nil {
				t.OpenIdList = timeline.OpenIdList
			}

			// 转换UserId列表
			if timeline.UserIdList != nil {
				t.UserIdList = timeline.UserIdList
			}

			result.Timeline = append(result.Timeline, t)
		}
	}

	global.GVA_LOG.Info("成功获取飞书合同详情",
		zap.String("instance_code", instanceCode),
		zap.String("approval_name", result.ApprovalName),
		zap.String("status", result.Status),
		zap.Int("comments_count", len(result.CommentList)),
		zap.Int("tasks_count", len(result.TaskList)),
		zap.Int("timeline_count", len(result.Timeline)),
	)

	return result, nil
}

// BatchGetContractDetails 批量获取合同详情
func (fc *FeishuClient) BatchGetContractDetails(ctx context.Context, instanceCodes []string) ([]*ContractDetail, []error) {
	var results []*ContractDetail
	var errors []error

	global.GVA_LOG.Info("开始批量获取飞书合同详情", zap.Int("count", len(instanceCodes)))

	for _, code := range instanceCodes {
		detail, err := fc.GetContractDetail(ctx, code)
		if err != nil {
			global.GVA_LOG.Error("获取合同详情失败",
				zap.String("instance_code", code),
				zap.Error(err),
			)
			errors = append(errors, fmt.Errorf("获取合同 %s 详情失败: %w", code, err))
			continue
		}
		results = append(results, detail)

		// 添加延迟避免API限流
		time.Sleep(100 * time.Millisecond)
	}

	global.GVA_LOG.Info("批量获取飞书合同详情完成",
		zap.Int("success_count", len(results)),
		zap.Int("error_count", len(errors)),
	)

	return results, errors
}

// ValidateConfig 验证飞书配置
func (fc *FeishuClient) ValidateConfig() error {
	if fc.appId == "" {
		return fmt.Errorf("飞书应用ID未配置")
	}
	if fc.secret == "" {
		return fmt.Errorf("飞书应用密钥未配置")
	}
	return nil
}
